from flask import request
from flask_restful import Resource,reqparse
from common.utils.logger import logger
from common.utils.dict_parse import DictParseType
from core.data.response import Response
from core.databases.orm.exam.exam_manage_orm import DBExamPlatformAdmin
from datetime import datetime, timedelta
from bson import ObjectId
from flask_restful.reqparse import Argument
from pymongo import MongoClient
from config import Config
from pymongo.errors import OperationFailure
from core.databases.orm.user.organzation_orm import DBCoursePlatformStuOrg
from core.databases.orm.course.course_orm import CourseInfo
from core.auth.auth import *
from web.api.course.innerapi import _get_semester_courses_form_sid
from core.databases.orm.course.course_orm import _Course_Teacher
import json

def _get_exam_info_from_sid(sid:str) -> List[dict]:
    course_id=request.args.get('courseId',type=str)
    course_info=_get_semester_courses_form_sid(sid)
    course_list=[]
    if course_id is not None:
        course_list.append(ObjectId(course_id))
    else:
        for i in range(len(course_info)):
            course_list.append(ObjectId(course_info[i]['course_id']))
    result={
        "total":0,
        "done":0,
        "todo":0,
        "todoList":[]
    }
    res=DBExamPlatformAdmin.query_exam_list({'course_id':{"$in":course_list}},{})
    current_time=datetime.now()
    for item in res:
        result['total']+=1
        if item["end_time"]>current_time and item['is_delete']==False:
            result['todo']+=1
            item['course_id']=str(item['course_id'])
            for i in range(len(course_info)):
                if item['course_id']==str(course_info[i]['course_id']):
                    item['course_name']=course_info[i]['course_name']
                    item['course_type']=course_info[i]['course_type']
            item['exam_id']=str(item['_id'])
            item.pop('create_user')
            item.pop('create_time')
            item.pop('update_user')
            item.pop('update_time')
            item['begin_time']=item['begin_time'].strftime(date_format)
            item['end_time']=item['end_time'].strftime(date_format)
            item.pop('_id')
            result['todoList'].append(item)
        else:
            result['done']+=1
    return result

# def _get_exam_info_from_tid(tid:str) -> List[dict]:
    