'''
-*- coding:utf-8 -*-
@Project ：guan-ji 
@File    ：ilab_util.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2024/1/23 13:06 
@desc    ：用于和ILAB的交互工具
'''
import os
import subprocess
from urllib import parse

from common.utils.logger import logger
from core.databases.orm.user.ilab_config_orm import IlabConfigOrm
from urllib.parse import quote, unquote
import urllib3
import hashlib
import json

class IlabUtils:

    def __init__(self):
        self.dbIlabConfig = IlabConfigOrm()
        ilab_config = self.dbIlabConfig.find_one()
        if not ilab_config:
            logger.error("缺少ILAB连接配置项")
            raise ValueError("ILAB没有找到配置参数")
        self.status = ilab_config["status"]
        logger.info("当前连接是【{}】".format(self.status))
        self.appid = ilab_config[self.status]["APPID"]
        self.secret = ilab_config[self.status]["SECRET"]
        self.api_path = ilab_config[self.status]["API_PATH"]
        logger.info("ilab地址是：{}".format(self.api_path))
        self.http = urllib3.PoolManager()
        self.current_running_count = ilab_config[self.status].get("RUNNING_COUNT",50)
        logger.info("当前系统支持的虚拟实例并发数：{}".format(self.current_running_count))
        self.ilab_course_id = ilab_config.get("COURSE_ID", "65af3e91b00b49151f6eed01")
        self.ilab_class_id = ilab_config.get("CLASS_ID", "65af3670b00b49151f6eecff")
        logger.info("开放给ilab用户的课程：{}和班级：{}".format(self.ilab_course_id, self.ilab_class_id))
    def authentication(self,ticket):
        '''
        获取用户信息和access_token
        access_token是接口调用凭据，调用各接口时都需使用access_token。
        开发者需要进行妥善保存，access_token的有效期目前为24个小时，过期需要重新获取。
        '''
        text = unquote(ticket) + self.appid + self.secret
        hl = hashlib.md5()
        hl.update(text.encode(encoding='utf8'))
        signature = hl.hexdigest().upper()

        # 此处是在url中使用TICKET，需要使用url编码后的ticket
        path = self.api_path + '/open/api/v2/token?ticket=' + ticket + '&appid=' + self.appid + '&signature=' + signature
        r = self.http.request("GET", path)
        return json.loads(r.data.decode('utf-8'))
    def send_experimental_data(self,data,access_token):
        '''
        实验结果回传数据接口适用于“实验空间”用户完成实验教学项目后，
        产生的实验结果数据回传到“实验空间”
        且可以分步骤上传
        '''
        # 在url中使用，需要进行url编码
        path = self.api_path + '/open/api/v2/data_upload?access_token=' + quote(access_token)
        encoded_data = json.dumps(data).encode('utf-8')
        r = self.http.request("POST", path, body=encoded_data)
        return r.data.decode('utf-8')
    def send_experimental_report(self, file_path, access_token, title, oriid, file_name):
        '''
        实验空间登录用户进入学校实验平台完成实验后，通过实验报告上传接口将学校实验平台生成的实验报告回传到实验空间
        如果有实验报告，则先调用数据回传接口，然后再调用实验报告上传接口
        '''
        f_file = open(file_path, 'rb')
        file_content = f_file.read()
        headers = {'Content-Type': 'application/octet-stream',
                   "User-Agent": "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/31.0.1650.63 Safari/537.36"
                   }
        f_file.close()
        if len(file_content) > 5 * 1024 * 1024:
            logger.info("文件大于5MB")
            return {"code": 8, "msg": '数据错误，未传输数据或者数据大于5Mb', "status": '数据错误，未传输数据或者数据大于5Mb'}
        appid = self.appid
        originId = oriid
        remarks = "报告上传"
        post_url = self.api_path + "/open/api/v2/attachment_upload?" + parse.urlencode(
            {"access_token": access_token, "appid": appid, "originId": originId, "filename": file_name, "title": title,
             "remarks": remarks})

        # Create a fields dictionary with the file data
        fields = {'file': ('file_to_upload.txt', file_content)}
        # Make a POST request with the file as part of the body
        logger.info("开始上传实验报告")
        logger.info("上传文件参数：【{}】".format(post_url))
        response = self.http.request('POST', post_url, fields=fields)
        result = json.loads(response.data.decode('utf-8'))
        logger.info("上传结果：{}".format(str(result)))
        return result
    def get_experimental_data(self,access_token):
        '''
        获取实验结果接口适用于“实验空间”用户完成实验教学项目后，
        产生的实验结果数据回传到“实验空间”后，
        学校实验平台可对实验结果数据进行查询
        '''
        text = access_token + self.appid + self.secret
        hl = hashlib.md5()
        hl.update(text.encode(encoding='utf8'))
        signature = hl.hexdigest().upper()

        # 此处是在linux中使用linux命令访问url，需要进行url编码
        path = self.api_path + '/open/api/v2/data_get?access_token=' + quote(
            access_token) + '&appid=' + self.appid + '&signature=' + signature
        r = self.http.request("GET", path)
        return json.loads(r.data.decode('utf-8'))


    def re_authentication(self,access_token):
        '''
        access_token的有效期目前为24个小时，过期需要重新获取。如果实验时间超过24小时，
        请在学生完成实验后向实验空间返回实验成绩时，重新获取新的access_token。
        '''
        text = access_token + self.appid + self.secret
        hl = hashlib.md5()
        hl.update(text.encode(encoding='utf8'))
        signature = hl.hexdigest().upper()

        # 此处是在url中使用ACCESS_TOKEN 需要对ACCESS_TOKEN进行url编码
        path = self.api_path + '/open/api/v2/token/refresh?access_token=' + quote(
            access_token) + '&appid=' + self.appid + '&signature=' + signature
        r = self.http.request("GET", path)
        return json.loads(r.data.decode('utf-8'))

ilab_util = IlabUtils()
