'''
-*- coding:utf-8 -*-
@Project ：jxsjpt 
@File    ：stu_my_grade.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2024/1/11 16:36 
@desc    ：
'''
import time
import copy
from bson import ObjectId
from flask import request,session
from flask_restful import Resource, reqparse, inputs
from common.utils.logger import logger
from core.data.response import Response
from core.databases.orm.user.organzation_orm import *
from core.databases.orm.course.course_orm import *
from core.databases.orm.exam.exam_manage_orm import DBExamPlatformAdmin
from core.hwService.hworkStatisticsService import HwStatiSerivce
from web.api.course.course_admin import getClassName
from datetime import datetime, timedelta

from web.api.course.innerapi import _get_semester_courses_form_tid

# 日期格式
date_format = '%Y-%m-%dT%H:%M:%SZ'

'''
返回老师本学期所有课程已完成作业，考试分数的班级平均分，以及各个班级的提交情况
'''
class TeaHwGrade(Resource):

    # def get(self):
    #     data=[{
    #             "_id":"fasdf2345t3datg",#作业提交表id
    #             "hwName":"第一次作业",#作业名称
    #             "courseId":"346342teftgdsfghdfsh",#课程id
    #             "courseName": "理论课程",  # 课程名称
    #             "classId": "346342teftgdsfghdfsh",  # 班级id
    #             "className": "网安一班",  # 班级名称
    #             "handInfo": "90/100",  # 提交情况
    #             "enddate": "2023-10-14T15:16:31+00:00",  # 截止日期
    #             "averagedScore":93#平均分
    #         }]
    #     return Response.success(message='success',data=data)
    def __init__(self):
        self.businessService = HwStatiSerivce()
    def get(self):
        try:
            teaId = session.get("id")
            # teaId = '64585957c3f51bee352e35de'
            courseId = request.args.get('courseId', type=str)
            #获取本学期所选课程
            courseInfoall = _get_semester_courses_form_tid(teaId)
            if courseId is not None and courseId != '':  # 查询单个
                courseInfos = list(filter(lambda x:x["course_id"]==courseId,courseInfoall))
            else:# 查询所有
                courseInfos = courseInfoall
            data = self.businessService.teaHwStati(teaId,courseInfos)
            return Response.success(message='success',data=data)


        except Exception as e:
            logger.warning("获取数据异常: {}".format(e))
            logger.exception(e)
            return Response.failed(data="", message=e)
class TeaExamGrade(Resource):
    def get(self):
        try:
            tid=session.get("id")
            course_id=request.args.get('courseId',type=str)
            course_list=[]
            if course_id is not None and course_id != '':
                course_list.append(ObjectId(course_id))
            else:
                teacher_course,status=CourseTeacher.find(
                query={"teacher_id":tid},need={})
                if status==CourseStatusCode.FIND_SUCCESS:
                    for course in teacher_course:
                        course_list.append(ObjectId(course['course_id']))
            res=DBExamPlatformAdmin.query_exam_list({'course_id':{'$in':course_list},'is_delete':False},{})
            result=[]
            item_list=[]
            for item in res:
                item_list.append(item)
            for item in item_list:
                if len(item['class_id'])>1:
                    temp=copy.deepcopy(item)
                    temp['class_id']=item['class_id'][1:]
                    del item['class_id'][1:]
                    item_list.append(temp)
            for item in item_list:
                item['_id']=str(item['_id'])
                item['courseId']=str(item['course_id'])
                item['classId']=item['class_id'][0]
                item['examName']=item['exam_name']
                classinfo=getClassName(item['class_id'])
                if len(classinfo)>0:
                    item['className']=classinfo[0]['name']
                item.pop('exam_name')
                item.pop('course_id')
                item.pop('create_user')
                item.pop('create_time')
                item.pop('update_user')
                item.pop('update_time')
                item.pop('class_id')
                courseinfo,status=CourseInfo.find_by_id(item['courseId'],need={})
                if status==CourseStatusCode.FIND_SUCCESS:
                    item['courseName']=courseinfo['course_name']
                else:
                    item['courseName']='无数据'
                item['begindata']=item['begin_time'].strftime(date_format)
                item['enddata']=item['end_time'].strftime(date_format)
                item.pop('begin_time')
                item.pop('end_time')
                student_dict=DBCoursePlatformStuOrg.get_one({'_id':ObjectId(item['classId'])})
                student_list=[]
                if student_dict is not None:
                    for student_info in student_dict['students']:
                        student_list.append(student_info['id'])
                exam_list=DBExamPlatformAdmin.query_exam_record_student_list({'exam_id':item['_id'],'user_id':{'$in':student_list}},{})
                total=0
                enter=0
                correct=0
                totalScore=0
                for exam in exam_list:
                    total+=1
                    totalScore+=exam['score']
                    if exam['exam_state']:
                        enter+=1
                    if exam['correct_state']:
                        correct+=1
                item['enterInfo']=str(enter)+'/'+str(total)
                if correct != 0:
                    item['averagedScore']=totalScore/correct
                else:
                    item['averagedScore']=0
                result.append(item)
            return Response.success(data=result,message='')
        except Exception as e:
            logger.warning('query failed: {}'.format(e))
            logger.exception(e)
            return Response.failed(data='', message=e)
# class TeaExamGrade(Resource):

#     def get(self):
#         data=[{
#                 "_id": "fasdf2345t3datg",  # 考试id
#                 "examName": "第一次考试",  # 考试名称
#                 "courseId": "346342teftgdsfghdfsh",  # 课程id
#                 "courseName": "理论课程",  # 课程名称
#                 "classId": "346342teftgdsfghdfsh",  # 班级id
#                 "className": "网安一班",  # 班级名称
#                 "enterInfo": "99 / 100",  # 参与考试情况
#                 "enddate": "2023-11-14T15:16:31+00:00",  # 截止日期
#                 "averagedScore":90#平均分
#             },{
#                 "_id": "fasdf2345t3datg343ghjgu",  # 考试id
#                 "examName": "第二次考试",  # 考试名称
#                 "courseId": "346342teftgdsfghdfsh",  # 课程id
#                 "courseName": "理论课程",  # 课程名称
#                 "classId": "346342teftgdsfghdfsh",  # 班级id
#                 "className": "网安一班",  # 班级名称
#                 "enterInfo": "98 / 100",  # 参与考试情况
#                 "enddate": "2023-01-08T15:16:31+00:00",  # 截止日期
#                 "averagedScore":91#平均分
#             }]
#         return Response.success(message='success',data=data)