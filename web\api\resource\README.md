# 资源管理模块接口文档

阅读该接口文档前须知：

资源中心所有接口都是基于产品设计的最终版设计图来编写的

在设计图中，资源管理是在点进某个课程后，以左侧侧边栏的一个子模块呈现

因此，我们的资源管理是课程级别的

即请求接口时需要带上当前课程的course_id

前端所需接口应该主要为id为1~8、11的接口


| 接口id | 接口api                  | 请求方式 | 功能说明                       |
| ------ | ------------------------ | -------- | ------------------------------ |
| 1      | /api/v1/file             | POST     | 用户上传文件并指定文件相关信息 |
| 2      | /api/v1/file/<file_name> | GET      | 用户下载指定文件名的文件       |
| 3      | /api/v1/file/<id>        | DELETE   | 用户删除指定id的文件           |
| 4      | /api/v1/resource         | GET      | 按文件别名搜索资源信息         |
| 5      | /api/v1/resource         | GET      | 按文件类别筛选资源信息         |
| 6      | /api/v1/resource         | GET      | 按文件标签筛选资源信息         |
| 7      | /api/v1/resource         | GET      | 获取全部type信息               |
| 8      | /api/v1/resource         | GET      | 获取全部label信息              |
| 9      | /api/v1/resource         | PUT      | 资源信息入库                   |
| 10     | /api/v1/resource/<id>    | GET      | 获取指定id的信息               |
| 11     | /api/v1/resource/<id>    | PATCH    | 增量修改指定id的信息           |
| 12     | /api/v1/resource/<id>    | DELETE   | 删除指定id的信息               |

## 1、用户上传文件到upload文件夹

请求方法：POST

参数：


| 参数                 | 类型   | 参数说明                                   |
| -------------------- | ------ | ------------------------------------------ |
| myfile               | file   | 二进制文件流                               |
| resource_type        | str    | 文件类型                                   |
| resource_version     | str    | 文件版本                                   |
| resource_description | str    | 文件描述                                   |
| resource_name        | str    | 文件别名，必填                             |
| labels[]             | Arrays | 文件标签                                   |
| course_id            | str    | 绑定的课程id，(必填，前端从课程信息中获取) |

功能：上传文件并指定文件相关信息

示例：POST http://**************:50020/api/v1/file

请求数据类型：form-data

返回结果示例：

```json
{
    "status": {
        "status": "success",
        "code": 10200,
        "message": "添加课程资源成功！"
    },
    "result": "",
    "timestamp": 1682598908
}
```

## 2、用户下载指定文件名的文件

请求方法：GET

参数：


| 参数      | 类型 | 参数说明                               |
| --------- | ---- | -------------------------------------- |
| download  | str  | 若设置了，则代表下载，没设置则代表打开 |
| course_id | str  | 必填，指定绑定课程                     |

功能：获取 指定file_name(id+后缀名，在资源表中包含该字段)的文件

请求示例：

```
GET
http://{{url}}/api/v1/file/1b4e6a9cb8591dbfd63d785ea7ccc823.pdf?download=true&course_id=10001
```

返回结果：文件流

## 3、用户删除指定id的文件

请求方法：DELETE

参数：


| 参数      | 类型 | 参数说明         |
| --------- | ---- | ---------------- |
| file_name | str  | 必填，表示文件名 |
| course_id | str  | 必填，表示课程id |

功能：用户删除指定file_id的文件

请求示例：DELETE http://**************:50020/api/v1/file/1b4e6a9cb8591dbfd63d785ea7ccc823

请求格式：raw/json格式

```json
{
    "file_name": "1b4e6a9cb8591dbfd63d785ea7ccc823.pdf"
    "course_id": "10001"
}
```

返回结果示例：

```json
{
    "status": {
        "status": "success",
        "code": 10200,
        "message": "删除资源成功！"
    },
    "result": "",
    "timestamp": 1682599988
}
```

## 4、按文件别名搜索资源信息

请求方法：GET

参数：


| 参数          | 类型 | 参数说明                   |
| ------------- | ---- | -------------------------- |
| offset        | int  | 页码，从1开始，不传默认为1 |
| limit         | int  | 页大小，不传默认为20       |
| resource_name | str  | 资源名，必填               |
| course_id     | str  | 绑定课程id，必填           |

功能：通过资源名称进行模糊搜索

请求示例：

```
GET 
http://{{url}}/api/v1/resource?offset=1&limit=20&resource_name=学术&course_id=10002
```

返回结果示例：

```json
{
    "status": {
        "status": "success",
        "code": 10200,
        "message": "获取资源信息成功！"
    },
    "result": [
        {
            "id": "1b4e6a9cb8591dbfd63d785ea7ccc823",
            "resource_type": "1",
            "add_user": "test",
            "add_time": 1682593615,
            "modify_user": "test",
            "modify_time": 1682593615,
            "download_count": 0,
            "resource_size": 0,
            "resource_version": "v4.0",
            "resource_description": "nihao",
            "resource_name": "学术",
            "icon_id": "test",
            "course_id": "10002",
            "labels": [
                "你好"
            ],
            "url": "http://127.0.0.1:50020/api/v1/file/1b4e6a9cb8591dbfd63d785ea7ccc823.pdf",
            "file_name": "1b4e6a9cb8591dbfd63d785ea7ccc823.pdf"
        }
    ],
    "timestamp": 1683597620
}
```

## 5、按文件类别筛选资源信息

请求方法：GET

参数：


| 参数             | 类型 | 参数说明                   |
| ---------------- | ---- | -------------------------- |
| offset           | int  | 页码，从1开始，不传默认为1 |
| limit            | int  | 页大小，不传默认为20       |
| course_id        | str  | 绑定课程id，必填           |
| resource_type    | str  | 资源类别，必填             |
| screen_type_flag | str  | 采用类别分类，必填         |

功能：

通过提交的type（单个字符串）来获取某课程下指定类别的资源信息

请求示例：

```
GET
http://{{url}}/api/v1/resource?offset=1&limit=20&course_id=10001&screen_type_flag=1&resource_type=文档
```

返回结果示例：

```json
{
    "status": {
        "status": "success",
        "code": 10200,
        "message": "获取资源信息成功！"
    },
    "result": [
        {
            "id": "1b4e6a9cb8591dbfd63d785ea7ccc823",
            "resource_type": "文档",
            "add_user": "test",
            "add_time": "1682600030",
            "modify_user": "test",
            "modify_time": "1682600030",
            "download_count": 0,
            "resource_size": 0,
            "resource_version": "v4.0",
            "resource_description": "网安学院申请学位条件清单",
            "resource_name": "测试文件",
            "icon_id": "test",
            "course_id": "10001",
            "labels": [
                "毕业"
            ],
            "url": "http://*************:50020/api/v1/file/1b4e6a9cb8591dbfd63d785ea7ccc823.pdf",
            "file_name": "1b4e6a9cb8591dbfd63d785ea7ccc823.pdf"
        }
    ],
    "timestamp": 1683597966
}
```

## 6、按文件标签筛选资源信息

请求方法：GET

参数：


| 参数               | 类型  | 参数说明                   |
| ------------------ | ----- | -------------------------- |
| offset             | int   | 页码，从1开始，不传默认为1 |
| limit              | int   | 页大小，不传默认为20       |
| course_id          | str   | 绑定课程id，必填           |
| labels             | Array | 资源标签，必填             |
| screen_labels_flag | str   | 采用标签分类，必填         |

功能：

通过提交的label数组（单个或多个）来获取某课程下指定标签的资源信息

请求示例：

```
GET
http://{{url}}/api/v1/resource?offset=1&limit=20&course_id=10002&labels[]=你好&labels[]=书籍&screen_labels_flag=1
```

返回结果示例：

```json
{
    "status": {
        "status": "success",
        "code": 10200,
        "message": "获取资源信息成功！"
    },
    "result": [
        {
            "id": "0335dff574ca52fb88c1c7adc6587e1b",
            "resource_type": "1",
            "add_user": "test",
            "add_time": "1682064029",
            "modify_user": "test",
            "modify_time": "1682064029",
            "download_count": 0,
            "resource_size": 0,
            "resource_version": "v4.0",
            "resource_description": "mysql八股，必不可少",
            "resource_name": "MySQL必知必会",
            "icon_id": "test",
            "course_id": "10002",
            "labels": [
                "书籍",
                "testtest"
            ],
            "url": "http://127.0.0.1:50020/api/v1/file/0335dff574ca52fb88c1c7adc6587e1b.pdf",
            "file_name": "0335dff574ca52fb88c1c7adc6587e1b.pdf"
        },
        {
            "id": "1b4e6a9cb8591dbfd63d785ea7ccc823",
            "resource_type": "1",
            "add_user": "test",
            "add_time": 1682593615,
            "modify_user": "test",
            "modify_time": 1682593615,
            "download_count": 0,
            "resource_size": 0,
            "resource_version": "v4.0",
            "resource_description": "nihao",
            "resource_name": "学术",
            "icon_id": "test",
            "course_id": "10002",
            "labels": [
                "你好"
            ],
            "url": "http://127.0.0.1:50020/api/v1/file/1b4e6a9cb8591dbfd63d785ea7ccc823.pdf",
            "file_name": "1b4e6a9cb8591dbfd63d785ea7ccc823.pdf"
        }
    ],
    "timestamp": 1683598188
}
```

## 7、获取全部type信息

请求方法：GET

参数：


| 参数      | 类型 | 参数说明                   |
| --------- | ---- | -------------------------- |
| offset    | int  | 页码，从1开始，不传默认为1 |
| limit     | int  | 页大小，不传默认为20       |
| course_id | str  | 绑定课程id，必填           |
| getType   | str  | 表示获取全部type，必填     |

功能：

获得当前课程的全部resource_type

请求示例：

```
GET
http://{{url}}/api/v1/resource?offset=1&limit=20&course_id=10002&getType=1
```

返回结果示例：

```json
{
    "status": {
        "status": "success",
        "code": 10200,
        "message": "获取资源类型成功！"
    },
    "result": [
        "物联网安全",
        "网络安全"
    ],
    "timestamp": 1683598453
}
```

## 8、获取全部label信息

请求方法：GET

参数：


| 参数      | 类型 | 参数说明                   |
| --------- | ---- | -------------------------- |
| offset    | int  | 页码，从1开始，不传默认为1 |
| limit     | int  | 页大小，不传默认为20       |
| course_id | str  | 绑定课程id，必填           |
| getLabel  | str  | 表示获取全部labels，必填   |

功能：

获得当前课程的全部label

请求示例：

```
GET
http://{{url}}/api/v1/resource?offset=1&limit=20&course_id=10002&getLabel=true
```

返回结果示例：

```json
{
    "status": {
        "status": "success",
        "code": 10200,
        "message": "获取资源标签成功！"
    },
    "result": [
        "你好",
        "书籍",
        "testtest"
    ],
    "timestamp": 1683598297
}
```

## 9、资源信息入库

请求方法：PUT

参数：


| 参数                 | 类型      | 参数说明             |
| -------------------- | --------- | -------------------- |
| id                   | str       | 资源ID               |
| resource_type        | str       | 资源类型             |
| add_user             | str       | 添加人               |
| add_time             | timestamp | 添加时间             |
| modify_user          | str       | 修改人               |
| modify_time          | timestamp | 修改时间             |
| download_count       | int       | 下载次数             |
| resource_size        | int       | 文件大小             |
| resource_version     | str       | 文件版本             |
| resource_description | str       | 文件描述             |
| resource_name        | str       | 文件名称             |
| icon_id              | str       | 文件缩略图ID         |
| course_id            | str       | 课程id               |
| labels               | Arrays    | 文件标签             |
| url                  | str       | 资源对外下载URL      |
| file_name            | str       | 存储在服务器的文件名 |

功能：将资源信息添加进数据库。

请求示例：PUT http://**************:50020/api/v1/resource

请求示例：raw/json

```json
{
    "id": "1b4e6a9cb8591dbfd63d785ea7ccc823",
    "resource_type": "文档",
    "add_user": "test",
    "add_time": 1682600030,
    "modify_user": "test",
    "modify_time": 1682600030,
    "download_count": 0,
    "resource_size": 0,
    "resource_version": "v4.0",
    "resource_description": "网安学院申请学位条件清单",
    "resource_name": "网安学院申请学位条件清单",
    "icon_id": "test",
    "course_id": "10003",
    "labels": ["毕业", "资料"],
    "url": "http://*************:50020/api/v1/file/1b4e6a9cb8591dbfd63d785ea7ccc823.pdf",
    "file_name": "1b4e6a9cb8591dbfd63d785ea7ccc823.pdf"
}
```

返回结果示例：

```json
{
    "status": {
        "status": "success",
        "code": 10200,
        "message": "资源信息入库成功！"
    },
    "result": "",
    "timestamp": 1682602958
}
```

## 10、获取指定id的信息

请求方法：GET

参数：


| 参数      | 类型 | 参数说明         |
| --------- | ---- | ---------------- |
| course_id | str  | 资源绑定的课程id |

功能：通过课程id和资源id获取资源信息。

请求示例：GET http://**************:50020/api/v1/resource/1b4e6a9cb8591dbfd63d785ea7ccc823?course_id=10001

返回结果示例：

```json
{
    "status": {
        "status": "success",
        "code": 10200,
        "message": "获取资源信息成功！"
    },
    "result": {
        "id": "1b4e6a9cb8591dbfd63d785ea7ccc823",
        "resource_type": "文档",
        "add_user": "test",
        "add_time": 1682600030,
        "modify_user": "test",
        "modify_time": 1682600030,
        "download_count": 0,
        "resource_size": 0,
        "resource_version": "v4.0",
        "resource_description": "网安学院申请学位条件清单",
        "resource_name": "网安学院申请学位条件清单",
        "icon_id": "test",
        "course_id": "10001",
        "labels": ["毕业", "资料"],
        "url": "http://*************:50020/api/v1/file/1b4e6a9cb8591dbfd63d785ea7ccc823.pdf",
        "file_name": "1b4e6a9cb8591dbfd63d785ea7ccc823.pdf"
    },
    "timestamp": 1682603520
}
```

## 11、增量修改指定id的信息

请求方法：PATCH

参数：


| id                   | str       | 资源ID               |
| -------------------- | --------- | -------------------- |
| resource_type        | str       | 资源类型             |
| add_user             | str       | 添加人               |
| add_time             | timestamp | 添加时间             |
| modify_user          | str       | 修改人               |
| modify_time          | timestamp | 修改时间             |
| download_count       | int       | 下载次数             |
| resource_size        | int       | 文件大小             |
| resource_version     | str       | 文件版本             |
| resource_description | str       | 文件描述             |
| resource_name        | str       | 文件名称             |
| icon_id              | str       | 文件缩略图ID         |
| course_id            | str       | 课程id               |
| labels               | Arrays    | 文件标签             |
| url                  | str       | 资源对外下载URL      |
| file_name            | str       | 存储在服务器的文件名 |

功能：更新指定资源信息。

请求示例：PATCH http://**************:50020/api/v1/resource/1b4e6a9cb8591dbfd63d785ea7ccc823

格式 raw/json

```json
{
    "id": "1b4e6a9cb8591dbfd63d785ea7ccc823",
    "resource_type": "文档",
    "add_user": "test",
    "add_time": 1682600030,
    "modify_user": "test",
    "modify_time": 1682600030,
    "download_count": 0,
    "resource_size": 0,
    "resource_version": "v4.0",
    "resource_description": "网安学院申请学位条件清单",
    "resource_name": "测试文件",
    "icon_id": "test",
    "course_id": "10003",
    "labels": ["毕业"],
    "url": "http://*************:50020/api/v1/file/1b4e6a9cb8591dbfd63d785ea7ccc823.pdf",
    "file_name": "1b4e6a9cb8591dbfd63d785ea7ccc823.pdf"
}
```

返回结果示例：

```json
{
    "status": {
        "status": "success",
        "code": 10200,
        "message": "更新资源信息成功！"
    },
    "result": "",
    "timestamp": 1682603903
}
```

## 12、删除指定id的信息

请求方法：DELETE

参数：


| 参数      | 类型 | 参数说明   |
| --------- | ---- | ---------- |
| course_id | str  | 指定资源ID |
| id        | str  | uri中传入  |

功能：删除指定ID的资源信息。

请求示例：DELETE  http://**************:50020/api/v1/resource/1b4e6a9cb8591dbfd63d785ea7ccc823

格式 raw/json

```json
{
    "course_id": "10001"
}
```

返回结果示例：

```json
{
    "status": {
        "status": "success",
        "code": 10200,
        "message": "删除资源信息成功！"
    },
    "result": "",
    "timestamp": 1682604182
}
```


```
