#!/usr/bin/env python
# -*- coding: utf-8 -*- 
# @Time   : 5/18/21 5:48 AM 
# <AUTHOR> jackey 
# @File   : token.py
# @desc   : ""

from ast import List
from functools import wraps
from flask import redirect, request, session, url_for
from core.databases.orm.role.roles_orm import DBCoursePlatformRoles as h_role, Permission
from core.databases.orm.user.users_orm import DBCoursePlatformUsers as h_user
from web.flask_app import flask_app
from common.utils.logger import logger
from core.data.response import Response, StatusCode

#进行token检测,在函数执行前进行检测
# def auth(func):
#     @wraps(func)
#     def wrapper(*args, **kwargs):
#         if not flask_app.config.get("AUTH"):
#             session['user'] = "guest"
#             session['nick'] = "guest"
#             session['email'] = "<EMAIL>"
#             session['authority'] = 0
#             return func(*args, **kwargs)
#         token = request.headers.get('token')
#         if not token:
#             token = request.args.get('token')
#         if not token:
#             token = request.form.get('token')
#         if not token:
#             return Response.failed(StatusCode.AUTH_FAILED)  #如果没有获取到token,返回失败
#         try:
#             item = DBCoursePlatformUsers.token_check(token)  #检测token
#             if item:
#                 session['user'] = item['username']
#                 session['authority'] = item['role']
#                 session['nick'] = item['nick']
#                 session['email'] = item['email']
#                 session['date'] = item['date']
#                 return func(*args, **kwargs)
#             else:
#                 return Response.failed(StatusCode.AUTH_FAILED)
#         except Exception as e:
#             logger.warning("unknown error: auth token {}".format(e))
#             return Response.failed()
#     return wrapper

def login_required(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        session_id = request.cookies.get('session')
        if not session_id or not session.get('id'):
            # raise Exception("please login first.")
            return Response.failed(message="please login first.",code=401)
            # return redirect(url_for('login'))
        return func(*args, **kwargs)
    return wrapper

"""
for API
    If role not in allowed roles, just return failed response is OK.
"""
def api_role_allowed(*roles):
    def decorator(func):
        @wraps(func)
        def decorated_func(*args, **kwargs):
            role_list = list(roles)
            if not session.get("role") in role_list:
                return Response.failed(StatusCode.AUTH_FAILED, message="role not allowed.")
            return func(*args, **kwargs)
        return decorated_func
    return decorator


""" 
for function. 
    If role not in allowed roles, just return failed response is *NOT* OK.
    It should raise an exception so that API could catch this and do something.
"""
def role_allowed(*roles):
    def decorator(func):
        @wraps(func)
        def decorated_func(*args, **kwargs):
            role_list = list(roles)
            if not session.get("role") in role_list:
                raise Exception("role not allowed.")
            return func(*args, **kwargs)
        return decorated_func
    return decorator

"""
def permission_required(permission: List):
    def decorator(func):
        @wraps(func)
        def decorated_func(*args, **kwargs):
            # id = session.get('id')
            perm = session.get('permission')
            
            has_perm = sum(perm)
            need_perm = sum(permission)
            
            # 1101 0111 -> 0101 != 1101
            if need_perm & has_perm != need_perm:
                return Response.failed(StatusCode.AUTH_FAILED)
                
            
            return func(*args, **kwargs)
        return decorated_func
    return decorator
            
            
def admin_required(func):
    return permission_required([Permission.ADMIN])(func) 
"""