"""
-*- coding:utf-8 -*-
@Project ：jxsjpt
@File    ：homework_orm.py
@IDE     ：PyCharm
<AUTHOR>
@Date    ：2023/3/18 16:43
@desc    ：
"""
from core.databases.orm.homework.hwDatabase import HwDatabase
from core.databases.db_mongo import mongo
from core.databases.orm.database_base import DatabaseBase


class QuestionOrm(HwDatabase):
    def __init__(self):
        HwDatabase.__init__(self)
        self.table = "hw_question_table"

    def query_hw_question(self,condition):
        return mongo[self.table].find(condition)

    def update_hw_question(self, condition, dao_object):
        item = mongo[self.table].update_one(condition, {"$set": dao_object})
        return item.matched_count

    def delete_hw_question(self, serialNo):
        result = mongo[self.table].delete_one(serialNo).delete_count
        return result
