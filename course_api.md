# 一、course API 接口说明

| api | 请求方式 | 功能说明
| ------------------------------------| ----- | ---- |
| /api/v1/teacher_center/creat_course | GET  | 新建课程获取课程状态参数 |
| /api/v1/teacher_center/creat_course | POST | 新建课程 |
|





## 1.新建课程
请求方法：GET
请求参数：无
示例：GET http://10.12.159.86:27017/api/v1/course/teacher_center/create_course
功能：获取课程状态编码，用于新建课程时指定课程状态
返回示例：

```json
{
    "status": {
        "status": "success",
        "code": 10200,
        "message": "get course status success"
    },
    "result": {
        "course_open": 21,
        "course_wait_open": 22,
        "course_closed": 20
    },
    "timestamp": 1681723729
}
```

请求方法：POST
请求参数：

| 参数 | 类型 | 参数说明 | 接收空值 |
| ---- | --- | -------- | ------- |
| course_name         | str     | 课程名                               | 否 |
| course_id           | str     | 校方指定课程编号                      | 是 |
| course_teacher_name | str[]   | 课程相关教师用户姓名                  | 否 |
| course_teacher_id   | str[]   | 课程相关教师用户对应id                | 否 |
| course_type         | str     | 课程类型，仅能从[理论课，实验课]二选一  | 否 |
| course_tag          | str[]   | 课程描述标签                          | 否 |
| course_status       | int     | 课程状态，仅能从以上get结果中三选一     | 否 |
| course_introduction | str     | 课程简介                             | 是 |
| course_purpose      | str     | 课程目的                             | 是 |
| course_image        | str     | 课程封面，图片url                     | 否 |
| course_organization | str     | 开课组织                             | 否 |
| open_date           | str     | 课程开始时间（xxxx-xx-xx）            | 否 |
| finish_date         | str     | 课程结束时间（xxxx-xx-xx）            | 否 |
| associate_experment | str[]   | 课程相关实验课，存id                  | 是 |
| last_update_user    | str     | 最近修改课程信息的用户姓名             | 否 |

示例：POST http://10.12.159.86:27017/api/v1/teacher_center/creat_course
json数据
功能：创建课程并填充基本信息,并返回新建课程的id
返回示例：

```json
{
    "status": {
        "status": "success",
        "code": 10200,
        "message": "create success"
    },
    "result": "643d34e7b59580cd2003ad95",
    "timestamp": 1681732840
}

```

## 2.搜索课程
请求方法：GET
请求参数：
| 参数 | 类型 | 参数说明 | 接收空值 |
| ---- | --- | -------- | ------- |
| course_name | str | 课程名，支持模糊搜索 | 是 |
| teacher     | str | 教师名，支持模糊搜索 | 是 |
| course_organization | 开课组织名，支持模糊搜索 | 是 |
上三个参数必须有一个，即搜索不能为空
返回课程简要相关信息

示例：GET http://10.12.159.86:27017/api/v1/course/search?course_name=<course_name>&teacher=<teacher>&organization=<organization>

返回示例：
```json
{
    "status": {
        "status": "success",
        "code": 10200,
        "message": "success"
    },
    "result": [
        {
            "_id": "643d34e7b59580cd2003ad95",
            "course_name": "ceshi",
            "course_status": 21,
            "course_image": "url",
            "course_organization": "ncc"
        }
    ],
    "timestamp": 1681785993
}

```


## 3.课程详细展示页面

请求方法：GET
请求参数：
| 参数 | 类型 | 参数说明 | 接收空值 |
| ---- | --- | -------- | ------- |
| id   | str | 课程id | 否 |

功能：根据课程id返回课程详细信息（全部信息）

示例：GET http://10.12.159.86:27017/api/v1/course/NCC-1?id=<_id>

返回示例：
```json
{
    "status": {
        "status": "success",
        "code": 10200,
        "message": "success"
    },
    "result": [
        {
            "_id": "643d34e7b59580cd2003ad95",
            "course_name": "ceshi",
            "course_type": "\u7406\u8bba\u8bfe(理论课)",
            "course_tag": [
                "ceshi1"
            ],
            "course_introduction": "ceshi",
            "course_purpose": "1",
            "course_image": "url",
            "course_organization": "ncc",
            "chapter":[
                {
                    "chapter_name":"1.1 23123213123",
                    "sub_chapter":{
                        {
                            "sub_chapter_name":"1.1 123123123213",
                            "resource":"url"
                        },
                        {
                            "sub_chapter_name":"1.2 123123123213",
                            "resource":"url"
                        }
                    }
                }
            ],
            "open_date": "time",
            "finish_date": "time",
            "associate_experment": [
                "12231232131"
            ]
        }
    ],
    "timestamp": 1681786743
}

```

## 4.课程观看页面

请求方法：GET
请求参数：
| 参数 | 类型 | 参数说明 | 接收空值 |
| ---- | --- | -------- | ------- |
| id   | str | 课程id | 否 |

功能：返回页面观看信息，一次返回所有章节及其内容相关资源url
示例：GET http://10.12.159.86:27017/api/v1/course/view?id=<course_id>

返回示例：
```json
{
    "status": {
        "status": "success",
        "code": 10200,
        "message": "success"
    },
    "result": {
        "chapter":[
                {
                    "chapter_name":"1.1 23123213123",
                    "sub_chapter":{
                        {
                            "sub_chapter_name":"1.1 123123123213",
                            "resource":"url"
                        },
                        {
                            "sub_chapter_name":"1.2 123123123213",
                            "resource":"url"
                        }
                    }
                }
            ],
        "course_type": "\u7406\u8bba\u8bfe(理论课)"
    },
    "timestamp": 1681787698
}

```
示例2：GET http://10.12.159.86:27017/api/v1/course/view?id=<course_id>&user=<user_id>

返回示例2：
```json
{
    "status": {
        "status": "success",
        "code": 10200,
        "message": "success"
    },
    "result": {
        "chapter":[
                {
                    "chapter_name":"1.1 23123213123",
                    "sub_chapter":{
                        {
                            "sub_chapter_name":"1.1 123123123213",
                            "resource":"url"
                        },
                        {
                            "sub_chapter_name":"1.2 123123123213",
                            "resource":"url"
                        }
                    }
                }
            ],
        "course_type": "\u7406\u8bba\u8bfe(理论课)",
        "last_view_position":"1.1 123123123213"
    },
    "timestamp": 1681787698
}
```


请求方法：PUT

请求参数：
| 参数 | 类型 | 参数说明 | 接收空值 |
| ---- | --- | -------- | ------- |
| id | str | 页面课程id | 否 |


请求参数(json)
| 参数 | 类型 | 参数说明 | 接收空值 |
| ---- | --- | -------- | ------- |
| time | int | 页面停留时间(分) | 否 |
| chapter | str[] | 页面关闭前完成的章节名 | 否 |

功能：学生关闭观看页面时发送请求更新学习进度

示例：PUT http://10.12.159.86:27017/api/v1/course/view?id=<course_id>&user=<user_id>
```json
{
    "chapter":[
        "1.1 ddddddddddd",
        "1.2 ddddddddddd"
    ],
    "time":56
}
```

返回示例：
```json
{
    "status": {
        "status": "success",
        "code": 10200,
        "message": "success"
    },
    "result": {
    },
    "timestamp": 1681787697
}
```

## 5.教师端显示自己所教的所有课程

请求类型：GET
请求参数：
| 参数 | 类型 | 参数说明 | 接收空值 |
| ---- | --- | -------- | ------- |
| id   | str | 老师用户id(session获取) | 否 |

功能说明：根据老师id在用户认证之后显示自己交的所有课程的简要信息

示例：GET http://10.12.159.86:27017/api/v1/course/showteachercourse

返回示例：
```json
{
    "status": {
        "status": "success",
        "code": 10200,
        "message": "success"
    },
    "result": [
        {
            "_id": "643d34e7b59580cd2003ad95",
            "course_name": "ceshi",
            "course_image": "url",
            "course_organization": "ncc",
            "teachers_name": [
                "zhangsan",
                "lisi"
            ],
            "teachers_id": [
                "123123123123123123213",
                "3123123123123123"
            ]
        }
    ],
    "timestamp": 1681802882
}
```

## 6.更新课程的任课老师

请求类型：POST
请求参数：
| 参数 | 类型 | 参数说明 | 接收空值 |
| ---- | --- | -------- | ------- |
| course_id  | str | 课程id | 否 |
| teacher_name | str[] | 教师用户姓名 | 否 |
| teacher_id |  str[] | 教师用户id | 否 |

功能：在页面选择该门课程的任课老师后，更新数据库相关关系表

示例：GET http://10.12.159.86:27017/api/v1/course/updateCourseTeacher
+json数据

返回示例：
```json
{
    "status": {
        "status": "success",
        "code": 10200,
        "message": "update ct success"
    },
    "result": [],
    "timestamp": 1681802882
}
```

## 7.课程章节列表以及详情展示页面
请求类型：GET
请求参数：

| 参数 | 类型 | 参数说明 | 接收空值 |
| ---- | --- | -------- | ------- |
| course_name | str  | 课程名       | 是       |

示例：GET http://10.12.159.86:27017/api/v1/course/sector/SectorListPage?course_name=<course_name>

返回示例：
```json
{
    "status": {
        "status": "success",
        "code": 10200,
        "message": "success"
    },
    "result": [
        {
            "_id": "64099801ebca8eb508b7668c",
            "course_name": "example",
            "chapter": [
                {
                    "chapter_name": "第一章 从入门到放弃",
                    "chapter_introduction": "sdadsadsdasdasdsadasdsads",
                    "sub_chapter": [
                        {
                            "chapter_name": "1.1 入门",
                            "chapter_introduction": "sdadsadsdasdasdsadasdsads",
                            "resource": "http://10.12.159.86/resources/2.mp3",
                            "Scenario_id": "1311231231231232312"
                        }
                    ]
                }
            ]
        }
    ],
    "timestamp": 1683186543
}
```

## 8.更新课程章节

请求类型：POST
请求参数：

| 参数    | 类型   | 参数说明     | 接收空值 |
| ------- | ------ | ------------ | -------- |
| chapter | 自定义 | 章节详细情况 | 否       |
| _id      | str | 课程id | 否 |

章节自定义结构如下：
章节最多2级，每个最小一级章节下只能拥有一个资源

```json
"chapter": [
    {
        "chapter_name": "第一章 从入门到放弃",
        "chapter_introduction": "sdadsadsdasdasdsadasdsads",
        "sub_chapter": [
            {
                "chapter_name": "1.1 入门",
                "chapter_introduction": "sdadsadsdasdasdsadasdsads",
                "resource": "http://10.12.159.86/resources/2.mp3",
                "Scenario_id": "1311231231231232312"
            }
        ]
    }
]
```

示例：POST http://10.12.159.86:27017/api/v1/course/sector/chapterupdate

返回示例：

```json
{
    "status": {
        "status": "success",
        "code": 10200,
        "message": "success"
    },
    "result": null,
    "timestamp": 1683186588
}
```



## 9.学生查看自己在学的课程

请求方式：GET

请求参数：
| 参数 | 类型 | 参数说明 | 接收空值 |
| ---- | --- | -------- | ------- |
| userid | str | 学生用户id（session获取） | 否 |

功能：学生查看自己在学的课程的简要信息

示例：GET http://10.12.159.86:27017/api/v1/course/student_center/chosen_course

返回示例：
```json
{
    "status": {
        "status": "success",
        "code": 10200,
        "message": "success"
    },
    "result": [
        {
            "_id": "643d34e7b59580cd2003ad95",
            "course_name": "ceshi",
            "course_image": "url",
            "course_organization": "ncc",
            "teachers_name": [
                "zhangsan",
                "lisi"
            ],
            "teachers_id": [
                "123123123123123123213",
                "3123123123123123"
            ]
        }
    ],
    "timestamp": 1681802882
}
```

## 10.老师批量添加学生到课程

请求方式：POST

请求参数：
| 参数 | 类型 | 参数说明 | 接收空值 |
| ---- | --- | -------- | ------- |
| course_id | str | 课程id | 否 |
| students_id | str[] | 学生用户id数组 | 否 |

功能：老师批量将学生添加到课程

示例： POST http://10.12.159.86:27017/api/v1/course/teacher_center/student_management

返回示例：
```json
{
    "status": {
        "status": "success",
        "code": 10200,
        "message": "update T-cs success"
    },
    "result": [
        {
            "course_id":"123123123123123123",
            "student_id":[
                "3123213123123131",
                "3151231424124141",
                "2313213123131231"
            ]
        }
    ],
    "timestamp": 1681802882
}
```

## 11.获取课程在修所有学生id

请求方式：GET

请求参数：
| 参数 | 类型 | 参数说明 | 接收空值 |
| ---- | --- | -------- | ------- |
| course_id | str | 课程id | 否 |

功能：获取课程下面再修的所有学生

示例：GET http://10.12.159.86:27017/api/v1/course/teacher_center/student_management

返回示例：
```json
{
    "status": {
        "status": "success",
        "code": 10200,
        "message": "update T-cs success"
    },
    "result": [
        {
            "course_id":"123123123123123123",
            "student_id":[
                "3123213123123131",
                "3151231424124141",
                "2313213123131231"
            ]
        }
    ],
    "timestamp": 1681802882
}
```

## 12.删除课程
请求方式：DELETE

请求参数：
| 参数 | 类型 | 参数说明 | 接收空值 |
| ---- | --- | -------- | ------- |
| course_id | str | 课程id | 否 |

功能：删除课程

示例：DELETE http://10.12.159.86:27017/api/v1/course/delete?id=<course_id>

返回示例：
```json
{
    "status": {
        "status": "success",
        "code": 10200,
        "message": "delet course success"
    },
    "result": [
    ],
    "timestamp": 1681802882
}
```
