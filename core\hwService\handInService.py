'''
-*- coding:utf-8 -*-
@Project ：jxsjpt 
@File    ：handInService.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2023/3/19 18:32 
@desc    ：处理学生查询作业列表，做作业，查看作业批改详情，修改已保存的答案，提交作业
'''
import re
from datetime import datetime

from bson import ObjectId
from requests import session
from common.utils.ilab_util import IlabUtils
from common.utils.logger import logger
from core.databases.orm.homework.classHIHw_orm import ClassHIHwOrm
from core.databases.orm.homework.handInHwork_orm import HandInHworkOrm
from core.databases.orm.homework.handInDetail_orm import HandInDetailOrm
from core.databases.orm.homework.question_orm import QuestionOrm
from core.auth.ilab.ilab_expr_service import *
class HandInService:
    def __init__(self):
        self.dbhInOrm = HandInHworkOrm()
        self.dbhIDOrm = HandInDetailOrm()
        self.dbQuestionOrm = QuestionOrm()
        self.dbclahiOrm = ClassHIHwOrm()

    def queryHandIn(self,jsonData):
        '''
        方法功能：学生查询已布置的作业
        条件：课程id，学生id
        '''

        courseId = jsonData['courseId']
        studentId = jsonData['studentId']
        logger.info("学生{}查询课程{}已布置的作业".format(studentId,courseId))
        handInList = self.dbhInOrm.find( {
            "$and": [
                {'courseId':courseId},
                {'studentId':studentId}
            ]
        })
        result = []
        for handIn in handInList:
            handIn['_id'] = str(handIn['_id'])
            # 返回前端作业发布设置信息，包括期限时间，是否允许补交
            condition = {'$and': [{'homeworkNo': handIn['hworkNo']},
                                  {'classId': handIn['classId']}]}
            result1 = self.dbclahiOrm.find(condition)
            for classHandIn in result1:
                logger.info("获取作业{}发布设置信息".format(handIn['hworkNo']))
                handIn['startTime'] = classHandIn['startTime'] # 开始时间
                handIn['endTime'] = classHandIn['endTime'] # 结束时间
                handIn['expiredHand'] = classHandIn['expiredHand']# 是否允许补交，0 不允许 1允许
            result.append(handIn)
        return result
    def queryHandInDetail(self,jsonData):
        '''
        方法功能：老师查看批改详情,逻辑和toDoHomework相识，但是有答案
        '''
        return self.toDoHomework(jsonData,isDone = 1)
    def toDoHomework(self,jsonData,isDone = 0):
        '''
        方法功能：提供学生做作业界面所需要加载的数据，包括题目信息
        条件：handInNo
        '''
        #查询handIndetail表
        handInHworkNo = jsonData['_id']
        handInDetailList = self.dbhIDOrm.find({"handInHworkNo":handInHworkNo})
        if isDone==1:
            logger.info("isDone=={},本次为学生查询作业详情,提交表id为：{}".format(isDone,handInHworkNo))
        else:
            logger.info("isDone=={},本次为学生去做作业,提交表id为：{}".format(isDone, handInHworkNo))
        #查询题库表，获得习题信息，包括类型，题干，选项等
        resultList = []
        for handInDetail in handInDetailList:
            qusetionNo = handInDetail['questionNo']
            question = self.dbQuestionOrm.find_by_id(qusetionNo)
            info = {}
            handInDetail['_id'] = str(handInDetail['_id'])
            # question['_id'] = str(question['_id'])
            question.pop('_id')
            if isDone==0:
                question.pop('ref_answer')#去掉参考答案
                handInDetail.pop('obtainedScore')
                handInDetail.pop('comment')
            info['handInDetail'] = handInDetail
            info['question'] = question
            resultList.append(info)
        return resultList
    
    def correct(self, handIn):
        hwDetail  = self.dbhIDOrm.find_one({'handInHworkNo': str(handIn["_id"])})
        flag = ""
        score = 40
        evaluation = "已完成"
        #批改完成后修改作业状态为已批改
        #如果是重复提交，则指修改得分和提交的答案，不再修改各种状态
        self.dbhIDOrm.update_by_id(hwDetail['_id'], {"obtainedScore": score, "aContent": flag})
        if handIn['state'] != 5:
            self.dbhInOrm.update_by_id(handIn['_id'],{
                'note':evaluation,
                'obtainedScore':score,
                'correctTeaId':"auto",#批改人
                'correctTime':datetime.utcnow().strftime('%Y-%m-%dT%H:%M:%SZ'),#批改时间
                'state':5 #已批改
            })
            self.dbclahiOrm.increment_by_condition({"$and":[{'homeworkNo': handIn['hworkNo']},
                                                        {'classId': handIn['classId']}]}, {'correctCount': 1,'handedCount': 1})
        else:
            self.dbhInOrm.update_by_id(handIn['_id'], {
                'note': evaluation,
                'obtainedScore': score,
                # 'correctTeaId': "auto",  # 批改人
                'correctTime': datetime.utcnow().strftime('%Y-%m-%dT%H:%M:%SZ')  # 批改时间
            })
        return {"score":score, "evaluation":evaluation, "maxScore":handIn['score']}
    def saveHandIn(self,jsonData):
        '''
        方法功能：用户保存或者提交学生做题的答案，isHandIn=0，只保存，isHandIn=1，保存并提交
        参数：学生上送的作业答案
        '''
        logger.info("保存作业参数：[{}]".format(str(jsonData)))
        handInHworkId = jsonData['_id']
        handInHwork = self.dbhInOrm.find_by_id(handInHworkId)
        if handInHwork['state'] not in [1, 2, 3, 4, 5]:#只有未提交，保存未提交，打回状态才可以提交作业
            logger.info("作业已提交，不能更改")
            return '作业已提交，不能再更改'
        isHandIn = jsonData['isHandIn']
        handInDetails = jsonData['handInDetails']
        if jsonData['user_from'] == 'ilab':#如果是ilab用户,作业需要保存access_token和user_from,在教师批改完作业后，还需要上传
            logger.info("ilab用户提交作业，提交表id为{}".format(handInHworkId))
            self.dbhInOrm.update_by_condition({"_id": ObjectId(handInHworkId)}, {'user_from': jsonData['user_from'],
                'access_token': jsonData['access_token']})
        for hid in handInDetails:
            if hid["question_type"]==5 and not hid["aContent"].endswith(".pdf"):
                return "请提交pdf"
            serialNo = hid['_id']
            answer = hid['aContent']
            updatedict = {}
            updatedict['aContent'] = answer
            updatedict['finishTime'] = datetime.utcnow().strftime('%Y-%m-%dT%H:%M:%SZ')
            self.dbhIDOrm.update_by_condition({"_id":ObjectId(serialNo)},updatedict)
        if isHandIn == 1:#更新作业提交表状态为已提交
            logger.info("本次操作为提交作业")
            condition = {'$and': [{'homeworkNo': handInHwork['hworkNo']},
                                  {'classId': handInHwork['classId']}]}
            #result = self.dbclahiOrm.find(condition)
            result = self.dbclahiOrm.find_one(condition)
            endTime = self.__format_datestr_with_zone(result['endTime'])
            now = datetime.utcnow().astimezone()
            if now > endTime:#已过正常提交时间
                if result['expiredHand']==1:
                    logger.info("已过提交作业时间，允许补交")
                else:
                    logger.info("已过提交作业时间，不允许补交")
                    return "已过提交作业时间，不允许补交"

            updatedict = {}
            updatedict['state'] = 3
            if handInHwork['state']==5:
                updatedict['state'] = 5
            updatedict['finishTime'] = datetime.utcnow().strftime('%Y-%m-%dT%H:%M:%SZ')
            self.dbhInOrm.update_by_condition({"_id": ObjectId(handInHworkId)}, updatedict)
            #班级提交表加1
            handedCount = result['handedCount']
            self.dbclahiOrm.update_by_condition(condition,{'handedCount' : handedCount+1})
            if handInDetails and handInDetails[0]["question_type"]==5:
                access_id = session.get("id")
                access_token=session.get('access_token')
                appid=str(IlabUtils().appid)
                result={}
                result['user_id']=access_id
                result['title']="实验报告"
                result['startTime']=int(datetime.now().timestamp())*1000-16*3600000
                result['endTime']=int(datetime.now().timestamp())*1000-8*3600000
                result['timeUsed']=(result['endTime']-result['startTime'])/1000
                result['expectTime']=3600
                result['score']=0
                result['courseId']=handInHwork['courseId']
                result['chapterId']=""
                result['flag']= ""
                correct_res = self.correct(handInHwork)
                result['repeatCount']=1
                result['maxScore']=correct_res['maxScore']
                result['score']=correct_res['score']
                result['evaluation']=correct_res['evaluation']
                result['scoringModel']="报告提交得40分"
                res=DBExprforAdmin.insert_expr_record(result)
                res=ilabExprService().sendreport(username=session.get("profile_id"),title="城市水务系统网络安全攻防虚拟仿真实验",access_id=access_id,access_token=access_token,appid=appid)
                logger.info('报告提交结果：{}'.format(res))
                logger.info("真实ip{}".format(str(request.access_route[0])))
        else:#只更新作业提交表状态为已保存
            logger.info("本次操作为保存作业")
            updatedict = {}
            updatedict['state'] = 2
            updatedict['finishTime'] = datetime.utcnow().strftime('%Y-%m-%dT%H:%M:%SZ')
            self.dbhInOrm.update_by_condition({"_id": ObjectId(handInHworkId)}, updatedict)
        return True
    def handInHwork(self,jsonData):
        '''
        方法功能：提交作业
        参数：学生上送的作业id
        先不用
        '''
        serialNo = jsonData['handInNo']
        self.dbhInOrm.update_by_condition({"_id":ObjectId(serialNo)},{"state":2})
        return True
    def __format_datestr_with_zone(self,datetime_str: str):
        """
        格式化带时区时间字符串，返回datetime类型时间
        :param datetime_str: 2022-06-14T15:16:31+00:00
        :return: datetime
        """
        format_ = '%Y-%m-%d %H:%M:%S'
        if '.' in datetime_str:
            format_ = format_ + '.%f'
        zone_ = re.search(r'[+-]\d{2}:\d{2}', datetime_str)
        if zone_:
            format_ = format_ + '%z'
        if 'T' in datetime_str:
            format_ = format_.replace(' ', 'T')
        format_ = format_ + 'Z'
        return datetime.strptime(datetime_str, format_).astimezone()
