'''
-*- coding:utf-8 -*-
@Project ：jxsjpt 
@File    ：handInDetail.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2023/3/18 16:28 
@desc    ：作业提交明细表封装对象
'''
class HandInDetail:
    def __init__(self,data):
        # self._id = data['']#表主键非ObjectId
        self.hworkDetailNo=str(data['_id'])#作业明细表主键
        # self.homeworkNo = data['']  # 课程作业号
        self.handInHworkNo = data['handInHworkNo']#提交记录表号
        self.questionNo = data['questionNo']  # 习题号
        self.score = data['score']  # 总分数
        self.obtainedScore = 0  # 所得总分数
        self.finishTime = None#完成时间
        self.knowledgeLabel = data['knowledgeLabel']  # 知识点标签
        self.aContent = None  # 提交答案
        self.comment = None   # 批改批注
    def toDbType(self):
        return self.__dict__