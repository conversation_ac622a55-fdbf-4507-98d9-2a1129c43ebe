from flask import request
from flask_restful import Resource, reqparse
from common.utils.logger import logger
from core.data.response import Response, StatusCode
from core.databases.orm.course.course_orm import CourseInfo,CourseStatusCode,CourseStudent
from flask_restful import Resource, reqparse
from bson import ObjectId
from core.auth.auth import *
import time

#**********************************课程新建/更新parser************************************
course_update_parser = reqparse.RequestParser()
course_update_parser.add_argument("_id",                    required=True,location=['json',],type=str,)
course_update_parser.add_argument("updater",         required=True,location=['json',],type=str,)
def mutilist_type(value,name):
    if not value:
        return []
    elif not isinstance(value,dict):
        raise ValueError('cant use varivale '+name+'as list type')
    return value
course_update_parser.add_argument("chapter",               required=True,location=['json',],type=mutilist_type,action='append',)

#教师端对章节详细内容进行更改/添加等，本质update，使用creat_parser
class ChapterUpdate(Resource):
    '''
    API: /api/v1/course/srctor/chapterupdate
    args:course_update_parser
    输入:更新者updater以及更新内容chapter
    '''
    @login_required
    def post(self):
        args = course_update_parser.parse_args()
        updater = str(session.get('id'))
        _id = ObjectId(str(request.json.get('_id')))
        chapter = request.json.get("chapter")
        #最后更新日期自己更新
        need = {"_id": 1,
                "chapter": 1,
                "last_update_time": 1, }
        try:
            _,status=CourseInfo.find_by_id(_id,need)
            if CourseInfo.find_by_id(_id,need):
                update = {}
                update["last_update_user"] = updater
                update["chapter"] = chapter
                update[ "last_update_time"] = time.strftime("%Y-%m-%d-%H:%M")
                res,status = CourseInfo.update_one_by_id(_id,update)
                if status == CourseStatusCode.UPDATE_SUCCESS:
                    return Response.success(data=res,message="success")
                else:
                    return Response.failed(data="", message="update failed")
            return Response.failed(data="", message="cannot find specific course")
        except Exception as e:
            logger.warning("test failed: {}".format(e))
            return Response.failed(data="", message=e)



#****************************章节列表及内容页面*****************************
class SectorListPage(Resource):
    """API /api/v1/course/sector/SectorListPage?course_name=<course_name>"""
    @login_required
    def get(self):
        _id=ObjectId(request.args.get("id"))
        if _id == None:
            return Response.failed(message='id null ')
        query={
            "_id":_id,
        }

        need={
                "_id":1,
                "course_name":1,
                "chapter":1,
        }
        try:
            result,status=CourseInfo.find(
                query=query,
                need=need,
            )
            if not status == CourseStatusCode.FIND_SUCCESS:
                return Response.failed(data="", message="not found")
            for item in result:
                item["_id"]=str(item["_id"])
                if session.get('role') == 'student':
                    sid=session.get("id")
                    res,status=CourseStudent.find(
                        query={"course_id":str(_id),"student_id":sid},
                        need={"complete":1,"last_view_position":1}
                    )
                    if not status ==  CourseStatusCode.FIND_SUCCESS:
                        complete = []
                    complete = res[0]['complete']
                    last_view_position = res[0]['last_view_position']
                    item.update({"complete":complete,"last_view_position":last_view_position})
            return Response.success(data=result,message="success")
        except Exception as e:
            logger.warning("get method meets error: {}".format(e))
            return Response.failed(data="", message=e)