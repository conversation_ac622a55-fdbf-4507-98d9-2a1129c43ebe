'''
-*- coding:utf-8 -*-
@Project ：jxsjpt 
@File    ：classhandinHwork.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2023/5/12 21:24 
@desc    ：
'''
class ClassHandInHw:
    def __init__(self,data):
        # self._id = data['']#表主键非ObjectId
        self.homeworkNo=data['_id']#作业表主键
        self.homeworkName = data['']  # 作业表主键
        self.classId = data['classId']#班级主键
        self.className = None #班级名称
        self.courseId = data['course_id']  # 课程id
        self.teacherId = None # 发布作业老师id
        self.studentCount = data['']  # 班级人数
        self.handedCount = data['']  # 已提交人数
        self.correctCount = data['']  # 已批改人数
        self.startTime = data['']  # 开始时间
        self.endTime = data['']  # 结束时间
        self.expiredHand = 0 # 是否允许补交，0 不允许 1允许
        self.state = 2 #2已发布 3已结束
        self.type = data['type']  # 作业类型，1普通作业，2报告作业

    def toDbType(self):
        return self.__dict__