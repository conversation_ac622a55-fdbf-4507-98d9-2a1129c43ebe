from common.utils.logger import logger
from core.databases.db_mongo import mongo, T_EXPR_MESSAGE
from core.databases.db_error import DatabaseError
from core.databases.orm.database_base import DatabaseBase
from bson import ObjectId

'''
@description: ilab平台管理ORM
@author: zheng
@date: 2024/1/22
'''
class _DBExprforAdmin(DatabaseBase):
    def __init__(self) -> None:
        DatabaseBase.__init__(self)
        self.table_expr_message=T_EXPR_MESSAGE

    def insert_expr_record(self,insert_items):
        try:
            mongo[self.table_expr_message].delete_many({'user_id':insert_items['user_id'],'courseId':insert_items['courseId'],'chapterId':insert_items['chapterId']})
            res=mongo[self.table_expr_message].insert_one(insert_items)
            return res
        except Exception as e:
            logger.warning("insert exam failed: {} {}".format(insert_items["user_id"], e))
            return False
    def find(self, query):
        res=mongo[self.table_expr_message].find(query)
        return res
DBExprforAdmin=_DBExprforAdmin()