#!/usr/bin/env python
# -*- coding: UTF-8 -*-
'''
@Project ：jxsjpt 
@File    ：file_handler.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2023-03-21 10:36 
'''
import datetime
import hashlib
import requests
import calendar
import time

from bson import ObjectId
from flask_restful import Resource, reqparse

from config import Config
from core.data.response import Response
from common.utils.logger import logger
from common.utils.pathConcat import inner_url, outer_url
from common.utils.fileParser import parseFile
from flask import Flask, render_template, send_from_directory, request
from config import Config
import os
from core.databases.orm.resource.resource_orm import ResourceDB
from core.auth.auth import *
from web.api.user.user_api import role_to_handler, get_handler

# 白名单放行文件后缀
ALLOWED_EXTENSIONS = {'txt', 'png', 'jpg', 'xls', 'JPG',
                      'PNG', 'xlsx', 'gif', 'GIF', 'ppt',
                      'docx', 'mp4', 'flv', 'pdf','doc'}
# 不在资源页面展示的，主要是图片
NOT_ALLOWED_EXTENSIONS = {'png', 'jpg', 'JPG', 'PNG', 'gif', 'GIF'}


class FileHandler(Resource):
    """ API: /api/v1/file """

    @login_required
    def post(self):
        parser = parseFile()
        args = parser.parse_args()
        course_id = args['course_id']
        # 从session获取用户信息
        user_id = session.get("id")
        user_role = session.get("role")
        try:
            h_user = get_handler(user_role)
            user_data = h_user.get_one({"_id": ObjectId(user_id)})
        except Exception as e:
            msg = "用户{id}不合法: {}".format(user_id, e)
            logger.warning(msg)
            return Response.failed(message=msg)
        user_name = user_data["user_name"]
        if not course_id:
            return Response.failed(message="在上传资源时，请指定对应的课程！")
        resource_type = args['resource_type']
        f = request.files['myfile']  # 从表单的file字段获取文件，myfile为该表单的name值



        # 获取后缀文件名
        ext = f.filename.rsplit('.', 1)[1]
        #如果接口没有上传resource_type，取ext
        if resource_type is None or resource_type == "":
            resource_type = ext
        if f and '.' in f.filename and ext in ALLOWED_EXTENSIONS:  # 判断是否是允许上传的文件类型
            if ext in NOT_ALLOWED_EXTENSIONS:#图片等文件不在前端资源查询页面显示
                allow = 0
            else:
                allow = 1
            if args['resource_name'] == "学生报告":#学生提交的报告作业，不便在前端展示
                allow = 0
                logger.info("学生报告，不在前端展示")
            # 对文件取hash
            md5 = hashlib.md5(f.read()).hexdigest()
            
            fname = datetime.datetime.now().strftime("%Y-%m-%d-%H:%M:%S")+"-"+f.filename
            f.seek(0)
            try:
                f.save(os.path.join(Config.UPLOAD_FOLDER, fname))  # 保存文件到upload目录
            except Exception as e:
                logger.warning("文件上传失败: {}".format(e))
                return Response.failed(message=e)

            current_GMT = time.gmtime()
            time_stamp = calendar.timegm(current_GMT)
            # 获取文件大小 单位为字节
            file_size = f.seek(0, os.SEEK_END)
            try:
                # 直接调用
                dao_object = ResourceDB.find_by_id_and_course_id(md5, course_id)
                if False:
                    url = {
                        "url": dao_object["url"]
                    }
                    #若文件已添加过，直接返回文件url
                    logger.info("资源已存在：[{}]，直接返回前端使用".format(url["url"]))
                    return Response.failed(code=10201, message="在该课程中，该资源已经添加过了!", data=url)
                data = {
                    "id": md5,
                    "url": '/api/v1/file/' + fname,
                    "file_name": fname,
                    "resource_type": resource_type,
                    "add_user": user_name,
                    "add_time": datetime.datetime.utcnow().strftime('%Y-%m-%dT%H:%M:%SZ'),
                    "modify_user": user_name,
                    "modify_time": datetime.datetime.utcnow().strftime('%Y-%m-%dT%H:%M:%SZ'),
                    "download_count": 0,
                    # 在前端做文件大小校验，并且将大小传到后端
                    "resource_size": file_size,
                    "resource_version": args['resource_version'],
                    "resource_description": args['resource_description'],
                    "resource_name": args['resource_name'],
                    # todo 关联icon id
                    "icon_id": "test",
                    "course_id": course_id,
                    # 自定义label
                    "labels": request.values.getlist('labels[]'),
                    "allow": allow
                }
                result = ResourceDB.add_resource(data)
                url = {
                    "url": data["url"]
                }
                if result:
                    return Response.success(data=url, message="添加文件成功！")
                else:
                    return Response.failed(message="添加失败，内部服务错误！")

                # 服务分离
                # url = inner_url('/api/v1/resource')
                # resp = requests.put(url, json=data, headers=headers)
                # if resp.status_code != 200:
                #     return Response.failed(message="内部服务错误")
            except Exception as e:
                logger.warning("资源信息入库失败: {}".format(e))
                return Response.failed(message=e)
        else:
            return Response.failed(message="文件类型不合法！")


# 对文件本身的操作
class FileIDHandler(Resource):
    """ API: /api/v1/file/<file_id> """
    # 这里传文件id
    @login_required
    def get(self, file_name):
        download_flag = request.args.get("download")
        course_id = request.args.get("course_id")
        resource_id = str(file_name).split(sep='.')[0]
        if not download_flag:
            download_flag = False
        else:
            download_flag = True
        upload_path = os.path.join(Config.Resource_root_path, 'upload')
        if course_id:
            try:
                dao_object = ResourceDB.find_by_id_and_course_id(resource_id, course_id)
                if dao_object:
                    dao_object["download_count"] += 1
                    ResourceDB.update_resource(resource_id, course_id, dao_object)
            except Exception as e:
                logger.warning("内部服务错误: {}".format(e))
                return Response.failed(message=e)
        # as_attachment=True 下载 False 打开
        return send_from_directory(upload_path, file_name, as_attachment=download_flag,use_x_sendfile=True)

    # 这里传文件id
    @login_required
    def delete(self, file_id):
        parser = reqparse.RequestParser()
        # 传入文件hash id + 后缀名
        parser.add_argument('file_name', type=str, location='json')
        parser.add_argument('course_id', type=str, location='json')
        args = parser.parse_args()
        file_name = args['file_name']
        course_id = args['course_id']
        if not course_id:
            return Response.failed(message="请指定资源所属课程！")
        try:
            result = ResourceDB.delete_by_id_and_course_id(file_id, course_id)
        except Exception as e:
            logger.warning("DELETE resource failed: {}".format(e))
            return Response.failed(message="删除失败，内部服务错误")
        if result == 0:
            return Response.failed(message="删除失败，资源不存在！")
        try:
            # 没有文件的课程映射了，方可删除文件
            if not ResourceDB.find_by_id(file_id):
                os.remove("./upload/" + file_name)
        except Exception as e:
            logger.warning("资源删除失败: {}".format(e))
            return Response.failed(message=e)
        return Response.success(message="删除资源成功！")

