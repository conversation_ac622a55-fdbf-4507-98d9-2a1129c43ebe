#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time   : 5/10/21 1:17 AM
# <AUTHOR> jackey
# @File   : db_mongo.py
# @desc   : "项目配置文件"

from web.flask_app import flask_app
from flask_pymongo import PyMongo

mongo = PyMongo(flask_app).db

T_TESTS = "course_platform_tests"

T_STUDENTS_ORGANIZATION = "course_platform_org_students"
T_TEACHERS_ORGANIZATION = "course_platform_org_teachers"

T_USER_BLOCK = "UserBlock"

T_ADMIN = "course_platform_admin_v1"
T_POC_TASKS = "poc_tasks"
T_POC_PLUGINS = "poc_plugins"
T_POC_VULS = "poc_vuls"
T_SQLMAP_TASKS = "sqlmap_tasks"
T_SQLMAP_RESULT = "sqlmap_result"
T_HTTP_REQUEST_LOG = "http_req"
T_JSON_HIJACKER_TASK = 'json_hijacker_task'
T_JSON_HIJACKER_RES = 'json_hijacker_res'
T_XSS_TASKS = 'xss_tasks'
T_XSS_PAYLOADS = 'xss_payloads'
T_XSS_RES = 'xss_res'

T_PORT_TASKS = 'port_scan_tasks'
T_PORT_RESULT = 'port_scan_result'
T_WHATWEB_TASK = 'whatweb_tasks'
T_WEB_FP = 'web_fingerprint'
T_SUBDOMAIN_TASK = 'subdomain_tasks'
T_SUBDOMAIN_RESULT = 'subdomain_result'
T_RESOURCE_MANAGE = 'Platform_resource'

T_USERS = "course_platform_users"
T_ROLES = "course_platform_roles"

T_STUDENTS = "course_platform_students"
T_ASSISTANTS = "course_platform_assistants"
T_TEACHERS = "course_platform_teachers"
T_ADMINS = "course_platform_admins"

T_PORT_TASKS = 'port_scan_tasks'
T_PORT_RESULT = 'port_scan_result'
T_WHATWEB_TASK = 'whatweb_tasks'
T_WEB_FP = 'web_fingerprint'
T_SUBDOMAIN_TASK = 'subdomain_tasks'
T_SUBDOMAIN_RESULT = 'subdomain_result'

T_COURSE = "CourseInfo"
T_COURSE_STUDENT = "Course_Student"
T_COURSE_TEACHER = "Course_Teacher"
T_COURSE_TIME = "Course_Time"
T_LOGINHISTORY = "course_platform_login_history"
T_EXPERIMENT= "Experiment"
T_EXPERMIENT_COURSE="Experiment_Course"
T_EXPERMIENT_TEACHER="Experiment_Teacher"
T_NOTEBOOK = "Notebook"
T_SEMESTERS="Semesters"

T_TEAIDXSTA="TeacherIndexStatistics"
T_NEWPW="PWexpire"
# T_CONFIG = "course_platform_configuration"
# T_SYSTEM_INFO = "course_platform_system_info"

# T_ADMIN = "course_platform_admin_v1"
# T_POC_TASKS = "poc_tasks"
# T_POC_PLUGINS = "poc_plugins"
# T_POC_VULS = "poc_vuls"
# T_SQLMAP_TASKS = "sqlmap_tasks"
# T_SQLMAP_RESULT = "sqlmap_result"

# T_HTTP_REQUEST_LOG = "http_req"
# T_JSON_HIJACKER_TASK = 'json_hijacker_task'
# T_JSON_HIJACKER_RES = 'json_hijacker_res'
# T_XSS_TASKS = 'xss_tasks'
# T_XSS_PAYLOADS = 'xss_payloads'
# T_XSS_RES = 'xss_res'

# T_PORT_TASKS = 'port_scan_tasks'
# T_PORT_RESULT = 'port_scan_result'
# T_WHATWEB_TASK = 'whatweb_tasks'
# T_WEB_FP = 'web_fingerprint'
# T_SUBDOMAIN_TASK = 'subdomain_tasks'
# T_SUBDOMAIN_RESULT = 'subdomain_result'


T_EXAM_MESSAGE = 'exam_message'
T_EXAM_RECORD_PAPER = 'exam_record_paper'
T_EXAM_RECORD_STUDENT = 'exam_record_student'
T_EXAM_PAPER = 'Exam_paper'

T_EXPR_MESSAGE = 'lab_experiment'