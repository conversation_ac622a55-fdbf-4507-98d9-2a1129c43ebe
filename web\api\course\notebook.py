from flask import request,session
from flask_restful import Resource, reqparse
from common.utils.logger import logger
from core.data.response import Response, StatusCode
from core.databases.orm.course.course_orm import *
from flask_restful import Resource, reqparse
from bson import ObjectId
from core.auth.auth import *
from web.api.course.course_teacher import getClassName,getTeacherName
from core.databases.orm.user.organzation_orm import *
from core.databases.orm.user.users_orm import *
from .course_tool import compare_time
import re
import time
import datetime
import requests
from datetime import timedelta
from dateutil import rrule
import collections


class Editnote(Resource):
    @login_required
    def get(self):
        try:
            if session.get('role') != 'student':
                return Response.success(message='notebook:not student',data='')  
            course_id=request.args.get("course_id")
            chp_id=request.args.get("chapter")
            
            if course_id == None or chp_id == None:
                return Response.failed(message='notebook:None param',data='')
            
            sid=session.get("id")
            res,status=Notebook.find(
                query={
                    "course_id":course_id,
                    "student":sid
                },
                need={
                    "_id":1,
                    "notes":1
                }
            )
            if status == CourseStatusCode.FIND_SUCCESS:
                res=res[0]
                data={
                    "_id":str(res['_id']),
                    "notes":''
                }
                if chp_id in res['notes'].keys():
                    data['notes']=res['notes'][chp_id]
                return Response.success(message='notebook:success',data=data)
            else:
                new={
                    'available':1,
                    'course_id':course_id,
                    "student":sid,
                    "notes":{
                        chp_id:''
                    }
                }
                id,status=Notebook.insert_one(data=new)
                if status == CourseStatusCode.INSERT_SUCCESS:
                    return Response.success(message='notebook:success',data={"_id":str(id),"notes":''})
                else:
                    return Response.failed(message='notebook:error',data=status)
        except Exception as e:
            error=str(e)+';;'+str(e.__traceback__.tb_frame.f_globals["__file__"])+';;'+str(e.__traceback__.tb_lineno)
            logger.exception(error)
            logger.warning("create scheme meets error: {}".format(error))
            return Response.failed(data="", message=error)
                     
    @login_required 
    def post(self):
        try:
            if session.get('role') != 'student':
                return Response.success(message='notebook:not student',data='')
            args=request.json
            id=args['_id']
            res,status=Notebook.find_by_id(_id=id,need={})
            if not status == CourseStatusCode.FIND_SUCCESS:
                return Response.failed(message='notebook:post find error')
            notes=res['notes']
            if not args['chapter'] in notes.keys():
                notes.update({args['chapter']:args['notes']})
            else:
                notes[args['chapter']]=args['notes']
            _,status=Notebook.update_one_by_id(_id=id,update={"notes":notes})
            if status == CourseStatusCode.UPDATE_SUCCESS:
                return Response.success(message='notebook:update success')
            return Response.failed(message='notebook:update failed')
        except Exception as e:
            error=str(e)+';;'+str(e.__traceback__.tb_frame.f_globals["__file__"])+';;'+str(e.__traceback__.tb_lineno)
            logger.exception(error)
            logger.warning("create scheme meets error: {}".format(error))
            return Response.failed(data="", message=error)
        
        
        
class AggregateTest(Resource):
    def get(self):
        tid=session.get('id')
        course_id=request.args.get('id')
        res,status=CourseTeacher.aggregate_find([
            {
                "$match":{
                    "teacher_id":tid,
                }
            },
            {
                "$lookup": {
                    "from": "CourseInfo",
                    "let": {"cid": {"$toObjectId": "$course_id"}},  
                    "pipeline": [
                        {
                            "$match": {
                                "$expr": {
                                    "$eq": ["$$cid","$_id"]  
                                }
                            }
                        }
                    ],
                    "as": "joined_data"
                }
            },
            {
              "$unwind":"$joined_data"  
            },
            {
                "$project":{
                    "_id":{"$toString":"$_id"},
                    "course_name":"$joined_data.course_name",
                    "chapter":"$joined_data.chapter"
                }
            }
        ])
        return Response.success(data=res)