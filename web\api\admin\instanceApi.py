'''
-*- coding:utf-8 -*-
@Project ：jxsjpt 
@File    ：instanceApi.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2023/7/21 21:49 
@desc    ：实例操作接口
'''
import time

from flask import request
from flask_restful import Resource, reqparse

from common.utils.logger import logger
from core.data.response import Response
from core.middle.instanceService import InstanceService


class InstanceApi(Resource):
    def __init__(self):
        self.businessService = InstanceService()

    def get(self):
        try:
            jsonData = []
            page = request.args.get('page', type=int)
            size = request.args.get('size', type=int)

            instances = self.businessService.queryInstance(
                {}, page, size)
            return Response.success(data=instances, message="")

        except Exception as e:
            logger.warning("get instance failed: {}".format(e))
            logger.exception(e)
            return Response.failed(message=e)

    def post(self):
        """
        新增实例：
        """
        parser = reqparse.RequestParser()
        parser.add_argument('ins_id', type=str)
        parser.add_argument('model_name', type=str)
        parser.add_argument('status', type=str)
        parser.add_argument('create_time', type=str)
        parser.add_argument('course_name', type=str)
        parser.add_argument('course_id', type=str)
        parser.add_argument('chapter_id', type=str)
        parser.add_argument('chapter', type=str)
        parser.add_argument('user_name', type=str)
        parser.add_argument('user_id', type=str)
        parser.add_argument('user_role', type=str)
        try:

            args = parser.parse_args()
            ins_id = args['ins_id']
            model_name = args['model_name']
            status = args['status']
            create_time = args['create_time']
            chapter = args['chapter']
            course_name = args['course_name']
            user_name = args['user_name']
            user_id = args['user_id']
            user_role = args['user_role']
            course_id = args['course_id']
            chapter_id = args['chapter_id']
            # 修改直接返回id
            # jsonData = "sava_question_id:" + str(self.businessService.save_question({
            jsonData = str(self.businessService.saveInstance({
                # 'create_time': int(time.strftime("%Y%m%d", time.localtime())),
                'ins_id': ins_id,
                'model_name': model_name,
                'status': status,
                'create_time': create_time,
                'chapter': chapter,
                'course_id': course_id,
                'chapter_id': chapter_id,
                'course_name': course_name,
                'user_name': user_name,
                'user_id': user_id,
                'user_role': user_role
            }))
            return Response.success(message=jsonData)
        except Exception as e:
            logger.warning("save instance failed: {}".format(e.data))
            logger.exception(e)
            return Response.failed(message=e)

    def delete(self):
        try:
            parser = reqparse.RequestParser()
            parser.add_argument('ins_id', type=str)
            args = parser.parse_args()
            ins_id = args.get("ins_id")
            reuslt = self.businessService.deleteInstance(ins_id)
            return Response.success(message=reuslt)
        except Exception as e:
            logger.warning("delete instance failed: {}".format(e))
            logger.exception(e)
            return Response.failed(message=e)

    def put(self):
        try:
            parser = reqparse.RequestParser()
            parser.add_argument('ins_id', type=str)

            parser.add_argument('status', type=str)
            args = parser.parse_args()

            ins_id = args['ins_id']
            status = args['status']

            jsonData = self.businessService.modifyInstance(
                {
                    'status': status
                }, ins_id
            )

            return Response.success(message=jsonData)

        except Exception as e:
            logger.warning("update instance status failed: {}".format(e))
            logger.exception(e)
            return Response.failed(message=e)


class InstanceActivateCheck(Resource):
    '''
    启动实例前的校验
    '''

    def __init__(self):
        self.businessService = InstanceService()

    def get(self):
        try:
            course_id = request.args.get('course_id', type=str)
            chapter_id = request.args.get('chapter_id', type=str)

            code, msg = self.businessService.canStart(course_id, chapter_id)
            return Response.success(data=code, message=msg)

        except Exception as e:
            logger.warning("get instance failed: {}".format(e))
            logger.exception(e)
            return Response.failed(message=e)
