"""
-*- coding:utf-8 -*-
@Project ：jxsjpt
@File    ：homeworkApi.py
@IDE     ：PyCharm
<AUTHOR>
@Date    ：2023/3/18 18:33
@desc    ：用于教师端首页左上角数据查询
"""
import time

from bson import ObjectId
from flask import request,session
from flask_restful import Resource, reqparse, inputs
from common.utils.logger import logger
from core.data.response import Response
from core.databases.orm.user.organzation_orm import *
from core.databases.orm.course.course_orm import *

class TeaIndexStatistics(Resource):
    def __init__(self):
        self.businessService = None
    def get(self):
        try:
            teacherId = session.get("id")
            logger.info("查询统计信息的教师id:{}".format(teacherId))
            data = {
                "classes": 9,
                "hours": 80,
                "students": 130,
                "experiments": 4,
                "theories": 5
            }
            res,status = TeacherIdxStatistics.find(query={"teacher_id":teacherId},need={})
            if status == CourseStatusCode.FIND_SUCCESS:
                data=res[0]['data']
            else:
                data=self.update_statistics(teacherId)
            return Response.success(data=data, message="查询成功")
        except Exception as e:
            error=str(e)+';;'+str(e.__traceback__.tb_frame.f_globals["__file__"])+';;'+str(e.__traceback__.tb_lineno)
            logger.exception(error)
            logger.warning("TeaIndexStatistics meets error: {}".format(error))
            return Response.failed(data="", message=error)


    def update_statistics(self,tid:str)->dict:
        data = {
            "classes": 9,
            "hours": 80,
            "students": 130,
            "experiments": 4,
            "theories": 5
        }
        #理论课程+实验课程数
        res,status = CourseInfo.aggregate_find([
            {
                "$match":{
                    "available":1,
                    "course_type":"理论课",
                }
            },
            {
                "$lookup": {
                    "from": "Course_Teacher",
                    "let": {"cid": {"$toString": "$_id"}},  
                    "pipeline": [
                        {
                            "$match": {                               
                                "$expr": {
                                    "$and": [
                                        { "$eq": ["$$cid", "$course_id"] },
                                        { "$in": [tid, "$teacher_id"] }
                                    ]
                                }
                            }
                        }
                    ],
                    "as": "joined_data"
                }
            },
            {
              "$unwind":"$joined_data"  
            },
            {
                "$project":{
                    "_id":{"$toString":"$_id"},
                    "course_name":1,
                    "course_type":1,
                    "course_organization":1,
                    "teachers":"$joined_data.teacher_id"
                }
            }
        ])
        if not status == CourseStatusCode.FIND_SUCCESS:
            data['theories']=0
        else:
            data['theories']=len(res)
            
        eres,status = CourseInfo.aggregate_find([
            {
                "$match":{
                    "available":1,
                    "course_type":"实验课",
                }
            },
            {
                "$lookup": {
                    "from": "Course_Teacher",
                    "let": {"cid": {"$toString": "$_id"}},  
                    "pipeline": [
                        {
                            "$match": {                               
                                "$expr": {
                                    "$and": [
                                        { "$eq": ["$$cid", "$course_id"] },
                                        { "$in": [tid, "$teacher_id"] }
                                    ]
                                }
                            }
                        }
                    ],
                    "as": "joined_data"
                }
            },
            {
              "$unwind":"$joined_data"  
            },
            {
                "$project":{
                    "_id":{"$toString":"$_id"},
                    "course_name":1,
                    "course_type":1,
                    "course_organization":1,
                    "teachers":"$joined_data.teacher_id"
                }
            }
        ])
        if not status == CourseStatusCode.FIND_SUCCESS:
            data['experiments']=0
        else:
            data['experiments']=len(eres)
        
        #计算课时和+班级数
        time_sum=0
        classids=[]
        for course in res+eres:
            #课时和
            t,status = CourseTime.find(
                query={"course_id":course['_id']},
                need={'total':1}
            )
            if status == CourseStatusCode.FIND_SUCCESS:
                time_sum+=t[0]['total']
            #班级合并
            classids=list(set(classids+course['course_organization']))
        data['hours']=time_sum
        data['classes']=len(classids)
        
        #查询学生总数
        stu_sum=0
        for cid in classids:
            cls=DBCoursePlatformStuOrg.get_one({"_id":ObjectId(str(cid))})
            if cls != None:
                stu_sum+=len(cls['students'])
        data["students"]=stu_sum
        
        #查表
        fd,status = TeacherIdxStatistics.find(
            query={"teacher_id":tid},need={}
        )
        if not status == CourseStatusCode.FIND_SUCCESS:
            TeacherIdxStatistics.insert_one(
                data={"available":1,"teacher_id":tid,"data":data}
            )
            return data
        staid=str(fd[0]['_id'])
        _,status = TeacherIdxStatistics.update_one_by_id(
            _id=staid,
            update={"data":data})
        return data
    
    @classmethod
    def update_perday(cls):
        res,_=TeacherIdxStatistics.find(query={},need={})
        for teadata in res:
            cls.update_statistics(cls,teadata["teacher_id"])
        