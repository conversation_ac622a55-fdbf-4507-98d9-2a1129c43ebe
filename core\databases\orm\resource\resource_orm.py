#!/usr/bin/env python
# -*- coding: utf-8 -*- 
# @Time   : 3/21/23 10:44 AM
# <AUTHOR> playmood
# @File   : resource_orm.py
# @desc   : ""
import pymongo

from core.databases.db_mongo import mongo, T_RESOURCE_MANAGE
from core.databases.orm.database_base import DatabaseBase
import re


class _ResourceDB(DatabaseBase):
    def __init__(self):
        DatabaseBase.__init__(self)
        self.table = T_RESOURCE_MANAGE
        self.urandom_count = 16

    def find_one(self):
        return mongo[self.table].find_one()

    # 返回所有type
    def get_resource_types(self, course_id):
        return mongo[self.table].find({"course_id": course_id}).distinct("resource_type")

    # 返回所有label
    def get_resource_labels(self, course_id):
        # 获取嵌套列表
        items = mongo[self.table].find({"course_id": course_id}, {"labels": 1, "_id": 0})
        label_map = dict()
        labels = list()
        for item in items:
            for label in item["labels"]:
                if label not in label_map:
                    labels.append(label)
                else:
                    label_map[label] = True
        return labels

    def get_resource_list_by_course_id_and_page(self, conditions, offset, limit):
        return mongo[self.table].find(conditions, {"_id": 0}).sort("add_time", pymongo.DESCENDING).skip((offset-1)*limit).limit(limit)

    def get_resource_list_by_course_id(self, course_id):
        return mongo[self.table].find({"course_id": course_id}, {"_id": 0})

    def find_by_id(self, sid):
        item = mongo[self.table].find_one({"id": sid}, {"_id": 0})
        if item:
            return True
        return False

    def find_by_id_and_course_id(self, sid, course_id):
        item = mongo[self.table].find_one({"id": sid, "course_id": course_id}, {"_id": 0})
        return item

    def find_by_name(self, sname, course_id, offset, limit):
        # if not bl_flag:
        #     item = mongo[self.table].find({"resource_name": sname})
        # else:
        item = mongo[self.table].find({"resource_name": re.compile(sname), "course_id": course_id}, {"_id": 0}).skip((offset-1)*limit).limit(limit)
        return item

    def screen_by_type(self, resource_type, offset, limit, course_id):
        item = mongo[self.table].find({"resource_type": resource_type, "course_id": course_id}, {"_id": 0}).skip((offset-1)*limit).limit(limit)
        return item

    def screen_by_labels(self, search_labels, offset, limit, conditions):
        results = mongo[self.table].find(conditions, {"_id": 0}).skip((offset-1)*limit).limit(limit)
        label_map = dict()
        data_ids = set()
        data_dict = dict()
        for result in results:
            data_dict[result["id"]] = result
            for label in result["labels"]:
                if label not in label_map:
                    label_map[label] = set()
                label_map[label].add(result["id"])
        for label in search_labels:
            if len(data_ids) == 0:
                data_ids = label_map[label]
            else:
                data_ids = data_ids.union(label_map[label])
        data = []
        for data_id in data_ids:
            data.append(data_dict[data_id])
        return data

    def delete_by_id_and_course_id(self, resource_id, course_id):
        item = mongo[self.table].delete_one({"id": resource_id, "course_id": course_id})
        # num = item[0]
        return item.deleted_count

    def add_resource(self, args):
        item = mongo[self.table].insert_one(
            {
                "id": args['id'],
                "resource_type": args['resource_type'],
                "add_user": args['add_user'],
                "add_time": args['add_time'],
                "modify_user": args['modify_user'],
                "modify_time": args['modify_time'],
                "download_count": args['download_count'],
                "resource_size": args['resource_size'],
                "resource_version": args['resource_version'],
                "resource_description": args['resource_description'],
                "resource_name": args['resource_name'],
                "icon_id": args['icon_id'],
                "course_id": args['course_id'],
                "labels": args['labels'],
                "url": args['url'],
                "file_name": args['file_name'],
                "allow":args['allow']
            }
        )
        return item.acknowledged

    # 根据id和course_id更新对应数据，默认是对已查询的数据进行修改，即 增量更新
    def update_resource(self, resource_id, course_id, dao_object):
        item = mongo[self.table].update_one({"id": resource_id, "course_id": course_id}, {"$set": dao_object})
        return item.matched_count


ResourceDB = _ResourceDB()
