#!/usr/bin/env python
# -*- coding: utf-8 -*- 
# @Time   : 5/18/21 5:48 AM 
# <AUTHOR> jackey 
# @File   : token.py
# @desc   : ""

from functools import wraps
from flask import request, session
from web.flask_app import flask_app
from common.utils.logger import logger
from core.databases.orm.auth.user_orm import DBCoursePlatformAdmin
from core.data.response import Response, StatusCode

#进行token检测,在函数执行前进行检测
def auth(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        if not flask_app.config.get("AUTH"):
            session['user'] = "guest"
            session['nick'] = "guest"
            session['email'] = "<EMAIL>"
            session['authority'] = 0
            return func(*args, **kwargs)
        token = request.headers.get('token')
        if not token:
            token = request.args.get('token')
        if not token:
            token = request.form.get('token')
        if not token:
            return Response.failed(StatusCode.AUTH_FAILED)  #如果没有获取到token,返回失败
        try:
            item = DBCoursePlatformAdmin.token_check(token)  #检测token
            if item:
                session['user'] = item['username']
                session['authority'] = item['role']
                session['nick'] = item['nick']
                session['email'] = item['email']
                session['date'] = item['date']
                return func(*args, **kwargs)
            else:
                return Response.failed(StatusCode.AUTH_FAILED)
        except Exception as e:
            logger.warning("unknown error: auth token {}".format(e))
            return Response.failed()
    return wrapper