"""
-*- coding:utf-8 -*-
@Project ：jxsjpt 
@File    ：hwDatabase.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2023/3/18 16:49 
@desc    ：定义一些共有的数据库操作方法
"""
from bson import ObjectId

from core.databases.orm.database_base import DatabaseBase
from core.databases.db_mongo import mongo


class HwDatabase(DatabaseBase):
    def __init__(self):
        DatabaseBase.__init__(self)

    def add_one(self, data):
        inserted_id = mongo[self.table].insert_one(data).inserted_id
        return inserted_id

    def add_many(self, datas):
        list = []
        for data in datas:
            inserted_id = mongo[self.table].insert_one(data).inserted_id
            list.append(inserted_id)
        return list

    def delete_by_condition(self, condition):
        return mongo[self.table].delete_many(condition)

    def update_by_condition(self, condition, data):
        result = mongo[self.table].update_many(condition, {'$set': data})
        return result.matched_count
    def increment_by_condition(self, condition, data):
        '''
        按条件自增
        '''
        result = mongo[self.table].update_one(condition, {'$inc': data})
        return result.matched_count
    def find_one(self,condition):
        return mongo[self.table].find_one(condition)
    def find_by_page(self,condition, page=1, size=10):
        skip = (page-1) * size
        return mongo[self.table].find(condition).skip(skip).limit(size)
    # def count_all(self):
    #     return mongo[self.table].estimated_document_count()
    # def count(self,condition):
    #     return mongo[self.table].count_documents(condition)

