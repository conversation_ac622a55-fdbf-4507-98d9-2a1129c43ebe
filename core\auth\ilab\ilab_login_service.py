'''
-*- coding:utf-8 -*-
@Project ：guan-ji 
@File    ：ilab_login_service.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2024/1/21 19:40 
@desc    ：国家实验平台登录处理
'''
import json
from datetime import datetime
from urllib.parse import quote, unquote
import urllib3
import hashlib

from bson import ObjectId

from common.utils.ilab_util import ilab_util
from common.utils.logger import logger
from core.databases.orm.user.ilab_config_orm import IlabConfigOrm
from core.databases.orm.user.organzation_orm import DBCoursePlatformStuOrg
from core.databases.orm.user.users_orm import DBCoursePlatformStudents, DBCoursePlatformLoginHistory
from core.hwService.hworkIssueService import HworkIssueService
from web.api.course.course import autoAddStudent

# APPID = '10000'
# SECRET = 'DDO7jw9TAZlLwRLRKSpBWXIlYZe9PKaD5Wc1S5ZQ0U8='
# API_PATH = 'http://39.105.173.29'
class ILABLoginService:

    def __init__(self):
        self.dbstudents = DBCoursePlatformStudents
        self.dbclass = DBCoursePlatformStuOrg
        self.dbloginhis = DBCoursePlatformLoginHistory
        self.dbhworkService = HworkIssueService()
    def login(self,ticket):
        #水处理，课程id
        courseId = ilab_util.ilab_course_id
        #ilab班级id
        classId = ilab_util.ilab_class_id
        #调用ilab工具
        responses = ilab_util.authentication(ticket)
        #判断是否登录成功
        code = responses["code"]

        if code != 0:
            logger.error("ILAB认证失败，返回错误码：{}，错误信息：{}".format(code,responses["msg"]))
            return code,responses["msg"]
        #登录成功后，创建学生，并且加入特定班级
        username = responses["un"]
        user = self.dbstudents.find_one({"profile_id":username})
        if not user:
            #创建学生
            logger.info("开始添加学生"+responses["un"])
            newStu = {"profile_id":username,
                                    "user_name": responses["dis"],
                                    "nick_name": "ILAB",
                                    "password": "Gj$12345",
                                    }
            stuId = self.dbstudents.create(**newStu)
            if stuId==False:
                logger.info("创建学生失败，请检查原因")
                return -1, "关基内部原因造成登录失败，正在查找问题。。。"
            logger.info("新学生id" + stuId+",并加入【ILAB】班级")
            self.dbclass.update_one({"_id":ObjectId(classId)},{"$push":
                 { "students":
                       {"id":stuId,
                        "join_date":datetime.utcnow().strftime('%Y-%m-%dT%H:%M:%SZ')
                        }}})
            # ilab_class = self.dbclass.find_one({"_id":"ILAB"})
            responses["id"] = stuId
            #为学生添加课程
            autoAddStudent([classId],courseId,None)
            # 为学生添加作业
            self.dbhworkService.issueAllHworkToStu(courseId,
                                                   classId,
                                                   [{"_id":stuId,"user_name":responses["dis"]}])
        else:#如果已存在证明以前登录过，不需要再重复以上操作
            logger.info("{}已是老用户".format(username))
            responses["id"] = str(user["_id"])
        #添加登录历史
        self.dbloginhis.add(user_id=responses["id"])
        #更新在线状态
        self.dbstudents.update(responses["id"], online_state=True)
        #要访问的课程，水处理实验课
        responses["course_id"] = courseId
        logger.info(str(responses))
        return code,responses
