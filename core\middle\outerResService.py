'''
-*- coding:utf-8 -*-
@Project ：jxsjpt 
@File    ：hworkService.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2023/3/18 17:08 
@desc    ：处理外部资源相关的业务
'''
from bson import ObjectId
from core.databases.orm.resource.outer_res_orm import OuterResOrm
from common.utils.logger import logger



class OuterResService:
    def __init__(self):
       self.dbOuterResOrm = OuterResOrm()

    def queryOuterRes(self, queryJson,page,size):
        logger.info("查询外部资源列表")
        jsonData = self.dbOuterResOrm.find_by_page(queryJson,page,size)
        total = self.dbOuterResOrm.count(queryJson)
        logger.info("外部资源总数：{}".format(total))
        logger.info("当前页：{}".format(page))

        result = {}
        dataList = []
        for data in jsonData:
            data['_id'] = str(data['_id'])
            dataList.append(data)
        logger.info("当前页数据条数：{}".format(len(dataList)))
        result['data'] = dataList
        result['total'] = total
        result['page'] = page
        result['page_size'] = size
        return result

    def queryOuterResDetail(self,_id):
        instance = self.dbOuterResOrm.find_by_id(_id)
        instance['_id'] = str( instance['_id'])
        return instance
    def saveOuterRes(self, jsonData):
        logger.info("保存外部资源：{}".format(str(jsonData)))
        #检查是否重名
        one = self.dbOuterResOrm.find_one({"resource_name":jsonData["resource_name"]})
        if one is not None:
            logger.info("外部资源名称已存在")
            return False
        return self.dbOuterResOrm.add_one(jsonData)

    def modifyOuterRes(self, jsonData,_id):
        logger.info("修改外部资源：{}||{}".format(_id,jsonData))
        success = self.dbOuterResOrm.update_by_condition({"_id": ObjectId(_id)}, jsonData)
        return success

    def deleteOuterRes(self, jsonData):
        logger.info("删除外部资源：{}".format(jsonData))
        self.dbOuterResOrm.delete_by_condition({"_id": ObjectId(jsonData)})
        return True

