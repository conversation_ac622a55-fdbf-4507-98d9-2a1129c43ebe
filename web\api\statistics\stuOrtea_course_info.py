"""
-*- coding:utf-8 -*-
@Project : jxsjpt
@File    : stuIndexStatistic
@IDE     : VSCode
<AUTHOR> xyy:)
@Date    : 2023/12/26 
@desc    ：用于学生左上统计
"""
import time

from bson import ObjectId
from flask import request, session
from flask_restful import Resource, reqparse, inputs
from common.utils.logger import logger
from core.data.response import Response
from core.databases.orm.user.organzation_orm import *
from core.databases.orm.course.course_orm import *
from core.auth.auth import *
from web.api.course.innerapi import _get_all_courses_form_tid,_get_semester_courses_form_sid,_get_semester_courses_form_tid

'''
返回本学期学生所选择的课程信息，包括课程id和课程名称
'''


class GetStuCourseInfo(Resource):

    def get(self):
        studentId = session.get("id")
        courseInfos = _get_semester_courses_form_sid(studentId)
        return Response.success(message='success', data=courseInfos)


'''
返回老师所教的课程信息，包括课程id和课程名称，写死的数据
'''
class GetTeaCourseInfo(Resource):

    def get(self):
        tid = session.get("id")
        if session.get('role') == 'teacher':
            logger.info("[GetTeaCourseInfo]:find course for "+str(tid))
            courseInfos = _get_all_courses_form_tid(tid)
        else:
            courseInfos = []
        # courseInfos = [{"_id": "6492b4e053c7d7246518ef51",
        #                 "course_name": "\u8003\u8bd5", "course_type": "理论课"},
        #                {"_id": "64ae48b7f081e7ec6834109c",
        #                 "course_name": "\u8d44\u6e90\u94fe\u63a5\u6d4b\u8bd5", "course_type": "理论课"},
        #                {"_id": "6554372542fe534d5e2c1d9c",
        #                 "course_name": "\u7406\u8bba\u8bfe\u7a0b", "course_type": "理论课"},
        #                {
        #                    "_id": "649152b4df803a39a20cf33b",
        #                    "course_name": "\u8bfe\u7a0b\u6d4b\u8bd5", "course_type": "理论课"},
        #                {
        #                    "_id": "6498ed97ced90c1ec4c0c835",
        #                    "course_name": "\u5de5\u63a7\u5b9e\u9a8c", "course_type": "实验课"}]
        return Response.success(message='success', data=courseInfos)
