'''
-*- coding:utf-8 -*-
@Project ：jxsjpt 
@File    ：teachCorrectApi.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2023/3/18 18:33 
@desc    ：用于老师进入批改界面，包括按学生批改，按题批改，
'''
from flask import request
from flask_restful import Resource

from common.utils.logger import logger
from core.data.response import Response
from core.hwService.hworkCorrectService import HworkCorrectService




class TeacherQueryCorrectApi(Resource):
    def __init__(self):
        self.businessService = HworkCorrectService()

    def get(self):

        try:
            byStu = request.args.get('byStu', type=int)  # 批改方式，1 按学生 2按题
            classId = request.args.get('classId', type=str)
            courseId = request.args.get('courseId', type=str)
            hworkNo = request.args.get('hworkNo', type=str)
            jsonData = {
                'classId':classId,
                'courseId': courseId,
                'hworkNo': hworkNo
            }
            if byStu == 1:
                result = self.businessService.toCorrectByStu(jsonData)
            else:
                result = self.businessService.toCorrectByQues(jsonData)

            return Response.success(data=result, message="")
        except Exception as e:
            logger.warning("获取批改数据异常: {}".format(e))
            logger.exception(e)
            return Response.failed(data="", message=e)
