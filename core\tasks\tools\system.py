#!/usr/bin/env python
# -*- coding: utf-8 -*- 
# @Time   : 5/10/21 6:00 AM 
# <AUTHOR> jackey 
# @File   : system.py
# @desc   : ""


import psutil
from common.utils.logger import logger
from core.databases.orm.other.system_orm import DBHSystemInfo
from web.flask_app import course_platform_celery
# from celery.contrib import rdb

def system_info():
    memory = psutil.virtual_memory()
    cpu_percent = psutil.cpu_percent(interval=1)
    return int(memory.percent), int(cpu_percent)

@course_platform_celery.task()
def t_schedule_update_system_info():
    # rdb.set_trace()
    data = {
        "memory_percent": 0,
        "cpu_percent": 0,
    }
    try:
        memory_percent, cpu_percent = system_info()
        data['memory_percent'] = memory_percent
        data['cpu_percent'] = cpu_percent
        # print(data)
    except Exception as e:
        logger.warning("get system info failed: {}".format(e))
    try:
        DBHSystemInfo.add(data)
    except Exception as e:
        logger.warning("update system info failed: {}".format(e))
