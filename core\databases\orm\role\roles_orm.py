import os
import time
import hashlib
from bson import ObjectId
from secrets import token_hex
from common.utils.logger import logger
from core.databases.db_mongo import T_ROLES
from core.databases.db_error import DatabaseError
from core.databases.orm.database_base import DatabaseBaseCollection
from enum import IntEnum


# temporary
class Permission(IntEnum):
    ADMIN = 1
    TEACHER = 2
    ASSISTANT = 4
    STUDENT = 8
    

class Role(IntEnum):
    ADMIN = 1
    TEACHER = 2
    ASSISTANT = 3
    STUDENT = 4


class _DBCoursePlatformRoles(DatabaseBaseCollection):
    """
    Example:
        {   
           id : 1,
           name : "Admin",
           state : "activate",
           permission : [1, 2]
        }       

    """
    def __init__(self):
        self.collection_name = T_ROLES
        DatabaseBaseCollection.__init__(self)
       
    def get_permission(self, filter=None):
        doc = self.find_one(filter=filter)
        if not doc:
            print(filter)
            return None
        return doc.get('permission') 
        
    def create(self, **kwargs):
        id = kwargs.get('id')
        name = kwargs.get('name')
        state = kwargs.get('state') or 'activate'
        permission = kwargs.get('permission') or []
        menu = kwargs.get('menu') or []
        
        role_info = {
                'id':id,
                'name':name,
                'state':state,
                'permission':permission,
                'menu':menu
            }
        if self.find_one({'id':id}):
            return self.replace_one({'id':id}, role_info, upsert=True)
        else:
            return self.insert_one(role_info)
              
    

DBCoursePlatformRoles = _DBCoursePlatformRoles()
    