
from core.databases.orm.role.roles_orm import DBCoursePlatformRoles as h_role, Permission as PermissionEnum, Role as RoleEnum

class Role:
    @staticmethod
    def init_roles():
        roles = [
            (RoleEnum.ADMIN, 'admin', [PermissionEnum.ADMIN]),
            (RoleEnum.TEACHER, 'teacher', [PermissionEnum.TEACHER]),
            (RoleEnum.ASSISTANT, 'assistant', [PermissionEnum.ASSISTANT]),
            (RoleEnum.STUDENT, 'student', [PermissionEnum.STUDENT]),
        ]
        
        for role in roles:
            h_role.create(
                id = int(role[0]),
                name =  role[1],
                state =  'activate',
                permission =  list(map(lambda p: int(p), role[2]))
            )
            
Role.init_roles()