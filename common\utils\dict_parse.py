from flask_restful.reqparse import Re<PERSON><PERSON><PERSON><PERSON>
from werkzeug.exceptions import BadRequest



class DictParseType(object):
    def __init__(self, args=None):
        self.args = args

    def __call__(self, value):
        if isinstance(value, dict):
            value = DictParseWrap(**value)
        try:
            return parse_args(self.args, req=value)
        except BadRequest as e:
            raise ValueError(getattr(e, 'data', {}).get('message') or e)

class DictParseWrap(object):
    def __init__(self, **kwargs):
        self.unparsed_arguments = None
        self.kwargs = kwargs

    def json(self):
        return self.kwargs


def parse_args(args, req=None, trim=True, bundle_errors=True):
    parser = RequestParser(trim=trim, bundle_errors=bundle_errors)
    parser.args = args
    return parser.parse_args(req=req)
