'''
-*- coding:utf-8 -*-
@Project ：jxsjpt 
@File    ：homeworkApi.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2023/3/18 18:33 
@desc    ：用于老师发布作业,查询已发布的作业
'''
from flask import request
from flask_restful import Resource, reqparse

from common.utils.logger import logger
from core.data.response import Response
from core.hwService.hworkIssueService import HworkIssueService

class HworkIssueApi(Resource):
    def __init__(self):
        self.businessService = HworkIssueService()
    def get(self):
        '''
        用于老师点击作业时的第一个页面展示，根据课程和班级查询作业布置情况
        '''
        try:
            class_id = request.args.get('class_id', type=str)
            course_id = request.args.get('course_id', type=str)
            result = self.businessService.queryIssuedHomework(
                {
                    'class_id': class_id,
                    'course_id': course_id
                }
            )
            return Response.success(data=result, message="")
        except Exception as e:
            logger.warning("get homework failed: {}".format(e))
            return Response.failed(message=e)
    def post(self):
        parser = reqparse.RequestParser()
        # parser.add_argument('birthday', type=inputs.date, help='生日字段验证错误！')
        # parser.add_argument('telphone', type=inputs.regex(r'1[3578]\d{9}'))
        parser.add_argument('startTime', type=str, required=True)
        parser.add_argument('endTime', type=str, required=True)
        parser.add_argument('expiredHand', type=int, required=True, choices=[0,1])
        parser.add_argument('_id', type=str, help='作业id没有上传！', required = True)
        parser.add_argument('classes', type=str, action='append', help='作业发布班级没有上传！', required = True)
        # parser.add_argument('gender', type=str, choices=['male', 'female', 'secret'])
        try:
            args = parser.parse_args()
            _id = args.get('_id')
            calsses = args.get('classes')
            startTime = args.get('startTime')
            endTime = args.get('endTime')
            expiredHand = args.get('expiredHand')
            jsonData = {
                '_id':_id,
                'classes':calsses,
                'startTime': startTime,
                'endTime': endTime,
                'expiredHand': expiredHand
            }
            returnId = self.businessService.issueHwork(jsonData)
            if returnId:
                return Response.success(data="", message=returnId)
            else:
                return Response.failed(data="", message="error")
        except Exception as e:
            logger.warning("issue failed: {}".format(e))
            logger.exception(e)
            return Response.failed(data="", message=e)
    def put(self):
        parser = reqparse.RequestParser()
        parser.add_argument('startTime', type=str, required=True)
        parser.add_argument('endTime', type=str, required=True)
        parser.add_argument('expiredHand', type=int, required=True, choices=[0, 1])
        parser.add_argument('homeworkNo', type=str, help='作业id没有上传！', required=True)
        try:
            args = parser.parse_args()
            homeworkNo = args.get('homeworkNo')
            startTime = args.get('startTime')
            endTime = args.get('endTime')
            expiredHand = args.get('expiredHand')
            jsonData = {
                'homeworkNo':homeworkNo,
                'startTime': startTime,
                'endTime': endTime,
                'expiredHand': expiredHand
            }
            returnId = self.businessService.updateIssueHwork(jsonData)
            if returnId:
                return Response.success(data=returnId, message="")
            else:
                return Response.failed(data="", message="error")
        except Exception as e:
            logger.warning("issue update failed: {}".format(e))
            logger.exception(e)
            return Response.failed(data="", message=e)
    def delete(self):
        parser = reqparse.RequestParser()

        parser.add_argument('_id', type=str, help='发布表id没有上传！', required=True)
        try:
            args = parser.parse_args()
            _id = args.get('_id')
            jsonData = {
                '_id':_id
            }
            returnId = self.businessService.deleteIssueHwork(jsonData)
            if returnId:
                return Response.success(data=returnId, message="")
            else:
                return Response.failed(data="", message="error")
        except Exception as e:
            logger.warning("test failed: {}".format(e))
            logger.exception(e)
            return Response.failed(data="", message=e)