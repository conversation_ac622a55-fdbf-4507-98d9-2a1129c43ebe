import datetime

from flask_restful import Resource
from common.utils.resourceParser import parseResource
from core.databases.orm.resource.resource_orm import ResourceDB
from core.auth.auth import *


def get_page_info(offset, limit, query_data_list, total):
    total_num = total
    total_pages = total_num // limit if total_num % limit == 0 else total_num // limit + 1
    hasNext = True if offset < total_pages else False
    hasPre = True if offset > 1 else False
    data_response = {
        "page": offset,
        "page_size": limit,
        "total": total_num,
        "total_pages": total_pages,
        "hasNext": hasNext,
        "hasPre": hasPre,
        "data": query_data_list
    }
    return data_response


class ResourceHandler(Resource):
    """ API: /api/v1/resource """

    # todo 在这里添加查找逻辑，支持通过resource_name, class, tags进行查找，添加创建时间和创建人

    #@login_required
    def get(self):
        # resource_name = request.args.get("resource_name")
        # 页码
        offset = request.args.get("page", type=int)
        # 页大小
        limit = request.args.get("page_size", type=int)
        # 默认值
        if not offset:
            offset = 1
        if not limit:
            limit = 20
        resource_name = request.args.get("resource_name")
        course_id = request.args.get("course_id")
        resource_type = request.args.get("resource_type")
        labels = request.args.getlist("labels[]")
        screen_type_flag = request.args.get("screen_type_flag")
        screen_labels_flag = request.args.get("screen_labels_flag")

        # 获得所有Type或者获得所有Label
        getType = request.args.get("getType")
        getLabel = request.args.get("getLabel")

        if getType or getLabel:
            if getType:
                try:
                    results = ResourceDB.get_resource_types(course_id)
                    data = list()
                    for result in results:
                        data.append(result)
                    return Response.success(data=data, message="获取资源类型成功")
                except Exception as e:
                    logger.warning("获取资源类型失败: {}".format(e))
                    return Response.failed(message=e)

            if getLabel:
                try:
                    results = ResourceDB.get_resource_labels(course_id)
                    return Response.success(data=results, message="获取资源标签成功")
                except Exception as e:
                    logger.warning("获取资源标签失败: {}".format(e))
                    return Response.failed(message=e)

        try:
            #只查询非图片的资源
            conditions = {
                "$and": [{"course_id": course_id}, {"allow":1}]
            }
            # 根据资源名返回记录
            if resource_name:
                resources = ResourceDB.find_by_name(resource_name, course_id, offset, limit)
            # 根据资源类型筛选记录
            elif screen_type_flag:
                resources = ResourceDB.screen_by_type(resource_type, offset, limit, course_id)
            # 根据资源标签筛选记录
            elif screen_labels_flag:
                results = ResourceDB.screen_by_labels(labels, offset, limit, conditions)
                total = ResourceDB.count(conditions)
                response_data = get_page_info(offset, limit, results, total)
                return Response.success(data=response_data, message="获取资源信息成功")
            # 未提供任何有效搜索参数，默认按照offset和limit返回一页信息
            else:
                resources = ResourceDB.get_resource_list_by_course_id_and_page(conditions, offset, limit)
                total = ResourceDB.count(conditions)
        except Exception as e:
            logger.warning("获取资源信息失败: {}".format(e))
            logger.exception(e)
            return Response.failed(message="获取资源信息失败: {}".format(e))

        data = list()
        for resource in resources:
            # del resource['_id']
            data.append(resource)
        response_data = get_page_info(offset, limit, data, total)
        return Response.success(data=response_data, message="获取资源信息成功")

    # 上传接口调用该接口
    @login_required
    def put(self):
        parser = parseResource()
        args = parser.parse_args()
        try:
            exists = ResourceDB.find_by_id_and_course_id(args['id'], args['course_id'])
            if exists:
                return Response.failed(message="该资源已存在！")
            result = ResourceDB.add_resource(args)
        except Exception as e:
            logger.warning("资源信息入库失败: {}".format(e))
            return Response.failed(message=e)
        if result:
            return Response.success(message="资源信息入库成功！")
        else:
            return Response.failed(message="资源信息入库失败！")


class ResourceIDHandler(Resource):
    """ API: /api/v1/resource/:id """

    # fixed bugs
    @login_required
    def get(self, resource_id):
        course_id = request.args.get("course_id")
        resource = ResourceDB.find_by_id_and_course_id(resource_id, course_id)
        if len(resource) != 0:
            return Response.success(data=resource, message="获取资源信息成功！")
        else:
            return Response.failed(message="获取资源信息失败! 资源不存在！")

    # todo
    # 添加创建时间
    # 添加修改者
    @login_required
    def patch(self, resource_id):
        parser = parseResource()
        args = parser.parse_args()
        course_id = args['course_id']
        dao_object = ResourceDB.find_by_id_and_course_id(resource_id, course_id)
        if not dao_object:
            return Response.failed(message="更新资源信息失败！ 资源不存在！！")
        args = dict(args)
        for k in args:
            if args[k]:
                dao_object[k] = args[k]
        #后台自己更新updatetime,utc
        dao_object["modify_time"] = datetime.datetime.utcnow().strftime('%Y-%m-%dT%H:%M:%SZ')
        try:
            resource = ResourceDB.update_resource(resource_id, course_id, dao_object)
            if resource == 0:
                return Response.failed(message="更新资源信息失败！ 资源不存在！！")
            else:
                return Response.success(message="更新资源信息成功！")
        except Exception as e:
            logger.warning("更新资源信息失败: {}".format(e))
            return Response.failed(message=e)

    @login_required
    def delete(self, resource_id):
        parser = parseResource()
        args = parser.parse_args()
        course_id = args['course_id']
        try:
            result = ResourceDB.delete_by_id_and_course_id(resource_id, course_id)
        except Exception as e:
            logger.warning("删除资源信息失败: {}".format(e))
            return Response.failed(message=e)
        if result == 0:
            return Response.failed(message="删除失败！资源不存在！")
        else:
            return Response.success(message="删除资源信息成功！")
