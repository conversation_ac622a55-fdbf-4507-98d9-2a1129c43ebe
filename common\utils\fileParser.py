#!/usr/bin/env python
# -*- coding: UTF-8 -*-
'''
@Project ：jxsjpt 
@File    ：fileParser.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2023-03-27 17:23 
'''
import werkzeug
from flask_restful import reqparse


def parseFile():
    # 请求为json格式
    parser = reqparse.RequestParser()
    parser.add_argument('id', type=str, location='form')
    parser.add_argument('url', type=str, location='form')
    parser.add_argument('resource_type', type=str, location='form')
    parser.add_argument('add_user', type=str, location='form')
    parser.add_argument('add_time', type=str, location='form')
    parser.add_argument('modify_user', type=str, location='form')
    parser.add_argument('modify_time', type=str, location='form')
    parser.add_argument('download_count', type=int, location='form')
    parser.add_argument('resource_size', type=int, location='form')
    parser.add_argument('resource_version', type=str, location='form')
    parser.add_argument('resource_description', type=str, location='form')
    parser.add_argument('resource_name', type=str, location='form')
    parser.add_argument('icon_id', type=str, location='form')
    parser.add_argument('course_id', type=str, location='form')
    parser.add_argument('labels', type=str, location='form', action='append')
    parser.add_argument('file_name', type=str, location='form')
    return parser
