#!/usr/bin/env python
# -*- coding: utf-8 -*-

import re

from bson import ObjectId
from flask import make_response, request, session
from flask_restful import Resource, reqparse
from datetime import datetime
from common.utils.logger import logger
from core.auth.auth import login_required, role_allowed
from core.data.response import Response, StatusCode

from core.databases.orm.user.users_orm import (
    DBCoursePlatformAdmins,
    DBCoursePlatformAssistants,
    DBCoursePlatformLoginHistory,
    DBCoursePlatformStudents,
    DBCoursePlatformTeachers,
    NewPassword,
    Userblock
)
from core.databases.orm.role.roles_orm import (
    DBCoursePlatformRoles as h_role,
    Permission,
)

role_to_handler = {
    "student": DBCoursePlatformStudents,
    "assistant": DBCoursePlatformAssistants,
    "teacher": DBCoursePlatformTeachers,
    "admin": DBCoursePlatformAdmins,
}


def get_handler(role: str):
    handler = role_to_handler.get(role)
    if handler == None:
        raise Exception("unsupported role provided.")
    return handler


class Login(Resource):
    @staticmethod
    def post():
        """
        post /login
        :return: 登录结果
        """
        try:
            # adjust json
            parser = reqparse.RequestParser()
            parser.add_argument("profile_id", type=str)
            parser.add_argument("password", type=str)
            parser.add_argument("role", type=str)
            args = parser.parse_args()

            profile_id = args.get("profile_id")
            password = args.get("password")
            role = args.get("role")
            if not Userblock.isRelease(profile_id):
                raise Exception(f'账号因连续输入密码错误被封禁,剩余{Userblock.remain(profile_id) + 1}分钟。')
            
            if not (profile_id and password and role):
                message = "profile_id or password check failed, please check it again. p.s. please pay attention to selecting the correct role."
                raise Exception(message)

            h_user = get_handler(role)
            if not h_user.passwd_check(profile_id=profile_id, password=password):
                times = Userblock.count(profile_id)
                if times < 5:
                    raise Exception(f"密码验证失败，剩余尝试次数：{5-times}")
                else:
                    raise Exception("密码连续输入错误5次，账号封禁30分钟。")
            Userblock.reset(profile_id)
            # 登录成功
            id = h_user.get_one({"profile_id": profile_id})["_id"]
            session["id"] = id
            session["role"] = role
            session["permission"] = h_role.get_permission({"name": role})
            h_user.update(id, online_state=True)
            DBCoursePlatformLoginHistory.add(user_id=id)

            # 检查密码过期时间
            time_msg=' '
            query_time={
                'id':ObjectId(id)
            }
            timestamp=NewPassword.get(query_time)['timestamp']
            if (datetime.now()-timestamp).total_seconds()>86400*60:#60天
                
                time_msg+="密码已过期，请重新设置"
            resp = make_response(
                Response.success(StatusCode.SUCCESS, message="login success"+time_msg,
                                 data={"id":id})
            )
            resp.set_cookie("role", role)
            return resp

        except Exception as e:
            message = "login error: {}".format(e)
            logger.exception(e)
            logger.warning(message)
            return Response.failed(StatusCode.AUTH_FAILED, message=message)

class Logout(Resource):
    @staticmethod
    @login_required
    def post():
        id = session.get("id")
        role = session.get("role")
        h_user = get_handler(role)
        h_user.update(id, online_state=False)

        session.pop("id", None)
        session.pop("role", None)
        session.pop("permission", None)

        return Response.success(message="logout success")
        # 重定向到登录页面
        # return redirect(url_for('main.index'))

class UsersMansger(Resource):
    def get(self):
        return get_handler(self.repr).get()
    
    def post(self):
        role = self.repr 
        
        parser = reqparse.RequestParser()
        parser.add_argument("profile_id", type=str)
        parser.add_argument("password", type=str)
        parser.add_argument("user_name", type=str)
        parser.add_argument("nick_name", type=str)
        parser.add_argument("activate_state", type=str)
        args = parser.parse_args()
        h_user = get_handler(role)

        # 参数校验
        if not (
            args.get("profile_id")
            and args.get("password")
            and args.get("user_name")
        ):
            msg = "profile_id / password / user_name must be assigned"
            raise Exception(msg)

        if h_user.get_one({"profile_id": args.get("profile_id")}):
            msg = "profile_id has exists."
            raise Exception(msg)

        # 插入数据库
        data = h_user.create(**args)
        return data

class Students(UsersMansger):
    repr = "student"
    general_role_allowed = ["student","assistant", "teacher", "admin"]
    
    @role_allowed(*general_role_allowed)
    def get(self):
        return super().get()

    @role_allowed(*general_role_allowed)
    def post(self):
        return super().post()


class Assistants(UsersMansger):
    repr = "assistant"
    general_role_allowed = ["teacher", "admin"]
    
    @role_allowed(*general_role_allowed)
    def get(self):
        return super().get()

    @role_allowed(*general_role_allowed)
    def post(self):
        return super().post()



class Teachers(UsersMansger):
    repr = "teacher"
    general_role_allowed = ["teacher", "admin"]
    
    @role_allowed(*general_role_allowed)
    def get(self):
        return super().get()

    @role_allowed(*general_role_allowed)
    def post(self):
        return super().post()

class Admins(UsersMansger):
    repr = "admin"
    general_role_allowed = ["admin"]
    
    @role_allowed(*general_role_allowed)
    def get(self):
        return super().get()

    @role_allowed(*general_role_allowed)
    def post(self):
        return super().post()


role_to_users = {
    "student": Students(),
    "assistant": Assistants(),
    "teacher": Teachers(),
    "admin": Admins(),
}

def get_users_manager(role: str):
    method = role_to_users.get(role)
    if method == None:
        raise Exception("unsupported role provided.")
    return method



class UserManager:
    def check_same_role(self, id):
        # same role but different id
        return session.get("role") == self.repr and session.get("id") != id
        
    def get(self, id):
        if self.check_same_role(id):
            raise Exception("cannot operate different user in same role.")
        return get_handler(self.repr).get_one({"_id": ObjectId(id)})
        
    def put(self, id, args):
        if self.check_same_role(id):
            raise Exception("cannot operate different user in same role.")
        
        role = self.repr
        h_user = get_handler(role)
        # 更新数据库
        h_user.update(id=id, **args)

    def delete(self, id):
        if self.check_same_role(id):
            raise Exception("cannot operate different user in same role.")
        return get_handler(self.repr).delete(id)

class Student(UserManager):
    repr = "student"
    general_role_allowed = ["student","assistant", "teacher", "admin"]
    
    @role_allowed(*general_role_allowed)
    def get(self, id):
        return super().get(id)

    @role_allowed(*general_role_allowed)
    def put(self, id, args):
        return super().put(id, args)

    @role_allowed(*general_role_allowed)
    def delete(self, id):
        return super().delete(id)

class Assistant(UserManager):
    repr = "assistant"
    general_role_allowed = ["assistant", "teacher", "admin"]
    
    @role_allowed(*general_role_allowed)
    def get(self, id):
        return super().get(id)

    @role_allowed(*general_role_allowed)
    def put(self, id, args):
        return super().put(id, args)

    @role_allowed(*general_role_allowed)
    def delete(self, id):
        return super().delete(id)

class Teacher(UserManager):
    repr = "teacher"
    general_role_allowed = ["teacher", "admin"]
    
    @role_allowed(*general_role_allowed)
    def get(self, id):
        return super().get(id)

    @role_allowed(*general_role_allowed)
    def put(self, id, args):
        return super().put(id, args)

    @role_allowed(*general_role_allowed)
    def delete(self, id):
        return super().delete(id)

class Admin(UserManager):
    repr = "admin"
    general_role_allowed = ["admin"]
    
    @role_allowed(*general_role_allowed)
    def get(self, id):
        return super().get(id)

    @role_allowed(*general_role_allowed)
    def put(self, id, args):
        return super().put(id, args)

    @role_allowed(*general_role_allowed)
    def delete(self, id):
        return super().delete(id)

role_to_user = {
    "student": Student(),
    "assistant": Assistant(),
    "teacher": Teacher(),
    "admin": Admin(),
}

def get_user_manager(role: str):
    method = role_to_user.get(role)
    if method == None:
        raise Exception("unsupported role provided.")
    return method


          
class Users(Resource):
    @login_required
    def get(self, role):
        """
        GET /<role>/info
        :return: 返回用户列表信息
        """
        try:
            data = get_users_manager(role).get()
            return Response.success(data=data)
        except Exception as e:
            msg = "get user list failed: {}".format(e)
            logger.warning(msg)
            return Response.failed(message=msg)


    # TODO: dev only
    # # @login_required # 登录验证
    def post(self, role):
        """
        post /<role>/create
        :return: 创建用户
        """
        try:
            msg="create successful"
            data = get_users_manager(role).post()
            return Response.success(message=msg, data=data)
        except Exception as e:
            msg = "create user failed: {}".format(e)
            logger.info(data)
            logger.exception(e)
            logger.warning(msg)
            return Response.failed(message=msg)


class User(Resource):
    # @login_required  # 登录验证
    def get(self, role, id):
        """
        GET /<role>/<id>
        :return: 获取单个用户的信息
        """
        try:
            data = get_user_manager(role).get(id)
            return Response.success(data=data)
        except Exception as e:
            msg = "get user {} failed: {}".format(id, e)
            logger.warning(msg)
            return Response.failed(message=msg)

    # @login_required  # 登录验证
    def put(self, role, id):
        """
        put /<role>/<id>
        :return: 修改用户信息的结果
        """
        try:
            msg = "change user info success"
            
            parser = reqparse.RequestParser()
            parser.add_argument("password", type=str)
            parser.add_argument("nick_name", type=str)
            # parser.add_argument("avatar", type=str)
            parser.add_argument("sex", type=str)
            parser.add_argument("mobile", type=str)
            args = parser.parse_args()
        
            get_user_manager(role).put(id, args)
            return Response.success(message=msg)
        except Exception as e:
            msg = "edit user {} failed: {}".format(id, e)
            logger.warning(msg)
            return Response.failed(message=msg)

    # @login_required  # 登录验证
    def delete(self, role, id):
        """
        post /<role>/<id>
        :return: 删除用户的结果
        """
        try:
            msg = "delete user success"
            data = get_user_manager(role).delete(id)
            return Response.success(message=msg, data=data)
        except Exception as e:
            msg = "delete user {} failed: {}".format(id, e)
            logger.warning(msg)
            return Response.failed(message=msg)
