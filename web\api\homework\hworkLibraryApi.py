'''
-*- coding:utf-8 -*-
@Project ：jxsjpt 
@File    ：homeworkApi.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2023/3/18 18:33 
@desc    ：用于作业库操作，保存，查询,作废
'''

from flask import request
from flask_restful import Resource, reqparse

from common.utils.logger import logger
from core.data.response import Response
from core.hwService.hworkService import HomeworkService

class HworkLibraryApi(Resource):
    def __init__(self):
        self.businessService = HomeworkService()

    def get(self):
        try:
            jsonData = []
            course_id = request.args.get('course_id', type=str)
            state = request.args.get('state', type=int)
            query_condition = []
            query_condition.append({'state': state})
            query_condition.append({'course_id': course_id})
            """
            条件查询
            """
            # query_condition.append({"state":1})
            homework = self.businessService.queryHomework(
                {
                    "$and": query_condition
                }
            )
            for work in homework:
                jsonData.append(
                    {
                        "_id": str(work['_id']),
                        "homework_name": work['homework_name'],
                        "course_id": work['course_id'],
                        "teacher_id": work['teacher_id'],
                        "question_count": work['question_count'],
                        "score": work['score'],
                        "state": work['state'],
                        "type": work['type']
                    }
                )
            return Response.success(data=jsonData, message="")
        except Exception as e:
            logger.warning("get homework failed: {}".format(e))
            logger.exception(e)
            return Response.failed(message=e)

    def post(self):
        parser = reqparse.RequestParser()
        parser.add_argument('course_id', type=str)
        parser.add_argument('teacher_id', type=str)
        parser.add_argument('question_list', type=dict, action="append")
        parser.add_argument('question_count', type=int)
        parser.add_argument('state', type=int)
        parser.add_argument('score', type=int)
        parser.add_argument('homework_name', type=str)
        parser.add_argument('type', type=int)
        try:
            args = parser.parse_args()
            course_id = args['course_id']
            teacher_id = args['teacher_id']
            question_count = args['question_count']
            question_list = args['question_list']
            state = args['state']
            score = args['score']
            homework_name = args['homework_name']
            type = args['type']
            hwId = str(self.businessService.saveHomework({
                'course_id': course_id,
                'teacher_id': teacher_id,
                'question_count': question_count,
                'question_list': question_list,
                'state': state,
                'homework_name': homework_name,
                'score': score,
                'is_delete': False,
                'type':type
            }))

            return Response.success(data=hwId, message="")

        except Exception as e:
            logger.error("save homework failed:".format(e))
            logger.exception(e)
            return Response.failed(message=e)

    def delete(self):
        try:
            parser = reqparse.RequestParser()
            parser.add_argument('_id', type=str)
            args = parser.parse_args()
            jsonData = "modified_count:" + str(self.businessService.invalidHomeWork(
                {'_id': args.get("_id")}
            ))
            return Response.success(data=jsonData, message="")
        except Exception as e:
            logger.warning(" delete homework failed: {}".format(e))
            logger.exception(e)
            return Response.failed(message=e)
