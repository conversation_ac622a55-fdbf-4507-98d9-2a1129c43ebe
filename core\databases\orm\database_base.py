#!/usr/bin/env python
# -*- coding: utf-8 -*- 
# @Time   : 5/10/21 6:02 AM 
# <AUTHOR> jackey 
# @File   : database_base.py
# @desc   : ""

from abc import ABC
from bson import ObjectId
from core.databases.db_mongo import mongo
from pymongo.collection import Collection

class DatabaseBaseCollection(Collection, ABC):
    def __init__(self):
        # if not self.collection_name:
        #     raise NotImplementedError("collection_name must be assigned.")
        # self.collection_name = ""
        super().__init__(mongo, self.collection_name)

    def find_by_id(self, _id):
        return self.find_one({"_id": ObjectId(str(_id))})

    def get_list(self, limit=1000):
        return self.find().limit(limit)
    
    def get_count(self, query=None):
        if query:
            return self.find(query).count()
        else:
            return self.find().count()

    def delete_by_id(self, _id):
        return self.delete_one({"_id": ObjectId(str(_id))})

    def update_by_id(self, _id, data):
        return self.update_one({"_id": ObjectId(str(_id))}, {"$set": data})


class DatabaseBase:
    def __init__(self):
        self.table = ""

    def find(self, query=None):
        return mongo[self.table].find(query)

    def find_one(self):
        return mongo[self.table].find_one()

    def find_by_id(self, _id):
        return mongo[self.table].find_one({"_id": ObjectId(str(_id))})

    def get_list(self, limit=1000):
        return mongo[self.table].find().limit(limit)

    def get_count(self, query=None):
        if query:
            return mongo[self.table].find(query).count()
        else:
            return mongo[self.table].find().count()

    def delete_by_id(self, _id):
        return mongo[self.table].delete_one({"_id": ObjectId(str(_id))})

    def delete_by_tid(self, tid):
        return mongo[self.table].delete_many({"task_id": str(tid)})

    def update_by_id(self, _id, data):
        return mongo[self.table].update_one({"_id": ObjectId(str(_id))}, {"$set": data})

    def count_all(self):
        return mongo[self.table].estimated_document_count()
    def count(self,condition):
        return mongo[self.table].count_documents(condition)
