#!/usr/bin/env python
# -*- coding: utf-8 -*- 
# @Time   : 5/18/21 6:03 AM 
# <AUTHOR> jackey 
# @File   : time_format.py
# @desc   : ""

import time
from datetime import datetime

from common.utils.logger import logger


def timestamp_to_str(timestamp):
    time_str = "-"
    try:
        if int(timestamp) == 0:
            return time_str
        time_str = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(int(timestamp)))
    except Exception as e:
        logger.warning("time format failed: timestamp to str, %s".format(e))
    return time_str

def second_to_str(seconds):
    time_m, time_s = divmod(int(seconds), 60)
    time_h, time_m = divmod(time_m, 60)
    if time_h:
        return "%dh %02dm %02ds" % (time_h, time_m, time_s)
    elif time_m:
        return "%02dm %02ds" % (time_m, time_s)
    else:
        return "%02ds" % time_s
def now_to_utc_str()->str:
    '''
    把当前时间转为utc字符串
    '''
    return datetime.now().utcnow().strftime('%Y-%m-%dT%H:%M:%SZ')
def utc_str_to_local(utc_str)->datetime:
    '''
    把utc字符串转为当前时区时间,带有时区信息
    '''
    utc_time = datetime.strptime(utc_str, '%Y-%m-%dT%H:%M:%SZ')
    return utc_time.astimezone()