from flask import request,session
from flask_restful import Resource, reqparse
from common.utils.logger import logger
from core.data.response import Response, StatusCode
from core.databases.orm.course.course_orm import *
from flask_restful import Resource, reqparse
from bson import ObjectId
from core.auth.auth import *
from web.api.course.course_student import *
from core.databases.orm.user.organzation_orm import *
from core.databases.orm.user.users_orm import *
from web.api.course.course_tool import str2utc
from .course_tool import compare_time
import re
import time
import requests

#****************************搜索课程页面(已废弃)*****************************
class CourseSearchPage(Resource):
    """API: /api/v1/course/search?course_name=<course_name>&teacher=<teacher>&organization=<organization>"""
    @login_required
    def get(self):
        course_name=request.args.get("search")
        teacher_id=session.get("id")
        
        query={}
        if not course_name == None:
            query.update({"course_name":{"$regex":course_name}})
            
        need={  "_id":1,
                "course_name":1,
                "course_image":1,
                "course_organization":1,
                "course_status":1,
                }
        try:
            data,status= CourseInfo.find(query,need)
            if status == CourseStatusCode.FIND_ERROR_NULL:
                return Response.failed(data="", message="not found")
            for item in data:
                item["_id"]=str(item["_id"])
            return Response.success(data=data,message="success")
        except Exception as e:
            logger.warning("get method meets error: {}".format(e))
            return Response.failed(data="", message=e)

##根据班级id返回名称
def getClassName(classid):
    data=[]
    for c in classid:
        res=DBCoursePlatformStuOrg.get_one({"_id":ObjectId(str(c))})
        if res == None:
            continue
        name=res['name']
        data.append(
            {
                "name":str(name),
                "id":str(c)
            }
        )
    return data
##根据教师id返回名称
def getTeacherName(teacherid):
    data=[]
    for t in teacherid:
        res=DBCoursePlatformTeachers.get_one({"_id":ObjectId(str(t))})
        if res == None:
            continue
        name=res['user_name']
        data.append(
            {
                "name":str(name),
                "id":str(t)
            }
        )
    return data


#****************************课程详细页面*****************************        
class CourseDisplayPage(Resource):
    """API /api/v1/course/NCC-1?id=<_id>"""
    @login_required
    def get(self):
        _id=ObjectId(str(request.args.get("id")))
        seme=request.args.get('semester')
        if seme == None:
            seme,status = Semesters.find(query={"isnow":1},need={})
            if status == CourseStatusCode.FIND_SUCCESS:
                seme=seme[0]['name']
        #course_name=request.args.get("course_name")
        if _id == None:
            return Response.failed(message='id null ')
        query={
            "_id":_id,
        }

        need={
                "_id":1,
                "course_name":1,
                "course_status":1,
                "course_image":1,
                "course_organization":1,
                "course_type":1,
                "course_tag":1,
                "course_introduction":1,
                "course_purpose":1,
                "associate_experment":1,
                "chapter":1,
                "open_date":1,
                "finish_date":1,
        }
        try:
            result,status=CourseInfo.find(
                query=query,
                need=need,
            )
            if not status == CourseStatusCode.FIND_SUCCESS:
                logger.error("~/api/course/course.py:106 :find data error in Table[CourseInfo]")
                return Response.failed(data="", message="ci not found")
            for item in result:
                item["_id"]=str(item["_id"])
                #添加班级名称
                classid=item['course_organization']
                classinfo=getClassName(classid)
                item['course_organization']=classinfo
                teacher,status=CourseTeacher.find(
                    query={"course_id":item["_id"]},
                    need={"_id":0,
                          "teacher_id":1}
                )
                if not status == CourseStatusCode.FIND_SUCCESS:
                    logger.error("~/api/course/course.py:119 :find data error in Table[CourseTeacher]")
                    return Response.failed(data="", message="ct not found")
                for t in teacher:
                    teacherid=t['teacher_id']
                    teacherinfo=getTeacherName(teacherid)
                    item.update({"course_teacher_id":teacherinfo})
                #添加课时信息
                if seme != None:
                    info,status=CourseTime.find(
                        query={
                            "course_id":str(_id),
                            "semester":seme
                        },
                        need={}
                    )
                    if status == CourseStatusCode.FIND_SUCCESS:
                        total=info[0]['total']
                    else:
                        total=25
                else:
                    total=25
                item.update({"course_period":total})
            return Response.success(data=result,message="success")
        except Exception as e:
            logger.exception(e)
            logger.warning("get method meets error: {}".format(e))
            return Response.failed(data="", message=e)



#**********************************课程新建parser************************************
course_creat_parser=reqparse.RequestParser()
course_creat_parser.add_argument("course_name",           required=True,location=['json',],type=str,)
#course_creat_parser.add_argument("course_id",             required=True,location=['json',],type=str,nullable=True)
#course_creat_parser.add_argument("course_teacher_name",   required=True,location=['json',],type=str,action='append',)
course_creat_parser.add_argument("course_teacher_id",                   location=['json',],type=str,action='append',nullable=True)
course_creat_parser.add_argument("course_type",           required=True,location=['json',],type=str,choices=["理论课","实验课"])
course_creat_parser.add_argument("course_tag",            required=True,location=['json',],type=str,action='append',)
course_status=[CourseStatusCode.COURSE_OPEN,CourseStatusCode.COURSE_WAIT_OPEN,CourseStatusCode.COURSE_CLOSED,]
course_creat_parser.add_argument("course_status",         required=True,location=['json',],type=int,choices=course_status,)
course_creat_parser.add_argument("course_introduction",   required=True,location=['json',],type=str,nullable=True)
course_creat_parser.add_argument("course_purpose",        required=True,location=['json',],type=str,nullable=True)
course_creat_parser.add_argument("course_image",          required=True,location=['json',],type=str,)#待定
course_creat_parser.add_argument("course_organization",   required=True,location=['json',],type=str,action='append',nullable=True)
course_creat_parser.add_argument("open_date",             required=True,location=['json',],type=str,default='2023-03-15',)
course_creat_parser.add_argument("finish_date",           required=True,location=['json',],type=str,default='2024-03-15',)
course_creat_parser.add_argument("associate_experment",   required=True,location=['json',],type=str,action='append',nullable=True)
#course_creat_parser.add_argument("chapter",               required=True,location=['json',],type=mutilist_type,action='append',)
#course_creat_parser.add_argument("last_update_user",      required=True,location=['json',],type=str,)
#*********************************测试parser**********************************
test_parser=reqparse.RequestParser()
test_parser.add_argument("test",                          required=True,location=['json',],type=int,)
#*********************************根据班级添加学生*********************************
def autoAddStudent(classes:list,course_id:str,sessionnow = None):
    stuList=[]
    for c in classes:
        res=DBCoursePlatformStuOrg.get_one({'_id': ObjectId(c)})
        if res == None:
            continue
        for stu in res['students']:
            stuList.append(stu["id"])
    res=addStu(course_id=course_id,students=stuList)
    if res:
        return True
    else:
        return False
#*********************************课程创建页面*********************************
class CourseCreatePage(Resource):
    """API: /api/v1/course/teacher_center/create_course"""
    @login_required
    def get(self):
        deafult={
            "course_open":CourseStatusCode.COURSE_OPEN,
            "course_wait_open":CourseStatusCode.COURSE_WAIT_OPEN,
            "course_closed":CourseStatusCode.COURSE_CLOSED,
            "course_type":[
                "实验课",
                "理论课",
            ],
        }
        return Response.success(data=deafult,message='get course status success')

    @login_required
    def post(self):
        try:
            teacher_creater=str(session.get("id"))
            args=course_creat_parser.parse_args()
            #args=test_parser.parse_args()
            #把教师id拿出来
            #teachers_name=args.pop("course_teacher_name")
            teacher_id=args.pop("course_teacher_id")
            if teacher_id =="" or teacher_id == None:
                teacher_id = []
            if session.get("role") == "teacher":
                if not teacher_creater in teacher_id: 
                    teacher_id.insert(0,teacher_creater)
            if None in teacher_id:
                teacher_id.remove(None)
            #课程名查重
            course_name=args['course_name']
            _,status=CourseInfo.find(
                query={"course_name":str(course_name)},
                need={}
            )
            if status == CourseStatusCode.FIND_SUCCESS:
                return  Response.failed(message='course name repeated')
            
            #把available插在最前面
            new_course={"available":CourseStatusCode.DOCUMENT_AVAILABLE,}
            new_course.update(args)
            #把两个时间量插在最后
            last_update_time,_ = str2utc(time.strftime("%Y-%m-%d-%H:%M"))
            creat_date,_ = str2utc(time.strftime("%Y-%m-%d-%H:%M"))
            new_course.update({
                "chapter":[],
                "last_update_user":str(session.get("id")),
                "last_update_time": last_update_time,
                "creat_date": creat_date,
            })
            #比对时间更新课程状态
            cres=compare_time(stm=new_course["open_date"],etm=new_course["finish_date"])
            if cres == 1 :
                new_course["course_status"]=CourseStatusCode.COURSE_WAIT_OPEN
            elif cres == 2 :
                new_course["course_status"]=CourseStatusCode.COURSE_OPEN
            else:
                new_course["course_status"]=CourseStatusCode.COURSE_CLOSED
                         
            result,status=CourseInfo.insert_one(new_course)
            if not status == CourseStatusCode.INSERT_SUCCESS:
                logger.error("~/api/course/course.py:233 :insert data error in Table[Courseinfo]")
                return Response.failed(message='creat course error')
            result=str(result)
            #更新课程-教师表
            tid_temp=[]
            for t in teacher_id:
                if str(t) == str(ObjectId(t)):
                    tid_temp.append(t)
            teacher_id=tid_temp
            update_time,_ = str2utc(time.strftime("%Y-%m-%d-%H:%M"))
            _,status=CourseTeacher.insert_one({
                    "available":1,
                    "course_id":result,
                    "teacher_id":teacher_id,
                    "update_time":update_time,
                })
            classId=new_course["course_organization"]
            if not autoAddStudent(classId,str(result),session):
                return Response.success(message="success create but add student failed")
            if not status == CourseStatusCode.INSERT_SUCCESS:
                return Response.failed(message='update ct error')
            return Response.success(data=result,message='create success')
        except Exception as e:
            logger.warning("post method meets error: {}".format(e))
            return Response.failed(data="", message=e)
#*********************************课程基本信息修改*********************************
class CourseUpdatePage(Resource):
    """API: /api/v1/course/teacher_center/update_courseinfo?course_id=121212121212"""
    @login_required
    def post(self):
        try:
            course_id=str(request.args.get("course_id"))
            course_update=course_creat_parser.parse_args()
            #teachers=course_update.pop("course_teacher_id")
            #课程名查重
            course_name=course_update['course_name']
            rawid,status=CourseInfo.find(
                query={"course_name":str(course_name)},
                need={"_id":1,}
            )
            if status == CourseStatusCode.FIND_SUCCESS:
                rawid=str(rawid[0]["_id"])
                if not rawid == course_id:
                    return  Response.failed(message='course name repeated')
            
            last_update_time,_ = str2utc(time.strftime("%Y-%m-%d-%H:%M"))
            course_update.update({
                "last_update_user":str(session.get("id")),
                "last_update_time": last_update_time,
            })
            #时间判定
            cres=compare_time(stm=course_update["open_date"],etm=course_update["finish_date"])
            if cres == 1 :
                course_update["course_status"]=CourseStatusCode.COURSE_WAIT_OPEN
            elif cres == 2 :
                course_update["course_status"]=CourseStatusCode.COURSE_OPEN
            else:
                course_update["course_status"]=CourseStatusCode.COURSE_CLOSED
            #先得到原本的班级信息
            classes,status=CourseInfo.find_by_id(
                _id=ObjectId(str(course_id)),
                need={
                    "_id":0,
                    "course_organization":1,
                }
            )
            if not status==CourseStatusCode.FIND_SUCCESS:
                return Response.success(message="cant find course")
            classes=classes['course_organization']
            #比对有无新增班级，有则添加相应学生
            newc=[]
            for c in course_update['course_organization']:
                if not c in classes:
                    newc.append(c)
            if len(newc)>0:
                autoAddStudent(newc,course_id,session)
            
            #更新老师
            newTeachers=course_update.pop("course_teacher_id")
            res,status = CourseTeacher.find(query={"course_id":course_id},need={})
            if status == CourseStatusCode.FIND_ERROR_NULL:
                return Response.failed(message="cant find C-T")
            creator = res[0]["teacher_id"][0]
            now_tea = session.get('id')
            if len(newTeachers) <= 0:
                newTeachers = [creator,now_tea]
            if not creator == newTeachers[0]:
                if creator in newTeachers:newTeachers.remove(creator)
                newTeachers.insert(0,creator)
            if not now_tea in newTeachers:
                newTeachers.append(now_tea)
                
            _,status=CourseTeacher.update_many(
                query={"course_id":course_id},
                update={"teacher_id":newTeachers}
            )
            if not status == CourseStatusCode.UPDATE_SUCCESS:
                return Response.failed(message='update ct error')
            _,status=CourseInfo.update_one_by_id(
                _id=course_id,
                update=course_update
            )
            if not status == CourseStatusCode.UPDATE_SUCCESS:
                return Response.failed(message='update ci error')
            return Response.success(data=[],message='update success')
        except Exception as e:
            logger.warning("get method meets error: {}".format(e))
            return Response.failed(data="", message=e)
#************************************删除课程******************************
class CourseDelete(Resource):
    
    """API /api/v1/course/delete?id=<course_id> """
    @login_required
    def delete(self):
        try:
            course_id=request.args.get("id")
            if course_id == None:
                raise ValueError("null course_id")
            #删除ci表相关
            _,status=CourseInfo.delete_by_id(course_id)
            if not status == CourseStatusCode.DELETE_SUCCESS:
                return Response.failed(message="delet failed:"+str(status))
            #删除ct表相关
            res,status=CourseTeacher.find(
                query={"course_id":str(course_id)},
                need={"_id":1}
            )
            if status == CourseStatusCode.FIND_SUCCESS:
                for r in res:
                    _id=ObjectId(str(r["_id"]))
                    _,status=CourseTeacher.delete_by_id(_id)
                    if not status == CourseStatusCode.DELETE_SUCCESS:
                        return Response.failed(message="delet failed:"+str(status))
            #删除cs表相关
            res,status=CourseStudent.find(
                query={"course_id":str(course_id)},
                need={"_id":1}
            )
            if status == CourseStatusCode.FIND_SUCCESS:
                for r in res:
                    _id=ObjectId(str(r["_id"]))
                    _,status=CourseStudent.delete_by_id(_id)
                    if not status == CourseStatusCode.DELETE_SUCCESS:
                        return Response.failed(message="delet failed:"+str(status))
            return Response.success(data="",message="delet success")
        except Exception as e:
            logger.warning("post method meets error: {}".format(e))
            return Response.failed(data="", message=e)


#************************************课程观看页面******************************
class CourseLearnPage(Resource):
    """API /api/v1/course/view?id=<course_id>"""

    @login_required
    def get(self):
        try:
            course_id=request.args.get("id")
            if course_id == None:
                raise ValueError('course null')
            user_id=session.get("id")
            #先搜索课程章节信息
            data,status=CourseInfo.find(
                query={"_id":ObjectId(str(course_id))},
                need={"_id":0,
                      "chapter":1,
                      "course_type":1,}
            )
            if not status == CourseStatusCode.FIND_SUCCESS:
                raise RuntimeError('find error:'+status)
            #验证用户信息是不是学生
            last_view={}
            if session.get('role') == 'student':
                query={"student_id":user_id,
                    "course_id":course_id,}
                need={"_id":0,
                    "complete":1,
                    "last_view_position":1,}
                if data[0]["course_type"] == "实验课":
                    need.update({"experiment_resource":1})
                if not user_id == None:
                    result,status=CourseStudent.find(
                        query=query,
                        need=need,
                    )
                    if not status == CourseStatusCode.FIND_SUCCESS:
                        raise RuntimeError('find error:'+status)
                    last_view.update(result)
            #合并信息
            data[0].update(last_view)
            return Response.success(data=data[0],message='success')
        except Exception as e:
            logger.warning("get method meets error: {}".format(e))
            return Response.failed(data="", message=e)
        
    #当关闭浏览页面时，发送put请求    
    # @login_required   
    def put(self):
        """API /api/v1/course/view"""
        if not session.get('role') == 'student':
            return Response.success(message='not student,no need to save')
        try:
            # course_id=request.args.get("course_id")
            # if course_id == None:
            #     raise ValueError('course null')
            user_id=session.get('id')
            if  user_id == None:
                raise ValueError('userid null')
            
            course_id=request.json["course_id"]
            chapter=request.json["chapter"]
            if chapter == None:
                chapter=[""]
            learn_time=int(request.json["time"])
            if learn_time == None:
                learn_time == 0
            res,status=CourseStudent.find(
                query={"student_id":user_id,
                        "course_id":course_id},
                need={  "_id":1,
                        "learn_total_time":1,
                        "complete":1,}
            )
            res=res[0]
            _id=ObjectId(str(res["_id"]))
            #如果不存在表，则先新建
            if not status == CourseStatusCode.FIND_SUCCESS:
                new={
                        "available":1,
                        "student_id": str(user_id),
                        "course_id": str(course_id),
                        "learn_total_time": int(learn_time),
                        "last_view_date": time.strftime("%Y-%m-%d-%H:%M"),
                        "last_view_position": str(chapter[-1]),
                        "experiment_resource": [
                        ],
                        "complete":chapter,
                        "course_final_score": 0,
                        "complete_rate": 0.1,
                        "pass": False
                }
                data,_status=CourseStudent.insert_one(new)
                if  _status == CourseStatusCode.INSERT_ERROR:
                    return Response.failed(data="", message="bad create T-cs")
                return Response.success(data=data,message="success")
            else:    
                learn_time+=res["learn_total_time"]
                #计算完成率
                total_chapter,status=CourseInfo.find(
                    query={"_id":ObjectId(str(course_id))},
                    need={"chapter":1}
                )
                total_chapter=total_chapter[0]
                count=0
                if status == CourseStatusCode.FIND_SUCCESS:
                    if "chapter" in total_chapter.keys():
                        for chp in total_chapter["chapter"]:
                            count+=len(chp['children'])
                    else:
                        count=30
                compete_rate=len(chapter)/count*1.0
                complete=[]
                complete.extend(res["complete"])
                for c in chapter:
                    if not c in complete: 
                        complete.append(c)
                if "" in complete:
                    complete.remove("")
                _,status=CourseStudent.update_one_by_id(
                    _id=_id,
                    update={"learn_total_time":learn_time,
                            "last_view_date": time.strftime("%Y-%m-%d-%H:%M"),
                            "complete_rate":compete_rate,
                            "complete":complete,
                            "last_view_position":chapter[-1]}
                )
                if not status == CourseStatusCode.UPDATE_SUCCESS:
                    raise RuntimeError('update error:'+status)
                return Response.success(message="success")
        except Exception as e:
            logger.warning("put method meets error: {}".format(e))
            return Response.failed(data="", message=e)

########################################内部接口##########################################

class InnerInterface(Resource):
    
    def getCourseClass(course_id):
        """返回课程的班级信息

        Args:
            course_id (_type_): _description_
        """
        course_id=ObjectId(str(course_id))
        classes,status=CourseInfo.find_by_id(
            _id=course_id,
            need={
                '_id':0,
                "course_organization":1,
            }
        )
        if not status == CourseStatusCode.FIND_SUCCESS:
            return status
        return classes

########################自动添加学生接口（内部）#################################

def updateNewStudentCourse(org_id:list):
    """ 
    内部接口不必try exception，出错时被catch反而会使返回的错误信息被掩盖
    try 语句只需要放在外部接口处，内部接口被调用出错时由外部接口处catch即可
    """
    # try:
    for oid in org_id:
        res,status=CourseInfo.find(
            query={
                "course_organization":str(oid)
            },
            need={
                "_id":1,
            }
        )
        if not status == CourseStatusCode.FIND_SUCCESS:
            logger.warning(f"update error: cannot find org {oid}'s course")# 没有绑定课程的时候为什么要出错呢？
            return False
            # raise RuntimeError("error when find course")
        for r in res:
            course_id=str(r["_id"])
            if not autoAddStudent(classes=[oid],course_id=course_id,sessionnow=None):
                raise RuntimeError("error when add students")
    return True
    # except Exception as e:
    #     logger.warning("post method meets error: {}".format(e))
    #     return e+"\n\r error in func<updateNewStudentCourse>"
    
########################自动删除学生课程信息接口（内部）#################################

def deleteStudentCourse(orglist:list,stulist:list):
    try:
        total_del=0
        for org in orglist:
            res,status=CourseInfo.find(
                query={
                    "course_organization":str(org)
                },
                need={
                    "_id":1,
                }
            )
            if not status == CourseStatusCode.FIND_SUCCESS:
                return 0
            for course in res:
                course_id=str(course["_id"])
                for stu in stulist:
                    delc,status=CourseStudent.delete_many(
                        query={
                            "course_id":str(course_id),
                            "student_id":str(stu)
                        }
                    )
                    if status == CourseStatusCode.DELETE_ERROR:
                        logger.warning("delete error in stu <"+str(stu)+"> course<"+course_id+">")
                    else:
                        total_del+=delc
        return total_del   
    except Exception as e:
        raise e+"\r\n error in func<deleteStudentCourse>"
    
##############################定时更新课程状态############################

def updateCourseStatus():
    logger.info('【AUTO】updateCourseStatus:【'+str(datetime.datetime.now())+'】')
    try:
        allcourse,status=CourseInfo.find(
            query={},
            need={
                "_id":1,
                "course_status":1,
                "open_date":1,
                "finish_date":1,
            }
        )
        if not status == CourseStatusCode.FIND_SUCCESS:
            raise RuntimeError("find course error: /api/course.py:603")
        for course in allcourse:
            cres=compare_time(course["open_date"],course["finish_date"])
            if cres == 1 :
                course["course_status"]=CourseStatusCode.COURSE_WAIT_OPEN
            elif cres == 2 :
                course["course_status"]=CourseStatusCode.COURSE_OPEN
            else:
                course["course_status"]=CourseStatusCode.COURSE_CLOSED
            _,status=CourseInfo.update_one_by_id(
                _id=course["_id"],
                update={
                    "course_status":course["course_status"]
                }
            )
            if not status == CourseStatusCode.UPDATE_SUCCESS:
                raise RuntimeError("update course error: /api/course.py:622")
        logger.success("daily update complete")
    except Exception as e:
        logger.error("error in func<updateCourseStatus>")
        raise e+"\r\n error in func<updateCourseStatus>"
