#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time   : 11/16/22
# <AUTHOR> jackey
# @File   : test
# @desc   : ""
from flask_restful import Resource,reqparse
from common.utils.logger import logger
from core.data.response import Response

parser = reqparse.RequestParser()
parser.add_argument('test_arg', type=str)


class TestTest(Resource):
    '''API: /api/v1/test/test_args'''
    def get(self):
        '''
        测试test参数
        '''
        args = parser.parse_args()
        test_arg = args.get('test_arg')
        try:
            if test_arg == "1":
                return Response.success(data=test_arg, message="")
            else:
                return Response.failed(data="", message="error")
        except Exception as e:
            logger.warning("test failed: {}".format(e))
            return Response.failed(data="", message=e)
