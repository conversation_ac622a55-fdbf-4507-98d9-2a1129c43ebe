'''
-*- coding:utf-8 -*-
@Project ：jxsjpt 
@File    ：stu_my_grade.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2024/1/11 16:36 
@desc    ：
'''
import time

from bson import ObjectId
from flask import request,session
from flask_restful import Resource, reqparse, inputs
from common.utils.logger import logger
from core.data.response import Response
from core.hwService.hworkStatisticsService import HwStatiSerivce
from web.api.course.innerapi import _get_semester_courses_form_sid
from core.databases.orm.exam.exam_manage_orm import DBExamPlatformAdmin
from core.databases.orm.user.organzation_orm import DBCoursePlatformStuOrg
from core.databases.orm.course.course_orm import CourseInfo,CourseStatusCode
from datetime import datetime, timedelta

# 日期格式
date_format = '%Y-%m-%dT%H:%M:%SZ'

'''
返回本学期所有课程已完成作业，考试分数
'''
class stuHwGrade(Resource):
    def __init__(self):
        self.businessService = HwStatiSerivce()
    def get(self):
        try:
            studentId = session.get("id")
            # studentId = '64915282df803a39a20cf33a'
            courseId = request.args.get('courseId', type=str)
            #获取本学期所选课程
            courseInfoall = _get_semester_courses_form_sid(studentId)
            if courseId is not None and courseId != '':  # 查询单个
                courseInfos = list(filter(lambda x:x["course_id"]==courseId,courseInfoall))
            else:# 查询所有
                courseInfos = courseInfoall
            data = self.businessService.stuHwStati(studentId,courseInfos)
            # data=[{
            #         "_id":"fasdf2345t3datg",#作业提交表id
            #         "hwName":"第一次作业",#作业名称
            #         "courseId":"346342teftgdsfghdfsh",#课程id
            #         "courseName": "理论课程",  # 课程名称
            #         "classId": "346342teftgdsfghdfsh",  # 班级id
            #         "className": "网安一班",  # 班级名称
            #         "obtainedScore":95,#分数
            #     },{
            #         "_id":"3245hb6ujfasdf2345t3datg",#作业提交表id
            #         "hwName":"第二次作业",#作业名称
            #         "courseId":"346342teftgdsfghdfsh",#课程id
            #         "courseName": "理论课程",  # 课程名称
            #         "classId": "346342teftgdsfghdfsh",  # 班级id
            #         "className": "网安一班",  # 班级名称
            #         "obtainedScore":95,#分数
            #     }]
            return Response.success(message='success',data=data)


        except Exception as e:
            logger.warning("获取数据异常: {}".format(e))
            logger.exception(e)
            return Response.failed(data="", message=e)

class stuExamGrade(Resource):
    def get(self):
        '''
        学生考试统计信息
        API:'/api/v1/student/exam/statistics'
        '''
        try:
            sid=session.get("id")
            course_id=request.args.get('courseId',type=str)
            query={
                'user_id':str(sid)
            }
            if course_id is not None and course_id !='':
                query['course_id']=str(course_id)
            res=DBExamPlatformAdmin.query_exam_record_student_list(query,{})
            result=[]
            for item in res:
                examinfo=DBExamPlatformAdmin.query_exam(ObjectId(item['exam_id']),{})
                item.pop('answer_content')
                item['_id']=str(item['_id'])
                item['examName']=examinfo['exam_name']
                item['courseId']=item['course_id']
                item['obtainedScore']=item['score']
                item['classId']=item['class_id']
                item['className']=DBCoursePlatformStuOrg.find_by_id(item['classId'])['name']
                item.pop('class_id')
                item.pop('user_id')
                item.pop('course_id')
                item.pop('exam_state')
                item.pop('correct_state')
                item.pop('score')
                courseinfo,status=CourseInfo.find_by_id(item['courseId'],need={})
                if status==CourseStatusCode.FIND_SUCCESS:
                    item['courseName']=courseinfo['course_name']
                else:
                    item['courseName']='无数据'
                result.append(item)
            return Response.success(data=result,message='')
        except Exception as e:
            logger.warning('query failed: {}'.format(e))
            logger.exception(e)
            return Response.failed(data='', message=e)
# class stuExamGrade(Resource):

#     def get(self):
#         data=[{
#                 "_id": "fasdf2345t3datg",  # 考试id
#                 "examName": "第一次考试",  # 考试名称
#                 "courseId": "346342teftgdsfghdfsh",  # 课程id
#                 "courseName": "理论课程",  # 课程名称
#                 "classId": "346342teftgdsfghdfsh",  # 班级id
#                 "className": "网安一班",  # 班级名称
#                 "obtainedScore": 92,  # 考试分数
#             },{
#                 "_id": "hkioy3fasdf2345t3datg",  # 考试id
#                 "examName": "第二次考试",  # 考试名称
#                 "courseId": "346342teftgdsfghdfsh",  # 课程id
#                 "courseName": "理论课程",  # 课程名称
#                 "classId": "346342teftgdsfghdfsh",  # 班级id
#                 "className": "网安一班",  # 班级名称
#                 "obtainedScore": 96,  # 考试分数
#             }]
#         return Response.success(message='success',data=data)