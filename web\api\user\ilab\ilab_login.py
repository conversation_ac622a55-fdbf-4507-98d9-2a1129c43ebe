'''
-*- coding:utf-8 -*-
@Project ：guan-ji 
@File    ：ilab_login.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2024/1/21 19:28 
@desc    ：国家实验平台登录请求接口
'''

from flask import make_response, request, session
from flask_restful import Resource, reqparse
from common.utils.logger import logger

from core.auth.ilab.ilab_login_service import ILABLoginService
from core.data.response import Response, StatusCode

from core.databases.orm.role.roles_orm import  DBCoursePlatformRoles as h_role





class ILABLogin(Resource):
    def __init__(self):
        self.businessService = ILABLoginService()
    def post(self):
        """
        post /ilab/login
        :return: 登录结果
        """
        try:
            # adjust json
            parser = reqparse.RequestParser()
            # parser.add_argument("profile_id", type=str)
            # parser.add_argument("password", type=str)
            parser.add_argument("role", type=str)
            parser.add_argument("ticket", type=str)
            args = parser.parse_args()

            # profile_id = args.get("profile_id")
            # password = args.get("password")
            role = args.get("role")
            ticket = args.get("ticket")
            logger.info("ILAB的ticket为【{}】".format(ticket))
            if not (ticket and role):
                logger.error("ticket不正确，登陆失败")
                message = "ticket【{}】不正确，登陆失败".format(ticket)
                Response.failed(StatusCode.AUTH_FAILED, message=message)
            code,response = self.businessService.login(ticket)
            if code !=0:
                logger.info("此次ILAB登录失败")
                return Response.failed(StatusCode.AUTH_FAILED, message=response)
            # 登录成功
            id = response["id"]
            session["id"] = id
            session["role"] = role
            session["permission"] = h_role.get_permission({"name": role})
            session["access_token"] = response["access_token"]#接口授权令牌
            session["expires_time_display"] = response["expires_time_display"]#令牌过期时间
            session["profile_id"] = response["un"]#后面上传实验数据接口需要使用
            session["user_from"] = "ilab"
            session['user_name'] = response['dis']
            returnData = {"id":id,
                          "user_name":response["dis"],
                          "courseId":response["course_id"]}
            resp = make_response(
                Response.success(StatusCode.SUCCESS, message="login success",
                                 data=returnData)
            )
            resp.set_cookie("role", role)
            return resp

        except Exception as e:
            message = "login error: {}".format(e)
            logger.exception(e)
            return Response.failed(StatusCode.AUTH_FAILED, message=message)
