from apscheduler.schedulers.background import BackgroundScheduler
import pytz
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.interval import IntervalTrigger
#from flask_apscheduler import APScheduler
from web.api.course.course import updateCourseStatus

# class SchedulerConfig(object):
#     JOBS = [
#         {
#             'id': 'job1',
#             'func': 'scheduler:test',
#             'args': (),
#             'trigger': 'interval',
#             'seconds': 2
#         }
#     ]
#     SCHEDULER_API_ENABLED = True


scheduler=BackgroundScheduler()
# scheduler2 = APScheduler()
time_zone = pytz.timezone('Asia/Shanghai')

#每天早上1:00更新课程状态
scheduler.add_job(
    func=updateCourseStatus,
    id="updatecourse",
    trigger="cron",
    hour=0,
    minute=15,
    replace_existing=True,
    timezone=time_zone
)

#每周一早上00:05更新本周的课表
from web.api.course.course_time import auto_update_week_course
scheduler.add_job(
    func=auto_update_week_course,
    id="weekly_update_scheme",
    trigger="cron",
    day_of_week='mon',
    hour=0,
    minute=5,
    replace_existing=True,
    timezone=time_zone
)

#每天早上00:10更新所有课程的进度
from web.api.course.course_time import auto_update_progress
scheduler.add_job(
    func=auto_update_progress,
    id="dayly_update_progress",
    trigger="cron",
    hour=0,
    minute=10,
    replace_existing=True,
    timezone=time_zone
)

from web.api.statistics.teaIndexStatistics import TeaIndexStatistics
#每天早上00:15更新教师统计信息
scheduler.add_job(
    func=TeaIndexStatistics.update_perday,
    id="dayly_update_statistics",
    trigger="cron",
    hour=0,
    minute=15,
    replace_existing=True,
    timezone=time_zone
)

from common.utils.logger import logger
def test():
    logger.info('【AUTO】TEST')
    
# trigger_test = IntervalTrigger(seconds=2)
# scheduler.add_job(
#     func=test,
#     trigger=trigger_test
# )