from flask import request,session
from flask_restful import Resource, reqparse
from common.utils.logger import logger
from core.data.response import Response, StatusCode
from core.databases.orm.course.course_orm import *
from flask_restful import Resource, reqparse
from bson import ObjectId
from core.auth.auth import *
from web.api.course.course_teacher import getClassName,getTeacherName
from core.databases.orm.user.organzation_orm import *
from core.databases.orm.user.users_orm import *
from .course_tool import compare_time
import re
import time
import datetime
import requests
from datetime import timedelta
from dateutil import rrule
import collections
from typing import List


def _get_semester_courses_form_sid(sid:str) -> List[dict]:
    """根据学生id获取本学期所有课程id
    Args:
        sid (str): student_id

    Returns:
        List[str]: course_id
    """
    now_semse,status=Semesters.find(query={"isnow":1},need={})
    if not status == CourseStatusCode.FIND_SUCCESS:
        return []
    now_semse=now_semse[0]['name']
    cours,status = CourseTime.find(
        query={
            "semester":now_semse
        },
        need={}
    )
    if not status == CourseStatusCode.FIND_SUCCESS:
        return []
    courses=[ cou['course_id'] for cou in cours]
    result=[]
    for cou in courses:
        _,status = CourseStudent.find(
            query={
                "course_id":cou,
                "student_id":sid
            },
            need={}
        )
        if status == CourseStatusCode.FIND_SUCCESS:
            result.append(cou)
    cinfo=[]
    for cou in result:
        res,status = CourseInfo.find_by_id(_id=cou,need={})
        if not status == CourseStatusCode.FIND_SUCCESS:
            continue
        cinfo.append({
            "course_id":cou,
            "course_name":res['course_name'],
            "course_type":res['course_type']
        })
    return cinfo

def _get_semester_courses_form_tid(tid:str) -> List[dict]:
    """根据学生id获取本学期所有课程id
    Args:
        tid (str): teacher_id

    Returns:
        List[str]: course_id
    """
    now_semse,status=Semesters.find(query={"isnow":1},need={})
    if not status == CourseStatusCode.FIND_SUCCESS:
        return []
    now_semse=now_semse[0]['name']
    cours,status = CourseTime.find(
        query={
            "semester":now_semse
        },
        need={}
    )
    if not status == CourseStatusCode.FIND_SUCCESS:
        return []
    courses=[ cou['course_id'] for cou in cours]
    result=[]
    for cou in courses:
        _,status = CourseTeacher.find(
            query={
                "course_id":cou,
                "teacher_id":tid
            },
            need={}
        )
        if status == CourseStatusCode.FIND_SUCCESS:
            result.append(cou)
    cinfo=[]
    for cou in result:
        res,status = CourseInfo.find_by_id(_id=cou,need={})
        if not status == CourseStatusCode.FIND_SUCCESS:
            continue
        cinfo.append({
            "course_id":cou,
            "course_name":res['course_name'],
            "course_type":res['course_type']
        })
    return cinfo

def _get_all_courses_form_tid(tid:str) -> List[dict]:
    """根据老师id获取所有课程id
    Args:
        tid (str): teacher_id

    Returns:
        List[str]: course_id
    """
    cours,status = CourseTeacher.find(
        query={
            "teacher_id":tid
        },
        need={
            "course_id":1
        }
    )
    if not status == CourseStatusCode.FIND_SUCCESS:
        return []
    courses=[ cou['course_id'] for cou in cours]
    cinfo=[]
    for cou in courses:
        res,status = CourseInfo.find_by_id(_id=cou,need={})
        if not status == CourseStatusCode.FIND_SUCCESS:
            continue
        cinfo.append({
            "course_id":cou,
            "course_name":res['course_name'],
            "course_type":res['course_type']
        })
    logger.info("[func:_get_all_courses_form_tid] get course for "+str(tid)+".total:"+str(len(cinfo)))
    return cinfo