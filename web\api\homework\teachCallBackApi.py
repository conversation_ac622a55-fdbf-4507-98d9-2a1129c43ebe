'''
-*- coding:utf-8 -*-
@Project ：jxsjpt 
@File    ：teachCorrectApi.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2023/3/18 18:33 
@desc    ：用于老师打回学生作业
'''
from flask_restful import Resource, reqparse

from common.utils.logger import logger
from core.data.response import Response
from core.hwService.hworkCorrectService import HworkCorrectService




class TeacherCallBackApi(Resource):
    def __init__(self):
        self.businessService = HworkCorrectService()

    def post(self):
        parser = reqparse.RequestParser()
        parser.add_argument('teacherId', type=str)# 批改老师
        parser.add_argument('note', type=str)# 作业评语
        parser.add_argument('_id', type=str)  # 提交表id
        try:
            args = parser.parse_args()
            teacherId = args.get('teacherId')
            note = args.get('note')
            _id = args.get('_id')

            jsonData = {
                'teacherId':teacherId,
                'note':note,
                '_id':_id
            }
            result = self.businessService.callBackHwork(jsonData)

            return Response.success(data=result, message="")

        except Exception as e:
            logger.warning("打回学生作业 failed: {}".format(e))
            logger.exception(e)
            return Response.failed(data="", message=e)
