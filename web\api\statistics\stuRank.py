"""
-*- coding:utf-8 -*-
@Project : jxsjpt
@File    : stuRank.py
@IDE     : VSCode
<AUTHOR> xyy:)
@Date    : 2023/12/20 
@desc    ：用于右下学生排名
"""
import time
import random
from bson import ObjectId
from flask import request,session
from flask_restful import Resource, reqparse, inputs
from common.utils.logger import logger
from core.data.response import Response
from core.databases.orm.user.organzation_orm import *
from core.databases.orm.course.course_orm import *
from core.databases.orm.user.users_orm import DBCoursePlatformStudents
from core.auth.auth import login_required

class stuRank(Resource):
    @login_required
    def get(self):
        try:
            user_id = session.get("id")
            ctype = request.args.get("type")
            type_dict = {
                'theory':"理论课",
                'experiment':"实验课"
            }
            if ctype in ['theory','experiment']:
                ctype = type_dict[ctype]
            else:
                ctype = '理论课'
            if session.get("role") == 'teacher':
                stu_ids = self.get_teacher_stu_lits(user_id,ctype)
            if session.get("role") == 'student':
                stu_ids = self.get_stu_stu_lists(user_id,ctype)
            stu_ids = sorted(stu_ids,key=lambda x: x["score"],reverse=True)
            return Response.success(message='success',data=stu_ids)
        except Exception as e:
            error=str(e)+';;'+str(e.__traceback__.tb_frame.f_globals["__file__"])+';;'+str(e.__traceback__.tb_lineno)
            logger.exception(error)
            logger.warning("stuRank meets error: {}".format(error))
            return Response.failed(data="", message=error)
        
    def get_teacher_stu_lits(self,tid:str,ctype:str = '理论课'):
        #获取所有班级id
        cres,status = CourseInfo.aggregate_find([
            {
                "$match":{
                    "available":1,
                    "course_type":ctype
                }
            },
            {
                "$lookup": {
                    "from": "Course_Teacher",
                    "let": {"cid": {"$toString": "$_id"}},  
                    "pipeline": [
                        {
                            "$match": {                               
                                "$expr": {
                                    "$and": [
                                        { "$eq": ["$$cid", "$course_id"] },
                                        { "$in": [tid, "$teacher_id"] }
                                    ]
                                }
                            }
                        }
                    ],
                    "as": "joined_data"
                }
            },
            {
              "$unwind":"$joined_data"  
            },
            {
                "$project":{
                    "_id":{"$toString":"$_id"},
                    "course_name":1,
                    "course_type":1,
                    "course_organization":1,
                    "teachers":"$joined_data.teacher_id"
                }
            }
        ])
        if status == CourseStatusCode.FIND_ERROR_NULL:
            return []
        classes = []
        for r in cres:
            classes.extend(r['course_organization'])
        classes = list(set(classes))
        return self.get_stu_from_classid(classes)
    
    def get_stu_stu_lists(self,sid:str,ctype:str = '理论课'):
        res=DBCoursePlatformStuOrg.get_one(filter={"students": {"$elemMatch": {"id": sid}}})
        if res == None:
            return []
        return self.get_stu_from_classid([str(res['_id'])])
    
    def get_stu_from_classid(self,cls_id:list):
        stu_list = []
        for cid in cls_id:
            res=DBCoursePlatformStuOrg.get_one({"_id":ObjectId(str(cid))})
            if res == None:
                continue
            sids = [ s['id'] for s in res['students']]
            #获取学生姓名和头像
            for sid in sids:
                sinfo = DBCoursePlatformStudents.get_one(filter={"_id":ObjectId(str(sid))})
                if sinfo:
                    stu_list.append({
                        "id":sid,
                        "name":sinfo['user_name'],
                        "icon":sinfo['avatar'],
                        "score":random.randint(0,100)
                    })
        return stu_list