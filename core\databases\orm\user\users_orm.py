#!/usr/bin/env python
# -*- coding: utf-8 -*-

from abc import ABC
import time
import hashlib
from datetime import datetime

from bson import ObjectId
from secrets import token_hex

from werkzeug.user_agent import UserAgent
from flask import jsonify, request
from common.utils.logger import logger
from core.databases.db_mongo import (
    T_ADMINS,
    T_ASSISTANTS,
    T_LOGINHISTORY,
    T_STUDENTS,
    T_TEACHERS,
    T_USER_BLOCK,
    T_NEWPW
)
from core.databases.db_error import DatabaseError
from core.databases.orm.database_base import DatabaseBaseCollection


class _DBCoursePlatformLoginHistory(DatabaseBaseCollection):
    def __init__(self):
        self.collection_name = T_LOGINHISTORY
        DatabaseBaseCollection.__init__(self)

    def add(self, **kwargs):
        user_id = kwargs.get("user_id")

        self.insert_one(
            {
                "user_id": user_id,
                "ip": request.remote_addr,
                "date": int(time.time()),
                "device": UserAgent(request.headers.get("User-Agent")).platform,
            }
        )


DBCoursePlatformLoginHistory = _DBCoursePlatformLoginHistory()


class DBCoursePlatformUsers(DatabaseBaseCollection, ABC):
    def __init__(self):
        DatabaseBaseCollection.__init__(self)

        self.urandom_count = 16

    def create(self, **kwargs):
        profile_id = kwargs.get("profile_id")
        password = kwargs.get("password")
        user_name = kwargs.get("user_name") or ""
        nick_name = kwargs.get("nick_name") or "佚名"
        sex = kwargs.get("sex") or ""
        school = kwargs.get("school") or ""
        college = kwargs.get("college") or ""
        major = kwargs.get("major") or ""
        activate_state = kwargs.get("activate_state") or True

        if not self.user_check(profile_id):
            raise DatabaseError("profile_id already exists")
        if profile_id and password:
            try:
                salt = token_hex()[8:16]
                item = self.insert_one(
                    {
                        "profile_id": profile_id,
                        "user_name": user_name,
                        "nick_name": nick_name,
                        "avatar": "",
                        "mobile": "",
                        "salt": salt,
                        "password": self.hash_md5(password, salt),
                        "sex": sex,
                        "school": school,
                        "college": college,
                        "major": major,
                        "online_state": False,
                        "activate_state": activate_state,
                        "create_date": datetime.utcnow().strftime('%Y-%m-%dT%H:%M:%SZ'),
                        "message": [],
                    }
                )
                return str(item.inserted_id)
            except Exception as e:
                logger.warning("user create failed: {} {}".format(profile_id, e))
                return False
        else:
            logger.warning("user create failed: invalid data")
            return False

    def get(self):
        users = self.find(
            {},
            {
                "salt": 0,
                "password": 0,
            },
        )
        result = []
        for user in users:
            user["_id"] = str(user["_id"])
            result.append(user)
        return result

    def get_one(self, filter=None):
        user = self.find_one(
            filter,
            {
                "salt": 0,
                "password": 0,
            },
        )
        if user:
            user["_id"] = str(user["_id"])
            user["login_history"] = list(DBCoursePlatformLoginHistory.find(
                {"user_id": user["_id"]},{"_id":0, "user_id":0}
            ))
        return user

    def delete(self, id):
        return self.delete_one(filter={"_id": ObjectId(id)}).deleted_count

    def update(self, id, **user_info):
        if not self.get_one(filter={"_id": ObjectId(id)}):
            raise Exception("id doesn't exists.")
            
        supported_update_attr = [
            "password",
            "nick_name",
            "avatar",
            "sex",
            "mobile",
            "online_state",
            "activate_state",
        ]

        for info in user_info:
            if info in supported_update_attr:
                if info == "password":
                    self.passwd_change(
                        self.get_one(filter={"_id": ObjectId(id)}).get("profile_id"),
                        user_info["password"],
                    )
                else:
                    self.update_one(
                        filter={"_id": ObjectId(id)},
                        update={
                            "$set": {
                                info: user_info.get(info),
                            }
                        },
                        upsert=False,
                    )

    def user_check(self, profile_id):
        if self.find_one({"profile_id": profile_id}):
            return False
        return True

    def passwd_check(self, profile_id, password):
        item = self.find_one({"profile_id": profile_id})
        if item:
            if item["password"] == self.hash_md5(password, item["salt"]):
                return True
        return False

    def passwd_change(self, profile_id, password):
        salt: str = token_hex()[8:16]
        self.update_one(
            {"profile_id": profile_id},
            {"$set": {"salt": salt, "password": self.hash_md5(password, salt)}},
        )
        return ""

    def get_or_create(self, user_info):
        profile_id = user_info["profile_id"]
        if self.user_check(profile_id):
            user_id = self.create(**user_info)
            if not user_id:
                raise Exception(f"add user {profile_id} failed.")
        else:
            user = self.get_one({"profile_id": profile_id})
            if not user:
                raise Exception(f"existed user {profile_id} get failed.")
            user_id = user["_id"] 
        return user_id
        
        
    @staticmethod
    def hash_md5(password, salt):
        md5_obj = hashlib.md5()
        md5_obj.update("{}{}".format(password, salt).encode("utf-8"))
        return md5_obj.hexdigest()


class _DBCoursePlatformStudents(DBCoursePlatformUsers):
    def __init__(self):
        self.collection_name = T_STUDENTS
        DBCoursePlatformUsers.__init__(self)


class _DBCoursePlatformAssistants(DBCoursePlatformUsers):
    def __init__(self):
        self.collection_name = T_ASSISTANTS
        DBCoursePlatformUsers.__init__(self)


class _DBCoursePlatformTeachers(DBCoursePlatformUsers):
    def __init__(self):
        self.collection_name = T_TEACHERS
        DBCoursePlatformUsers.__init__(self)


class _DBCoursePlatformAdmins(DBCoursePlatformUsers):
    def __init__(self):
        self.collection_name = T_ADMINS
        DBCoursePlatformUsers.__init__(self)


DBCoursePlatformStudents = _DBCoursePlatformStudents()
DBCoursePlatformAssistants = _DBCoursePlatformAssistants()
DBCoursePlatformTeachers = _DBCoursePlatformTeachers()
DBCoursePlatformAdmins = _DBCoursePlatformAdmins()

from datetime import datetime
from datetime import timedelta
class _Userblock(DatabaseBaseCollection):
    def __init__(self):
        self.collection_name = T_USER_BLOCK
        DatabaseBaseCollection.__init__(self)
    
    def add(self,id):
        userid = str(id)
        olds = self.find_to_list(userid)
        if len(olds) < 1 :
            self.insert_one(
                {
                    "user_id":userid,
                    "error_times":0,
                    "unblock_time":'release'
                }
            )
        
    def find_to_list(self, id):
        userid = str(id)
        res = []
        cursor = self.find({"user_id":userid})
        for user in cursor:
            res.append(user)
        return res
    
    def count(self,id):
        userid = str(id)
        old = self.find_to_list(userid)[0]
        oldtimes = old['error_times']
        if oldtimes + 1 < 5:
            self.update_by_id(_id=old['_id'],data={"error_times": oldtimes + 1})
        
        else:
            dtnow = datetime.now()
            delta = timedelta(minutes=30)
            unblock_time = dtnow + delta
            self.update_by_id(_id=old['_id'],data={"error_times": 0,"unblock_time":unblock_time.timestamp()})
        return oldtimes + 1
    
    def isRelease(self,id):
        userid = str(id)
        old = self.find_to_list(userid)
        if len(old) < 1:
            self.add(userid)
            old = self.find_to_list(userid)[0]
        else:
            old = old[0]
        if old['unblock_time'] == 'release':
            return True
        else:
            unblock_time = old['unblock_time']
            unblock_time = datetime.fromtimestamp(unblock_time)
            if datetime.now() < unblock_time:
                return False
            else:
                return True
            
    def remain(self, id):
        userid = str(id)
        old = self.find_to_list(userid)[0]
        unblock_time = old['unblock_time']
        unblock_time = datetime.fromtimestamp(unblock_time)
        remian = (unblock_time - datetime.now()).seconds // 60
        return remian
    
    def reset(self, id):
        userid = str(id)
        old = self.find_to_list(userid)[0]
        self.update_by_id(_id=old['_id'],data={"error_times": 0,"unblock_time":'release'})
    
Userblock = _Userblock()

class _NewPassword(DatabaseBaseCollection):
    def __init__(self):
        self.collection_name = T_NEWPW
        DatabaseBaseCollection.__init__(self)
    
    def add(self,query):
        try:
            result = self.add(query)
            return result
        except Exception as e:
                logger.warning("renew password failed: {}".format(e))
                return False

    def get(self,query):
        try:
            result=self.find_one(query)
            return result
        except Exception as e:
                logger.warning("get renewed password failed: {}".format(e))
                return False

NewPassword=_NewPassword()