
from common.utils.logger import logger
from core.databases.db_mongo import mongo, T_EXAM_RECORD_PAPER, T_EXAM_RECORD_STUDENT, T_EXAM_MESSAGE, T_EXAM_PAPER
from core.databases.db_error import DatabaseError
from core.databases.orm.database_base import DatabaseBase
from bson import ObjectId

'''
@description: 考试管理ORM
@author: xiong
@date: 2023/5/20
'''

class _DBExamPlatformAdmin(DatabaseBase):
    def __init__(self):
        DatabaseBase.__init__(self)
        self.table_record_paper = T_EXAM_RECORD_PAPER
        self.table_record_student = T_EXAM_RECORD_STUDENT
        self.table_message = T_EXAM_MESSAGE
        self.table_paper = T_EXAM_PAPER
        self.urandom_count = 16
    
    def query_exam(self, query_items, res_items):
        '''
        查询考试列表信息
        @param  query_items 查询条件
        @param  res_items   返回的键值
        @return 考试列表
        '''
        try:
            result = mongo[self.table_message].find_one(query_items, res_items)
            return result
        except Exception as e:
                logger.warning("query exam list failed: {} {}".format(id, e))
                return False
        
    def query_exam_list(self, query_items, res_items):
        '''
        查询考试列表信息
        @param  query_items 查询条件
        @param  res_items   返回的键值
        @return 考试列表
        '''
        try:
            result = mongo[self.table_message].find(query_items, res_items)
            return result
        except Exception as e:
                logger.warning("query exam list failed: {} {}".format(id, e))
                return False
        
    def insert_exam(self, insert_items):
        '''
        创建考试信息
        @param  insert_items    待创建试卷信息
        @return 创建执行结果
        '''
        try:
            res = mongo[self.table_message].insert_one(insert_items)
            return res
        except Exception as e:
            logger.warning("insert exam failed: {} {}".format(insert_items["exam_name"], e))
            return False

    def update_exam(self, query_items, update_items):
        '''
        更新考试信息
        @param  query_items 查询条件
        @param  update_items    待更新试卷信息
        @return 更新执行结果
        '''
        try:  
            result = mongo[self.table_message].update_many(query_items, {"$set" : update_items})
            return result
        except Exception as e:
            logger.warning("update exam failed: {} {}".format(id, e))
            return False
        
    def query_exam_record_student_list(self, query_items, res_items):
        '''
        查询考试详情列表信息
        @param  query_items 查询条件
        @param  res_items   返回的键值
        @return 考试详情列表信息
        '''
        try:
            result = mongo[self.table_record_student].find(query_items, res_items)
            return result
        except Exception as e:
                logger.warning("query exam list failed: {} {}".format(id, e))
                return False
        

    def query_exam_record_student(self, query_items, res_items):
        '''
        查询考试详情学生答题信息
        @param  query_items 查询条件
        @param  res_items   返回的键值
        @return 学生答题信息
        '''
        try:
            result = mongo[self.table_record_student].find_one(query_items, res_items)
            return result
        except Exception as e:
                logger.warning("query exam list failed: {} {}".format(id, e))
                return False
        
    def query_exam_record_student_list(self, query_items, res_items):
        '''
        查询考试详情学生答题信息
        @param  query_items 查询条件
        @param  res_items   返回的键值
        @return 学生答题信息
        '''
        try:
            result = mongo[self.table_record_student].find(query_items, res_items)
            return result
        except Exception as e:
                logger.warning("query exam list failed: {} {}".format(id, e))
                return False
        
    def update_exam_record_student(self, query_items, update_items):
        '''
        更新学生答题情况
        @param  query_items 查询条件
        @param  update_items    待更新答题情况
        @return 更新执行结果
        '''
        try:  
            result = mongo[self.table_record_student].update_many(query_items, {"$set" : update_items})
            return result
        except Exception as e:
            logger.warning("update exam failed: {} {}".format(id, e))
            return False

    def insert_exam_record_student(self, insert_items):
        '''
        创建用户答题信息
        @param  insert_items 待提交答题情况
        @return 创建执行结果
        '''
        try:
            res = mongo[self.table_record_student].insert_one(insert_items)
            return res
        except Exception as e:
            logger.warning("insert exam failed: {} {}".format(insert_items["user_id"], e))
            return False
        

    def insert_exam_record_paper(self, insert_items):
        '''
        备份试题信息
        @param  insert_items 待备份试题信息
        @return 创建执行结果
        '''
        try:
            res = mongo[self.table_record_paper].insert_one(insert_items)
            return res
        except Exception as e:
            logger.warning("insert exam failed: {} {}".format(insert_items["user_id"], e))
            return False
        
    def query_exam_record_paper(self, query_items, res_items):
        '''
        用户获取考试任务内容
        @param  query_items 查询条件
        @param  res_items   返回的键值
        @return 考试任务内容
        '''
        try:
            result = mongo[self.table_record_paper].find_one(query_items, res_items)
            return result
        except Exception as e:
                logger.warning("query exam list failed: {} {}".format(id, e))
                return False


    # other

    def query_paper(self, query_items, res_items):
        '''
        获取试卷库内容
        @param  query_items 查询条件
        @param  res_items   返回的键值
        @return 试卷库内容
        '''
        try:
            result = mongo[self.table_paper].find_one(query_items, res_items)
            return result
        except Exception as e:
                logger.warning("query exam list failed: {} {}".format(id, e))
                return False
         



DBExamPlatformAdmin = _DBExamPlatformAdmin()

