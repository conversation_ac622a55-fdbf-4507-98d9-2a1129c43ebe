import time
import requests

# l = ['673c54c04aa0e9fb8f3827fb', 
#      '673c47e44aa0e9fb8f38263a', 
#      '673c68644aa0e9fb8f382863',
#        '673c40b24aa0e9fb8f3825a4',
#          '673c68524aa0e9fb8f382861', 
#          '673c3fbd4aa0e9fb8f382589', 
#          '673c40374aa0e9fb8f382598', 
#          '673c3be84aa0e9fb8f3824ac', 
#          '673c3a1c4aa0e9fb8f382461', '673c3d5a4aa0e9fb8f382534']
# while True:
#     l2 = [x["id"] for x in requests.get("http://222.20.126.71:9090/api/daisy/instances/").json()['items']]
#     l3 = set(l2)-set(l)

#     for i in l3:
#         print(i)
#         print(requests.delete("http://222.20.126.71:9090/api/daisy/instances/"+i).text)
#     print(1)

from pymongo import MongoClient

# 创建MongoDB客户端
client = MongoClient('*************************************/')
db = client['gwj']
insl = []
# 选择集合
collection1 = db['course_platform_students']
collection2 = db['Course_Student']
all_ins = [x["id"] for x in requests.get("http://222.20.126.71:9090/api/daisy/instances/").json()['items']]

for i in range(1,26):
    _id = str(collection1.find_one({"user_name":f"信安{i}组"})["_id"])
    ins = collection2.find_one({"student_id":_id})['experiment_resource']
    try:
        ins_id = list(ins[0].values())[0]
        insl.append(ins_id)
    except: 
        print(i)


insl = insl+["673c93cb4aa0e9fb8f382911"]
l = set(all_ins)-set(insl)

for i in l:
    print(i)
    r = requests.delete("http://222.20.126.71:9090/api/daisy/instances/"+i).text
    time.sleep(3)
    print(r)