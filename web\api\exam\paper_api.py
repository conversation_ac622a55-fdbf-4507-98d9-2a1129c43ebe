import datetime
from flask import request,session
from flask_restful import Resource,reqparse
from common.utils.logger import logger
from core.data.response import Response, StatusCode
from core.databases.orm.exam.paper_orm import _Exam_paper, Exam_paper
from common.utils.dict_parse import DictParseType
from bson import ObjectId
from flask_restful.reqparse import Argument
from core.auth.auth import *
import json


parser = reqparse.RequestParser()
parser.add_argument('paper_id', location=['json',],type=str)
parser.add_argument('id_list',type=str,action="append")
parser.add_argument('course_id', type=str)
parser.add_argument('paper_name', type=str)
parser.add_argument('paper_content', type=dict,action="append")
parser.add_argument('create_time', type=str)
parser.add_argument('create_user', type=str)
parser.add_argument('update_time', type=str)
parser.add_argument('update_user', type=str)
parser.add_argument('is_delete', type=str)
parser.add_argument('paper_id', type=str)
parser.add_argument('id_list',type=str,action="append")
parser.add_argument('get_list',type=bool)
parser.add_argument('key_word',type=str,action='append')

# class ChangePaperV1(Resource):
#     ''' API: /api/v1/exam/paper/change_paper'''
#     def post(self):
#         '''
#         修改试卷
#         '''
#         args = parser.parse_args()

#         paper_id=args["paper_id"]
#         paper_name=args["paper_name"]
#         paper_content=args["paper_content"]
#         update_user=args["update_user"]
#         exam_paper=_Exam_paper()
#         result=exam_paper.change(
#                 paper_id= paper_id,
#                 paper_name = paper_name,
#                 paper_content = paper_content,
#                 update_user = update_user
#             )


#         try:
#             if result:
#                 return Response.success(data="Change paper success", message="成功修改试卷")
#             if result==100:
#                 return Response.failed(message="Change paper failed paper is deleted!", data=paper_id)
#             else:
#                 return Response.failed(message="Change paper failed paper_id not found!", data=paper_id)

#         except Exception as e:
#             logger.warning("test failed: {}".format(e))
#             return Response.failed(data="", message=e)

class DeletePaperV1(Resource):
    ''' API: /api/v1/exam/paper/del_paper'''
    @login_required
    def delete(self):
        '''
        删除试卷
        '''
        access_role = session.get("role")
        if access_role == 'student':
            return Response.failed(data='get exam list fail', message='student is not allowed')
        args = parser.parse_args()
        paper_id=args["paper_id"]
        exam_paper=_Exam_paper()

        try:
            if exam_paper.delete_by_id(_id=paper_id):
                return Response.success(data="Delete paper success", message="成功删除试卷")
            else:
                return Response.failed(message="Delete paper failed ,check the paper_id!", data=paper_id)

        except Exception as e:
            logger.warning("test failed: {}".format(e))
            logger.exception(e)
            return Response.failed(data="", message=e)

class DeletePaper_numV1(Resource):
    ''' API: /api/v1/exam/paper/del_paper_num'''
    @login_required
    def delete(self):
        '''
        批量删除试卷
        '''
        access_role = session.get("role")
        if access_role == 'student':
            return Response.failed(data='get exam list fail', message='student is not allowed')
        args = parser.parse_args()
        id_list=args["id_list"]
        exam_paper = _Exam_paper()


        flag,paper_ids=exam_paper.delete_by_nums(paper_ids=id_list)

        try:
            if flag:
                return Response.success(data="Delete papers success", message="成功删除试卷")
            else:
                return Response.failed(message="Delete papers failed", data=paper_ids)

        except Exception as e:
            logger.warning("test failed: {}".format(e))
            logger.exception(e)
            return Response.failed(data="", message=e)


class ExamPaperV1(Resource):
    """
    新建试卷
    :params
    course_id:_id,课程id
    exam_id:_id,考试id
    paper_name:str,试卷名
    paper_content:[str],题目列表
    create_time:str,创建时间
    create_user:_id,创建人
    """
    @login_required
    def post(self):
        access_role = session.get("role")
        if access_role == 'student':
            return Response.failed(data='get exam list fail', message='student is not allowed')
        try:
            """
            接收参数
            """
            args=parser.parse_args()
            course_id=args['course_id']
#            exam_id=args['exam_id']
            paper_name=args['paper_name']
            paper_content=args['paper_content']
            create_time=args['create_time']
            create_user=args['create_user']
            "新增试卷"
            result=str(Exam_paper.insert({
                'create_time':str(create_time),
                'create_user':ObjectId(create_user),
                'course_id':ObjectId(course_id),
#                'exam_id':ObjectId(exam_id),
                'paper_name':str(paper_name),
                'paper_content':paper_content,
                'is_delete':False
            }).inserted_id)
            return Response.success(data=result)
        except Exception as e:
            logger.warning("get exam questions failed: {}".format(e))
            logger.exception(e)
            return Response.failed(message=e)

    """
    查看试卷详情
    :params
    get_list false
    paper_id:oid,试卷id
    查看试卷列表
    get_list true
    course_id:oid,课程id
    """
    @login_required
    def get(self):
        result=[]
        try:
            """接收参数"""
            paper_id = request.args.get('paper_id')
            if paper_id is not None:
                result=Exam_paper.find({
                    "_id":ObjectId(paper_id)
                })
                if(result is None):
                    return Response.failed(message="NOT FOUND")
                result["_id"]=str(result["_id"])
                result["course_id"]=str(result["course_id"])
                result["create_user"]=str(result["create_user"])
                result["create_time"]=str(result["create_time"])
                if("update_user" in result and "update_time" in result):
                    result["update_user"]=str(result["update_user"])
                    result["update_time"]=str(result["update_time"])
                return Response.success(data=result)
            else:
                course_id=request.args.get('course_id')
                if course_id is None:
                    return Response.failed(message='Wrong Params')
                paper_list=Exam_paper.find_list({"$and":[
                    {"course_id":ObjectId(course_id)},
                    {"is_delete":False}
                ]})
                for paper in paper_list:
                    result.append({
                        "_id":str(paper["_id"]),
                        "course_id":str(paper["course_id"]),
                        "paper_name":paper["paper_name"],
                        "create_user":str(paper["create_user"]),
                        "create_time":str(paper["create_time"])
                    })
                    if("update_user" in result and "update_time" in result):
                        result[-1]["update_time"]=str(paper["update_time"])
                        result[-1]["update_user"]=str(paper["update_user"])
                return Response.success(data=result)
        except Exception as e:
            logger.warning("get exam questions failed: {}".format(e))
            logger.exception(e)
            return Response.failed(message=e)
    """
    更新试卷
    :params
    paper_id:oid,更新对象
    update_user:oid,更新用户
    update_time:$time,更新时间
    paper_name/paper_content:同上
    """
    @login_required
    def patch(self):
        access_role = session.get("role")
        if access_role == 'student':
            return Response.failed(data='get exam list fail', message='student is not allowed')
        try:
            args=parser.parse_args()
            result=Exam_paper.update({
                "_id":ObjectId(args["paper_id"])},
                {"$set":{
                    "paper_name":args["paper_name"],
                    "paper_content":args["paper_content"],
                    "update_user":args["update_user"],
                    "update_time":args["update_time"]}})
            if(result.matched_count==0):
                    return Response.failed(message="NOT FOUND")
            return Response.success(data=result.modified_count)
        except Exception as e:
            logger.warning("get exam questions failed: {}".format(e))
            logger.exception(e)
            return Response.failed(message=e)
