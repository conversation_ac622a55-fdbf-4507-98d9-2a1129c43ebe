"""
-*- coding:utf-8 -*-
@Project ：jxsjpt 
@File    ：question.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2023/3/18 11:14 
@desc    ：封装习题对象
"""


class Question:
    def __init__(self, data):
        self._id = data['']  # 题号
        self.question_type = data['']  # 类型 1单选题  2多选题  3判断题 4简答题 5报告题
        self.course_id = data['']  # 课程id
        self.knowledge_label = data['']  # 知识点标签
        self.degree = data['']  # 难易程度  1简单  2中等   3困难
        self.construct_time = data['']  # 创建时间
        self.builder = data['']  # 创建者
        self.chapter_id = data['']  # 所属章节
        self.question_content = data['']  # 题干/报告名
        self.question_options = data['']  # 题目选项/报告简介
        self.answer_content = data['']  # 选项内容/报告附件（资源url）
        self.ref_answer = data[''] # 参考答案


    def toDbType(self):
        return self.__dict__

