'''
-*- coding:utf-8 -*-
@Project ：jxsjpt 
@File    ：hworkIssueService.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2023/3/19 16:38 
@desc    ：此类是处理作业发布相关，老师建立作业后，点击发布按钮，把作业推送给所有同学
'''
from bson import ObjectId

from core.data.handInHwork import HandInHwork
from core.data.handInDetail import HandInDetail
from core.databases.orm.homework.homework_orm import HomeworkOrm
from core.databases.orm.homework.homeworkDetail_orm import HomeworkDetailOrm
from core.databases.orm.homework.handInHwork_orm import HandInHworkOrm
from core.databases.orm.homework.handInDetail_orm import HandInDetailOrm
from core.databases.orm.homework.classHIHw_orm import ClassHIHwOrm
from common.utils.logger import logger
from core.databases.orm.user.organzation_orm import DBCoursePlatformStuOrg as stu_org #可以根据班级id获取学生
class HworkIssueService:
    def __init__(self):
        self.dbhwOrm = HomeworkOrm()
        self.dbhwdOrm = HomeworkDetailOrm()
        self.dbhInOrm = HandInHworkOrm()
        self.dbhIDetailOrm = HandInDetailOrm()
        self.dbclassHIOrm = ClassHIHwOrm()

    def queryIssuedHomework(self,jsonData):
        class_id = jsonData['class_id']
        course_id = jsonData['course_id']
        condition = [ {'courseId':course_id}]
        if class_id is not None:
            condition.append({'classId':class_id})
        classHandIns = self.dbclassHIOrm.find({
                    "$and": condition
                })
        result = []
        for classHandIn in classHandIns:
            classHandIn['_id'] = str(classHandIn['_id'])
            result.append(classHandIn)
        return result


    def issueHwork(self,jsonData):
        '''
        方法功能：发布作业，通过前端传过来的homeworkNo,classid
                发布班级作业，classid可能不止一个
        '''
        logger.info("开始执行发布作业，作业为：{}".format(jsonData['_id']))
        logger.info("开始执行发布作业，待发布班级为为：{}".format(str(jsonData['classes'])))
        _id = jsonData['_id']
        returnMessage = ""
        #第一步查出作业信息
        homework = self.dbhwOrm.find_by_id(_id)
        hworkDetails = self.dbhwdOrm.find({'homeworkNo':_id})
        hwdList = [item for item in hworkDetails]
        #第二步，查出班级学生
        classIds = jsonData['classes']
        for classId in classIds:
            logger.info("开始为班级{}发布作业".format(classId))
            #首先判断是否已发布过，发布过的不在发布
            resultList = self.dbclassHIOrm.find({'$and': [
                {'classId': classId},
                {'homeworkNo': _id},
                {'courseId': homework['course_id']}
            ]})
            issued = False
            for obj in resultList:
                issued = True
            if issued:
                logger.info("{}班级已经发布过作业{}，不再次发布".format(classId,_id))
                returnMessage = returnMessage+"{}班级已经发布过作业{}，不再次发布".format(classId,_id)+" | "
                continue
            classInfo = stu_org.get_one({'_id': ObjectId(classId)})
            students = classInfo['students']
            className = classInfo['name']
            # list =[{'_id':classId+'stu001'},{'_id':classId+'stu002'},{'_id':classId+'stu003'}]

            if len(students) == 0:
                logger.info("{}班级人数为0,不执行发布操作".format(classId))
                returnMessage = returnMessage + "{}班级人数为0,不执行发布操作".format(classId) + " | "
                continue
            #第三步，给每个同学布置作业，操作作业提交和作业提交明细表
            classHandIn = {
                'homeworkNo':str(homework['_id']),
                'homeworkName': homework['homework_name'],
                'classId':classId,
                'studentCount':len(students),
                'courseId':homework['course_id'],
                'handedCount':0,
                'correctCount': 0,
                'startTime': jsonData['startTime'],
                'endTime': jsonData['endTime'],
                'expiredHand': jsonData['expiredHand'],
                'state': 2, # 2已发布 3已结束
                'className':className,
                'type':homework['type']
            }
            # self.dbclassHIOrm.update_by_condition({}, {'type': 1})
            self.dbclassHIOrm.add_one(classHandIn)
            logger.info("开始为班级{}的学生发布作业".format(classId))
            for student in students:
                logger.info("  开始为学生{}发布作业".format(str(student['id'])))
                homework['classId'] = classId
                homework['studentId']=str(student['id'])
                # stuName=DBCoursePlatformStudents.get_one({"_id":ObjectId(str(student))})
                # homework['studentName'] = " "
                # homework['studentName'] =student['user_name']
                # homework['studentId'] = str(student['_id'])
                homework['studentName'] = student['user_name']
                handInHwork = HandInHwork(homework)
                # self.dbhInOrm.update_by_condition({}, {'type': 1})
                handInHwork_id = self.dbhInOrm.add_one(handInHwork.toDbType())
                logger.info("    开始为学生{}建立作业习题明细".format(str(student['id'])))
                for hwd in hwdList:
                    hwd['handInHworkNo'] = str(handInHwork_id)
                    handInDetail = HandInDetail(hwd)
                    self.dbhIDetailOrm.add_one(handInDetail.toDbType())
            logger.info("{}班级发布过作业{}成功".format(classId,_id))
            returnMessage = returnMessage + "{}班级发布过作业{}成功".format(classId,_id) + " | "
        return returnMessage
    def updateIssueHwork(self, jsonData):
        homeworkNo = jsonData.pop('homeworkNo')
        return self.dbclassHIOrm.update_by_condition({'homeworkNo':homeworkNo},jsonData)

    def deleteIssueHwork(self, jsondata):
        logger.info("开始删除本次作业发布，发布表id：{}".format(jsondata['_id']))
        classHIn = self.dbclassHIOrm.find_by_id(jsondata['_id'])
        homeworkNo = classHIn['homeworkNo']
        classId = classHIn['classId']
        courseId = classHIn['courseId']
        handInlist = self.dbhInOrm.find({'$and': [
            {'classId': classId},
            {'courseId': courseId},
            {'hworkNo': homeworkNo}
        ]})
        logger.info("开始删除学生作业布置信息")
        for handIn in handInlist:
            logger.info("  删除[{}]同学的作业布置".format(handIn['studentName']))

            self.dbhIDetailOrm.delete_by_condition({'handInHworkNo': str(handIn['_id'])})
            self.dbhInOrm.delete_by_condition({'_id': handIn['_id']})
        logger.info("删除班级作业布置信息：{}".format(jsondata['_id']))
        self.dbclassHIOrm.delete_by_condition({'_id': ObjectId(str(jsondata['_id']))})
        return "删除完毕"
    def issueAllHworkToStu(self, course_id, class_id, stus):
        '''
        对于中途加入某个班级的学生，完善作业布置
        stus：list[{"_id":"学生id","user_name":"学生名字"}]
        '''
        #查询出班级作业
        clasHworks = self.dbclassHIOrm.find({'$and':
                                    [{'classId': class_id},
                                        {'courseId': course_id}
                                     ]})
        for claHwork in clasHworks:
            #查出作业信息，以及作业详情
            homework = self.dbhwOrm.find_by_id(claHwork["homeworkNo"])
            hworkDetails = self.dbhwdOrm.find({'homeworkNo': claHwork["homeworkNo"]})
            #为学生布置作业
            for stu in stus:
                logger.info("  开始为学生{}发布作业：{}".format(str(stu["_id"]),claHwork["homeworkName"]))
                homework['classId'] = class_id
                homework['studentId'] = stu["_id"]
                homework['studentName'] = stu['user_name']
                handInHwork = HandInHwork(homework)
                handInHwork_id = self.dbhInOrm.add_one(handInHwork.toDbType())
                logger.info("    开始为学生建立作业习题明细")
                for hwd in hworkDetails:
                    hwd['handInHworkNo'] = str(handInHwork_id)
                    handInDetail = HandInDetail(hwd)
                    self.dbhIDetailOrm.add_one(handInDetail.toDbType())
            #更新班级学生人数
            self.dbclassHIOrm.increment_by_condition({"_id":claHwork["_id"]}, {'studentCount': 1})
        return
