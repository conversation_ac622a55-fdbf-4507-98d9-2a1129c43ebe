'''
-*- coding:utf-8 -*-
@Project ：jxsjpt 
@File    ：handInHwork.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2023/3/18 16:19 
@desc    ：作业提交表
'''
class HandInHwork:
    def __init__(self,data):
        # self._id = data['']#作业提交表主键
        self.hworkNo = str(data['_id'])#课程作业表主键
        self.note = None  # 作业批改批注
        self.state = 1  # 提交状态 1未提交/2保存未提交/3已提交/4打回/5已批改
        self.finishTime = None# 提交时间
        self.studentId = data['studentId']  #学生id
        self.studentName = data['studentName']# 学生姓名
        self.classId = data['classId']  # 班级id
        self.teacherId = data['teacher_id']# 发布作业老师id
        self.courseId = data['course_id']  # 课程id
        self.questionCount = data['question_count']  # 题数
        self.handInCount = 0 # 用于按题批改，记录已批改题数
        self.hwName = data['homework_name']# 作业名称
        self.score = data['score']# 总分数
        self.obtainedScore = 0# 所得总分数
        self.correctTeaId = None #批改老师
        self.correctTime = None #批改时间
        self.type=data['type']  # 作业类型，1普通作业，2报告作业
    def toDbType(self):
        return self.__dict__