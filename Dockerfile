FROM python:3.8-slim-buster

# 设置时区
ENV TZ=Asia/Shanghai

# 将工作目录设置为 /app
WORKDIR /app

# 将当前目录中的内容复制到 /app 中
COPY . /app

# 安装应用程序所需的依赖项
RUN pip install -r r.txt && \
    apt-get update && apt install -y cron && service cron start

#设置时区
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo '$TZ' > /etc/timezone


# 在容器启动时运行应用程序
CMD ["gunicorn", "course_platform_manager:flask_app", "--bind", "0.0.0.0:50020", "--reload"]
