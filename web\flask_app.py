#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time   : 5/10/21 1:17 AM
# <AUTHOR> jackey
# @File   : flask_app.py
# @desc   : "项目主文件"

import sys
from datetime import timedelta
from flask import Flask, request, session
# from celery import Celery
from flask_cors import CORS
from flask_session import Session
# from secrets import token_urlsafe
from config import Config
from common.utils.logger import logger
from core.data.response import Response
# from core.databases.orm.role.roles_orm import Permission

def create_app():
    try:
        _app = Flask(__name__, static_folder="../../dist/", static_url_path='', template_folder="../../dist")

        _app.config.from_object(Config)
        # _app.config['CELERY_BROKER_URL'] = "redis://{}:{}/{}".format(
        #     _app.config.get("REDIS_HOST"), _app.config.get("REDIS_PORT"), _app.config.get("REDIS_DB"),
        #     )
        if _app.config.get('MONGO_USER') and _app.config.get('MONGO_PASSWD'):
            _app.config['MONGO_URI'] = "mongodb://{}:{}@{}:{}/{}?authSource=admin".format(
                _app.config.get("MONGO_USER"), _app.config.get("MONGO_PASSWD"),
                _app.config.get("MONGO_HOST"), _app.config.get("MONGO_PORT"), _app.config.get("MONGO_DB"),
            )
        else:
            _app.config['MONGO_URI'] = "mongodb://{}:{}/{}".format(
                _app.config.get("MONGO_HOST"), _app.config.get("MONGO_PORT"), _app.config.get("MONGO_DB"),
                )
        # _app.config['SECRET_KEY'] = token_urlsafe()
        CORS(_app, supports_credentials=True)
        return _app
    except Exception as e:
        logger.error("create flask app error: {}".format(e))
        sys.exit(0)

def login_check(app):
    @app.before_request
    def before_request():
        # 在请求到达视图函数之前执行的操作
        # print('Before request:', request.path)
        #添加一个第三方登录支持,ilab为国家实验平台
        if request.path in ['/api/v1/user/login', '/api/v1/user/logout', '/api/v1/user/ilab/login']:
            return 
        session_id = request.cookies.get('session')
        if not session_id or not session.get('id'):
            return Response.failed(message="please login first.", code=401)
    #         # session["id"] = "64916ee4297af29dcf4764bc"
    #         # session["role"] = "student"
    #         # session["permission"] = 8

    # @app.after_request
    # def after_request(response):
    #     # 在响应返回给客户端之前执行的操作
    #     # print('After request:', response.status_code)
    #     # response.set_cookie("role", "student")
    #     return response

    return app
    
flask_app = create_app()
flask_app.config['TRUSTED_PROXIES']=flask_app.config.get('SERVER_HOST')
flask_app = login_check(flask_app)

flask_app.config['SESSION_TYPE'] = 'filesystem'
flask_app.config['SESSION_PERMANENT'] = True
flask_app.config['SESSION_USE_SIGNER'] = True
# flask_app.config['SESSION_KEY_PREFIX'] = ''

Session(flask_app)

flask_app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(days=30)
flask_app.secret_key = "course_platform"
# TODO: dev only
flask_app.config['SESSION_COOKIE_HTTPONLY'] = False#True
flask_app.config['SESSION_COOKIE_SECURE'] = False#True


# course_platform_celery = Celery(flask_app.name, broker=Config.BROKER_URL,include=['core.tasks.packet_analysis.packet_fetch','core.tasks.tools.system'])
# course_platform_celery = Celery(flask_app.name, broker=Config.BROKER_URL)
# course_platform_celery.conf.update(flask_app.config)
# course_platform_celery.conf.update(Config)
flask_app.app_context().push()
