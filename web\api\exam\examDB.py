from common.utils import const
const.Exam_paper={
    "create_time":"str:创建时间",
    "create_user":"ObjectId:创建人",
    "course_id":"ObjectId:课程id",
    "paper_name":"str:试卷名",
    "paper_content":"list:试卷内容",
    "is_delete":"bool:是否删除",
    "update_time":"str:更新时间",
    "update_user":"str:更新人"
}
const.exam_message={
    "course_id":"ObjectId:课程id",
    "exam_name":"str:考试名称",
    "class_id":"list:班级列表",
    "begin_time":"date:开始日期",
    "end_time":"date:结束日期",
    "create_user":"ObjectId:创建人",
    "create_time":"date:创建日期",
    "update_user":"ObjectId:更新人",
    "update_time":"date:更新日期",
    "is_delete":"bool:是否删除"
}
const.exam_record_paper={
    "course_id":"ObjectId:课程id",
    "create_user":"ObjectId:创建人",
    "create_time":"date:创建日期",
    "update_user":"ObjectId:更新人",
    "update_time":"date:更新日期",
    "paper_content":"list:试卷内容",
    "exam_id":"ObjectId:考试id"
}
const.exam_record_student={
    "user_id":"str:学生id",
    "course_id":"str:课程id",
    "exam_id":"str:考试id",
    "answer_content":"list:学生答案",
    "exam_state":"bool:是否参加考试",
    "correct_state":"bool:是否批改",
    "score":"int:分数"
}