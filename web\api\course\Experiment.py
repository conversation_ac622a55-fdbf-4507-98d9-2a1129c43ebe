from flask import request,session
from flask_restful import Resource, reqparse
from common.utils.logger import logger
from core.data.response import Response, StatusCode
from core.databases.orm.course.course_orm import *
from flask_restful import Resource, reqparse
from bson import ObjectId
from core.auth.auth import *
import re
import time

EXPERIMENT_TYPE=[33,44]

create_experiment_parser=reqparse.RequestParser()
create_experiment_parser.add_argument("experiment_name",           required=True,location=['json',],type=str,)
create_experiment_parser.add_argument("course_id",                 required=True,location=['json',],type=str,)
create_experiment_parser.add_argument("chapter_id",                required=True,location=['json',],type=str,)
create_experiment_parser.add_argument("experiment_introduction",   required=True,location=['json',],type=str,nullable=True)
create_experiment_parser.add_argument("experiment_type",           required=True,location=['json',],type=int,choices=EXPERIMENT_TYPE)
create_experiment_parser.add_argument("experiment_scene",          required=True,location=['json',],type=str,)

################################################新建实验#####################################################
class ExperimentCreate(Resource):
    """API: /api/v1/course/teacher_center/create_experiment"""
    
    @login_required
    def get(self):
        data={
            "experiment_type_virture":EXPERIMENT_TYPE[0],
            "experiment_type_single":EXPERIMENT_TYPE[1],
        }
        
        return Response.success(data=data,message="experiment type")
    
    @login_required
    def post(self):
        try:
            args=create_experiment_parser.parse_args()
            _,status=Experiment.find(
                query={
                    'course_id':args["course_id"],
                    "chapter_id":args["chapter_id"],
                },
                need={
                    "_id":1,
                },
            )
            if status == CourseStatusCode.FIND_SUCCESS:
                return Response.failed(message="create failed,exp already exist")
            new_exp={
                "available":1,
                "experiment_name":args["experiment_name"],
                "course_id":args["course_id"],
                "chapter_id":args["chapter_id"],
                "experiment_introduction":args["experiment_introduction"],
                "experiment_type":args["experiment_type"],
                "experiment_scene":args["experiment_scene"],
                "last_update_time":time.strftime("%Y-%m-%d-%H:%M"),
                "last_update_user":str(session.get("id")),
            }
            result,status=Experiment.insert_one(
                data=new_exp
            )
            if not status == CourseStatusCode.INSERT_SUCCESS:
                return Response.failed(message="create exp error")
            result=str(result)
            return Response.success(data=result,message="create exp success")
        except Exception as e:
            logger.warning("post method meets error: {}".format(e))
            return Response.failed(data="", message=e)
        

################################################更改实验信息#####################################################
class UpdateExperiment(Resource):
    """API: /api/v1/course/teacher_center/update_experiment"""
    @login_required
    def post(self):
        try:
            exp_id=ObjectId(str(request.args.get("experiment_id")))
            data=create_experiment_parser.parse_args()
            data.update(
                {
                    "last_update_time":time.strftime("%Y-%m-%d-%H:%M"),
                    "last_update_user":str(session.get("id")),
                }
            )
            _,status=Experiment.find_by_id(_id=exp_id,need={})
            if not status == CourseStatusCode.FIND_SUCCESS:
                return Response.failed(message="experiment not exist")
            _,status=Experiment.update_one_by_id(
                _id=exp_id,
                update=data
            )
            if not status == CourseStatusCode.UPDATE_SUCCESS:
                return Response.failed(message="experiment update error")
            return Response.success(data={"experiment_id":str(exp_id)},message="experiment update success")
        except Exception as e:
            logger.warning("post method meets error: {}".format(e))
            return Response.failed(data="", message=e)
        
################################################删除实验#####################################################

class DeleteExperiment(Resource):
    """API: /api/v1/course/teacher_center/delete_experiment"""
    
    @login_required
    def delete(self):
        try:
            exp_id=ObjectId(str(request.args.get("experiment_id")))
            _,status=Experiment.find_by_id(_id=exp_id,need={})
            if not status == CourseStatusCode.FIND_SUCCESS:
                return Response.failed(message="experiment not exist")
            _,status=Experiment.delete_by_id(_id=exp_id)
            if not status == CourseStatusCode.DELETE_SUCCESS:
                return Response.failed(message="delete expriment error")
            return Response.success(message="delete success")
        except Exception as e:
            logger.warning("post method meets error: {}".format(e))
            return Response.failed(data="", message=e)
        
################################################获取实验信息#####################################################

class GetExperimentInfo(Resource):
    """API: /api/v1/course/teacher_center/experiment"""
    
    @login_required
    def get(self):
        try:
            exp_id=ObjectId(str(request.args.get("experiment_id")))
            need={
                    "_id":1,
                    "experiment_name":1,
                    "course_id":1,
                    "chapter_id":1,
                    "experiment_introduction":1,
                    "experiment_type":1,
                    "experiment_scene":1,
                    "last_update_time":1,
            }
            res,status=Experiment.find_by_id(_id=exp_id,need=need)
            if not status == CourseStatusCode.FIND_SUCCESS:
                return Response.failed(message="find expriment error")
            # data=[]
            # for r in res:
            #     data.append(r)
            res.update({"experiment_id":str(res["_id"])})
            res.pop("_id")
            return Response.success(data=res,message="success")
        except Exception as e:
            logger.warning("post method meets error: {}".format(e))
            return Response.failed(data="", message=e)