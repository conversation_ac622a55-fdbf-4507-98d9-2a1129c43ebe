'''
-*- coding:utf-8 -*-
@Project ：jxsjpt 
@File    ：hworkService.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2023/3/18 17:08 
@desc    ：处理布置作业相关的业务
'''
from bson import ObjectId

from core.data.homeworkDetail import HworkDetail
from core.databases.orm.homework.homework_orm import HomeworkOrm
from core.databases.orm.homework.homeworkDetail_orm import HomeworkDetailOrm
from core.databases.orm.homework.question_orm import QuestionOrm
from common.utils.logger import logger

class HomeworkService:
    def __init__(self):
        self.dbhwOrm = HomeworkOrm()
        self.dbhwdOrm = HomeworkDetailOrm()
        self.dbQuestionOrm = QuestionOrm()

    def queryHomework(self, queryJson):
        '''
        方法功能：教师端查询作业,也返回作业已提交数
        查询条件：课程id，班级id，教师id
        '''
        jsonData = self.dbhwOrm.find(queryJson)
        result = []
        for data in jsonData:
            # if data['state'] in [2, 3]:  # 2和3分别代表已发布和已结束的状态
            #     # 查询已提交的作业数
            #     count = self.dbhidOrm.get_count({"homeworkNo": data['homeworkNo']})
            #     data['handedCount'] = count
            # data['_id'] = str(data['_id'])
            result.append(data)

        return result

    def queryHworkDetail(self,queryJson):
        '''
        方法功能：查询作业题详情
        查询条件：homeworkNo 作业id
        '''
        homeworkNo = queryJson['_id']
        hwds = self.dbhwdOrm.find({'homeworkNo':homeworkNo})
        result = []
        for hwd in hwds:
            qusetionNo = hwd['questionNo']
            question = self.dbQuestionOrm.find_by_id(qusetionNo)
            question.pop('_id')
            hwd['_id'] = str(hwd['_id'])
            hwd['question'] = question
            result.append(hwd)
        return result
    def saveHomework(self, jsonData):
        '''
        方法功能：保存作业，保存作业题详情
        '''
        # homework = Homework(jsonData)
        logger.info("保存作业上送数据{}".format(str(jsonData)))
        hworkDetaillist = jsonData.pop('question_list')
        #判断作业参数是否完整，特别是题库id
        for hwd in hworkDetaillist:
            questionNo = hwd["_id"]
            if questionNo == "" or questionNo is None:
                message = "题库id为空，创建作业失败，参数为：[{}]".format(hwd)
                logger.error(message)
                #return message
                raise ValueError(message)

        hwId = self.dbhwOrm.add_one(jsonData)
        sequenceNo = 1
        for hwd in hworkDetaillist:
            hwd['homeworkNo'] = str(hwId)
            hwd['sequenceNo'] = sequenceNo
            sequenceNo += 1
            hworkDetail = HworkDetail(hwd)
            self.dbhwdOrm.add_one(hworkDetail.toDbType())
        # self.dbhwOrm.update_by_condition({},{'type': 1})
        return hwId

    def modifyHomeWork(self, jsonData):
        '''
        方法功能：更新作业题，此处不考虑修改作业题，只是修改作业的附加属性
                比如，截止日期等
        '''
        success = self.dbhwOrm.update_by_id(jsonData['homeworkNo'], jsonData)
        return success

    def invalidHomeWork(self, jsonData):
        '''
        方法功能：删除作业，这里先简单处理，就是直接把作业表，作业明细表对应数据删除
        '''
        homeworkNo = {"_id": ObjectId(jsonData["_id"])}
        self.dbhwOrm.update_by_condition( homeworkNo, {'state': 2})
        return True

