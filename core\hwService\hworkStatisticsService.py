'''
-*- coding:utf-8 -*-
@Project ：jxsjpt 
@File    ：hworkStatisticsService.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2024/1/19 9:48 
@desc    ：
'''
import re
from datetime import datetime

from bson import ObjectId
from common.utils.logger import logger
from core.databases.orm.homework.classHIHw_orm import ClassHIHwOrm
from core.databases.orm.homework.handInHwork_orm import HandInHworkOrm


class HwStatiSerivce:
    def __init__(self):
        self.dbhInOrm = HandInHworkOrm()
        self.dbclassHIOrm = ClassHIHwOrm()
    def stuHwStati(self,stu_id,courseInfos):
        '''
        统计学生本学期所有作业情况
        {
                    "_id":"fasdf2345t3datg",#作业提交表id
                    "hwName":"第一次作业",#作业名称
                    "courseId":"346342teftgdsfghdfsh",#课程id
                    "courseName": "理论课程",  # 课程名称
                    "classId": "346342teftgdsfghdfsh",  # 班级id
                    "className": "网安一班",  # 班级名称
                    "obtainedScore":95,#分数
                }
        '''
        logger.info("开始统计学生{}的作业成绩".format(stu_id))
        courseIds = []
        courseIdToName = {}
        for item in courseInfos:
            courseIds.append(item["course_id"])
            courseIdToName[item["course_id"]] = item["course_name"]
        logger.info("查询的课程为{}".format(str(courseIdToName)))
        results = self.dbhInOrm.stuStati(stu_id,courseIds)
        for result in results:
            result["courseName"] = courseIdToName[result["courseId"]]
            #这里查出来班级只有一个元素，但是是使用list包裹着，需要取出来
            result["className"] = result["className"][0]
        return results

    def teaHwStati(self, teaId, courseInfos):
        '''
        统计本学期老师所有课程的作业情况
              data=[{
                "_id":"fasdf2345t3datg",#作业提交表id
                "hwName":"第一次作业",#作业名称
                "courseId":"346342teftgdsfghdfsh",#课程id
                "courseName": "理论课程",  # 课程名称
                "classId": "346342teftgdsfghdfsh",  # 班级id
                "className": "网安一班",  # 班级名称
                "handInfo": "90/100",  # 提交情况
                "enddate": "2023-10-14T15:16:31+00:00",  # 截止日期
                "averagedScore":93#平均分
            }]
        '''
        logger.info("开始统计老师{}的所教作业成绩".format(teaId))
        courseIds = []
        courseIdToName = {}
        for item in courseInfos:
            courseIds.append(item["course_id"])
            courseIdToName[item["course_id"]] = item["course_name"]
        logger.info("查询的课程为{}".format(str(courseIdToName)))
        results = self.dbclassHIOrm.teaStati(teaId, courseIds)
        for result in results:
            result["courseName"] = courseIdToName[result["courseId"]]
            obtainedScores = result.pop("obtainedScore")
            count = len(obtainedScores)
            if count > 0:
                average_obtainedScore = sum(obtainedScores) / count
            else:
                average_obtainedScore = 0
            result["averagedScore"] = average_obtainedScore
            handInfo = str(result["handedCount"])+"/"+str(result["studentCount"])
            result["handInfo"] = handInfo
            result["hwName"] = result.pop("homeworkName")
            result["enddate"] = result.pop("endTime")
        return results
