'''
-*- coding:utf-8 -*-
@Project ：jxsjpt 
@File    ：handInHwork_orm.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2023/3/18 18:29 
@desc    ：
'''
from core.databases.db_mongo import mongo
from core.databases.orm.homework.hwDatabase import HwDatabase


class HandInHworkOrm(HwDatabase):
    def __init__(self):
        HwDatabase.__init__(self)
        self.table = "hw_handIn_hwork_table"

    def stuStati(self, stu_id, courseIds):
        cursor = mongo[self.table].aggregate([
               {
                   "$lookup":
                     {
                         "from": "hw_clas_handin_table",
                         "let": {"hworkNo": "$hworkNo",
                                 "courseId":"$courseId",
                                 "classId":"$classId"},
                         "pipeline": [
                            {
                                "$match": {
                                    "$expr": {
                                        "$and": [
                                            { "$eq": ["$$hworkNo", "$homeworkNo"] },
                                            { "$eq": ["$$courseId", "$courseId"] },
                                            {"$eq": ["$$classId", "$classId"]},
                                        ]
                                    }
                                }
                            }
                         ],
                         "as": "class_info"
                     }
               },
               {
                   "$match": {
                       "$expr": {
                            "$and": [
                                      { "$eq": [stu_id, "$studentId"]},
                                      { "$in": ["$courseId", courseIds]},
                                  ]
                            }
                   }
               },
               {
                   "$project": {
                       "_id":{"$toString":"$_id"},
                       "courseId": 1,
                       "className": "$class_info.className",
                       "obtainedScore": 1,
                       "classId": 1,
                       "hwName": 1
                   }
               }
            ])
        result = []
        if cursor:
            for data in cursor:
                result.append(data)
        return result