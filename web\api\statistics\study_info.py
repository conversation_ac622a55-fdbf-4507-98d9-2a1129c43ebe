"""
-*- coding:utf-8 -*-
@Project : jxsjpt
@File    : stuRank.py
@IDE     : VSCode
<AUTHOR> xyy:)
@Date    : 2023/12/20 
@desc    ：用于右下学生排名
"""
import time

from bson import ObjectId
from flask import request,session
from flask_restful import Resource, reqparse, inputs
from common.utils.logger import logger
from core.data.response import Response
from core.databases.orm.user.organzation_orm import *
from core.databases.orm.course.course_orm import *
from core.auth.auth import *
'''
展示学生的学习情况,老师的教学汇总
'''
class StuStudyInfo(Resource):

    def get(self):
        data={
            "days":101,
            "chapters":97,
            "homeworks":42,
            "exams":4,
            "last_exam_grade": 91
        }
        return Response.success(message='success',data=data)
class TeachInfo(Resource):

    def get(self):
        data={
            "days":101,
            "homeworks":42,
            "exams":4
        }
        return Response.success(message='success',data=data)