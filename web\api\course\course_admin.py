from flask import request
from flask_restful import Resource, reqparse
from common.utils.logger import logger
from core.data.response import Response, StatusCode
from core.databases.orm.course.course_orm import *
from flask_restful import Resource, reqparse
from bson import ObjectId
from core.auth.auth import *
import time
import re

from core.databases.orm.user.organzation_orm import *
from core.databases.orm.user.users_orm import *
##根据班级id返回名称
def getClassName(classid):
    data=[]
    for c in classid:
        res=DBCoursePlatformStuOrg.get_one({"_id":ObjectId(str(c))})
        if res == None:
            continue
        name=res['name']
        data.append(
            {
                "name":str(name),
                "id":str(c)
            }
        )
    return data
##根据教师id返回名称
def getTeacherName(teacherid):
    data=[]
    for t in teacherid:
        res=DBCoursePlatformTeachers.get_one({"_id":ObjectId(str(t))})
        if res == None:
            continue
        name=res['user_name']
        data.append(
            {
                "name":str(name),
                "id":str(t)
            }
        )
    return data
#***********************管理端显示所有理论课程****************************
class ShowAllCourse(Resource):
    
    @login_required
    def get(self):
        if not session.get('role') == 'admin':
            return Response.failed(message='permission denied')
        try:
            allcourse,status=CourseInfo.find(
                query={
                    "course_type":"理论课",
                    },
                need={
                    "_id": 1,
                    "course_name": 1,
                    "course_image": 1,
                    "course_type":1,
                    "course_status":1,
                    "course_organization": 1,
                }
            )
            if not status == CourseStatusCode.FIND_SUCCESS:
                return Response.success(data=[],message="empty course")
            data=[]
            for course in allcourse:
                course_id=str(course['_id'])
                course['_id']=course_id
                classinfo=getClassName(course["course_organization"])
                course['course_organization']=classinfo
                teachers,status=CourseTeacher.find(
                    query={
                        "course_id":course_id,
                    },
                    need={
                        "_id":0,
                        "teacher_id":1,
                    }
                )
                if not status == CourseStatusCode.FIND_SUCCESS:
                    return Response.failed(message='find ct table error')
                for t in teachers:
                    teacherinfo=getTeacherName(t['teacher_id'])
                    course.update(
                        {
                            "course_teacher_id":teacherinfo,
                        }
                    )
                data.append(course)
            search=None
            datas=[]
            search=request.args.get("search")
            if not search == None:
                for d in data:
                    r=re.search(search,d["course_name"])
                    if r:
                        datas.append(d)
                data=datas
            return Response.success(data=data,message='success')
        except Exception as e:
            logger.warning("get method meets error: {}".format(e))
            return Response.failed(data="", message=e)
        
        
        
#***********************管理端显示所有实验课程****************************
class ShowAllExperiment(Resource):
    
    @login_required
    def get(self):
        if not session.get('role') == 'admin':
            return Response.failed(message='permission denied')
        try:
            allcourse,status=CourseInfo.find(
                query={
                    "course_type":"实验课",
                    },
                need={
                    "_id": 1,
                    "course_name": 1,
                    "course_image": 1,
                    "course_type":1,
                    "course_status":1,
                    "course_organization": 1,
                }
            )
            if not status == CourseStatusCode.FIND_SUCCESS:
                return Response.success(data=[],message="empty course")
            data=[]
            for course in allcourse:
                course_id=str(course['_id'])
                course['_id']=course_id
                classinfo=getClassName(course["course_organization"])
                course['course_organization']=classinfo
                teachers,status=CourseTeacher.find(
                    query={
                        "course_id":course_id,
                    },
                    need={
                        "_id":0,
                        "teacher_id":1,
                    }
                )
                if not status == CourseStatusCode.FIND_SUCCESS:
                    return Response.failed(message='find ct table error')
                for t in teachers:
                    teacherinfo=getTeacherName(t['teacher_id'])
                    course.update(
                        {
                            "course_teacher_id":teacherinfo,
                        }
                    )
                data.append(course)
            search=None
            datas=[]
            search=request.args.get("search")
            if not search == None:
                for d in data:
                    r=re.search(search,d["course_name"])
                    if r:
                        datas.append(d)
                data=datas
            return Response.success(data=data,message='success')
        except Exception as e:
            logger.warning("get method meets error: {}".format(e))
            return Response.failed(data="", message=e)
        
        
        
        
#***********************管理端显示所有已删除的理论课程****************************
class ShowAllDeletedCourse(Resource):
    
    @login_required
    def get(self):
        if not session.get('role') == 'admin':
            return Response.failed(message='permission denied')
        try:
            allcourse,status=CourseInfo.find_for_deleted(
                query={
                    "course_type":"理论课",
                    },
                need={
                    "_id": 1,
                    "course_name": 1,
                    "course_image": 1,
                    "course_type":1,
                    "course_organization": 1,
                }
            )
            if not status == CourseStatusCode.FIND_SUCCESS:
                return Response.success(data=[],message="empty course")
            data=[]
            for course in allcourse:
                course_id=str(course['_id'])
                course['_id']=course_id
                classinfo=getClassName(course["course_organization"])
                course['course_organization']=classinfo
                teachers,status=CourseTeacher.find_for_deleted(
                    query={
                        "course_id":course_id,
                    },
                    need={
                        "_id":0,
                        "teacher_id":1,
                    }
                )
                if not status == CourseStatusCode.FIND_SUCCESS:
                    return Response.failed(message='find ct table error')
                for t in teachers:
                    teacherinfo=getTeacherName(t['teacher_id'])
                    course.update(
                        {
                            "course_teacher_id":teacherinfo,
                        }
                    )
                data.append(course)
            search=None
            datas=[]
            search=request.args.get("search")
            if not search == None:
                for d in data:
                    r=re.search(search,d["course_name"])
                    if r:
                        datas.append(d)
                data=datas
            return Response.success(data=data,message='success')
        except Exception as e:
            logger.warning("get method meets error: {}".format(e))
            return Response.failed(data="", message=e)
        
        
        
#***********************管理端显示所有已删除的实验课程****************************
class ShowAllDeletedExperiment(Resource):
    
    @login_required
    def get(self):
        if not session.get('role') == 'admin':
            return Response.failed(message='permission denied')
        try:
            allcourse,status=CourseInfo.find_for_deleted(
                query={
                    "course_type":"实验课",
                    },
                need={
                    "_id": 1,
                    "course_name": 1,
                    "course_image": 1,
                    "course_type":1,
                    "course_organization": 1,
                }
            )
            if not status == CourseStatusCode.FIND_SUCCESS:
                return Response.success(data=[],message="empty course")
            data=[]
            for course in allcourse:
                course_id=str(course['_id'])
                course['_id']=course_id
                classinfo=getClassName(course["course_organization"])
                course['course_organization']=classinfo
                teachers,status=CourseTeacher.find_for_deleted(
                    query={
                        "course_id":course_id,
                    },
                    need={
                        "_id":0,
                        "teacher_id":1,
                    }
                )
                if not status == CourseStatusCode.FIND_SUCCESS:
                    return Response.failed(message='find ct table error')
                for t in teachers:
                    teacherinfo=getTeacherName(t['teacher_id'])
                    course.update(
                        {
                            "course_teacher_id":teacherinfo,
                        }
                    )
                data.append(course)
            search=None
            datas=[]
            search=request.args.get("search")
            if not search == None:
                for d in data:
                    r=re.search(search,d["course_name"])
                    if r:
                        datas.append(d)
                data=datas
            return Response.success(data=data,message='success')
        except Exception as e:
            logger.warning("get method meets error: {}".format(e))
            return Response.failed(data="", message=e)
        
##***********************管理端恢复已删除的课程/实验****************************
class ReversDeletedCourse(Resource):
    def post(self,):
        try:
            if not session.get('role') == 'admin':
                return Response.failed(message='permission denied')                
            course_id=request.json["course_id"]
            _,status=CourseInfo.find_for_deleted(
                query={"_id":ObjectId(str(course_id)),},
                need={"_id":1,},
            )
            if not status == CourseStatusCode.FIND_SUCCESS:
                return Response.failed(message="find course failed")
            #恢复课程信息
            _,status=CourseInfo.reverse(id=course_id)
            if not status == CourseStatusCode.UPDATE_SUCCESS:
                return Response.failed(message="reverse failed")
            #恢复ct表信息
            res,status=CourseTeacher.find_for_deleted(
                query={"course_id":course_id},
                need={"_id":1}
            )
            if not status == CourseStatusCode.FIND_SUCCESS:
                return Response.failed(message="find failed")
            for r in res:
                _,status=CourseTeacher.reverse(id=str(r["_id"]))
                if not status == CourseStatusCode.UPDATE_SUCCESS:
                    return Response.failed(message="reverse failed")
            #恢复cs表信息
            res,status=CourseStudent.find_for_deleted(
                query={"course_id":course_id},
                need={"_id":1}
            )
            if not status == CourseStatusCode.FIND_SUCCESS:
                return Response.failed(message="find failed")
            for r in res:
                _,status=CourseStudent.reverse(id=str(r["_id"]))
                if not status == CourseStatusCode.UPDATE_SUCCESS:
                    return Response.failed(message="reverse failed")
            return Response.success(message="reverse success")
        except Exception as e:
            logger.warning("get method meets error: {}".format(e))
            return Response.failed(data="", message=e)
        
#################################管理端删除学生实例资源############################

class DeleteStuIns(Resource):
    
    @login_required
    def delete(self):
        try:
            course_id=request.json["course_id"]
            if course_id == None:
                raise ValueError("Null course_id")
            chapter_id=request.json["chapter_id"]
            if chapter_id == None:
                raise ValueError("Null chapter_id")
            student_id=request.json["user_id"]
            if student_id == None:
                raise ValueError("Null user_id")
            res,status=CourseStudent.find(
                query={
                    "student_id":student_id,
                    "course_id":course_id,
                },
                need={
                    "_id":0,
                    "experiment_resource":1,
                }
            )
            if not status == CourseStatusCode.FIND_SUCCESS:
                return Response.failed(message="cant find in T-cs")
            newexp=[]
            exps=res[0]["experiment_resource"]
            if len(exps)<=0:
                return Response.failed(message="no expr")
            for exp in exps:
                if not str(chapter_id) in exp.keys():
                    newexp.append(exp)
            _,status=CourseStudent.update_many(
                query={
                    "student_id":student_id,
                    "course_id":course_id,
                },
                update={
                    "experiment_resource":newexp
                }
            )
            if not status == CourseStatusCode.UPDATE_SUCCESS:
                return Response.failed(message="cant delete in T-cs")
            return Response.success(message="delete success")
        except Exception as e:
            logger.warning("post method meets error: {}".format(e))
            return Response.failed(data="", message=e)