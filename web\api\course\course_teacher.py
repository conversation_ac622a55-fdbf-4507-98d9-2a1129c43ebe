from flask import request
from flask_restful import Resource, reqparse
from common.utils.logger import logger
from core.data.response import Response, StatusCode
from core.databases.orm.course.course_orm import *
from flask_restful import Resource, reqparse
from bson import ObjectId
from core.auth.auth import *
import time
import re
import datetime

from core.databases.orm.user.organzation_orm import *
from core.databases.orm.user.users_orm import *

def cul_total_du(duration):
    total=0
    for d in duration:
        total+=len(d['weeks'])*len(d['day'])*len(d['time'])
    return total

def cul_total_sc(scheme:dict):
    total=0
    total+=cul_total_du(scheme['duration'])
    if  'total' in scheme.keys():
        scheme['total']=total
    else:
        scheme.update({
            "total":total
        })

def _get_process(course_id):
    seme,status=Semesters.find(query={"isnow":1},need={"name":1})
    if not status == CourseStatusCode.FIND_SUCCESS:
        now=datetime.datetime.now().date()
        if 3 <= now.month < 9:
            seme=str(now.year)+'-2'
        elif 9 <= now.month:
            seme=str(now.year)+'-1'
        else:
            seme=str(now.year-1)+'-1'
    seme=seme[0]['name']
    res,status=CourseTime.find(query={"course_id":str(course_id),"semester":seme},
                                need={"_id":0,
                                    "duration":1,
                                    "total":1,
                                    "progress":1})
    if not status == CourseStatusCode.FIND_SUCCESS:          
        return {"progress":0.5,'total':48,"done":24}
    res=res[0]
    if not 'total' in res.keys():
        cul_total_sc(res)
    back={
        "progress":res["progress"],
        "total":res['total'],
        "done":int(res['total']*res["progress"])
    }
    return back


##根据班级id返回名称
def getClassName(classid):
    data=[]
    for c in classid:
        res=DBCoursePlatformStuOrg.get_one({"_id":ObjectId(str(c))})
        if res == None:
            continue
        name=res['name']
        data.append(
            {
                "name":str(name),
                "id":str(c)
            }
        )
    return data
##根据教师id返回名称
def getTeacherName(teacherid):
    data=[]
    for t in teacherid:
        res=DBCoursePlatformTeachers.get_one({"_id":ObjectId(str(t))})
        if res == None:
            continue
        name=res['user_name']
        data.append(
            {
                "name":str(name),
                "id":str(t)
            }
        )
    return data
#***********************教师端显示教室所教理论课程****************************
class ShowTeacherCourse(Resource):
    """
    API: /api/v1/course/showteachercourse
    get方式传入数据为用户(教师)id
    回传参数包括_id,course_name,course_teacher,course_image,course_organization(统一格式)
    """
    @login_required
    def get(self):
        try: 
            teacher = session.get('id')
            teacher = str(teacher)
            query = {}
            if not teacher == None:
                query.update({"course_teacher": teacher})
            else:
                return Response.failed(data="", message="Null search option")

            #先在ct表中找到包含老师id的相关doc的课程id
            need={
                "_id":0,
                "course_id":1,
                "teacher_id":1,
            }
            res,status=CourseTeacher.find(
                query={"teacher_id":teacher},
                need=need)
            if status == CourseStatusCode.FIND_ERROR_NULL:
                return Response.success(data=[],message="empty course")
            data=[]
            for course in res:
                need = {"_id": 1,
                        "course_name": 1,
                        "course_image": 1,
                        "course_status":1,
                        "course_organization": 1,
                        "open_date":1,
                        "finish_date":1,
                        }
            
                res2, status = CourseInfo.find(
                    query={"_id":ObjectId(course["course_id"]),
                           "course_type":"理论课"},
                    need=need)
                if status == CourseStatusCode.FIND_SUCCESS:
                    for r in res2:
                        r["_id"]=str(r["_id"])
                        #班级信息
                        classid=r["course_organization"]
                        classinfo=getClassName(classid)
                        r["course_organization"]=classinfo
                        #把老师id加入进去
                        #r.update({"teachers_name":course["teacher_name"]})
                        teacherinfo=getTeacherName(course["teacher_id"])
                        r.update({"course_teacher_id":teacherinfo})
                        process=_get_process(r["_id"])
                        r.update(process)
                        data.append(r)
            search=None
            datas=[]
            search=request.args.get("search")
            if not search == None:
                for d in data:
                    r=re.search(search,d["course_name"])
                    if r:
                        datas.append(d)
                data=datas
            if len(data) > 0:        
                return Response.success(data=data, message="success")
            else:
                return Response.success(data=[], message="No class")
        except Exception as e:
            logger.warning("get method meets error: {}".format(e))
            return Response.failed(data="", message=e)

#***********************教师端显示教室所教实验课程****************************
class ShowTeacherExperiment(Resource):
    """
    API: /api/v1/course/showteacherexperiment
    get方式传入数据为用户(教师)id
    回传参数包括_id,course_name,course_teacher,course_image,course_organization(统一格式)
    """
    @login_required
    def get(self):
        try:
            teacher = session.get('id')
            teacher = str(teacher)
            query = {}
            if not teacher == None:
                query.update({"course_teacher": teacher})
            else:
                return Response.failed(data="", message="Null search option")

            #先在ct表中找到包含老师id的相关doc的课程id
            need={
                "_id":0,
                "course_id":1,
                "teacher_id":1,
            }
            res,status=CourseTeacher.find(
                query={"teacher_id":teacher},
                need=need)
            if status == CourseStatusCode.FIND_ERROR_NULL:
                return Response.success(data=[],message="empty course")
            data=[]
            for course in res:
                need = {"_id": 1,
                        "course_name": 1,
                        "course_image": 1,
                        "course_status":1,
                        "course_organization": 1,
                        "open_date":1,
                        "finish_date":1,
                        }
            
                res2, status = CourseInfo.find(
                    query={"_id":ObjectId(course["course_id"]),
                           "course_type":"实验课"},
                    need=need)
                if status == CourseStatusCode.FIND_SUCCESS:
                    for r in res2:
                        r["_id"]=str(r["_id"])
                         #班级信息
                        classid=r["course_organization"]
                        classinfo=getClassName(classid)
                        r["course_organization"]=classinfo
                        #把老师id加入进去
                        #r.update({"teachers_name":course["teacher_name"]})
                        teacherinfo=getTeacherName(course["teacher_id"])
                        r.update({"course_teacher_id":teacherinfo})
                        process=_get_process(r["_id"])
                        r.update(process)
                        data.append(r)
            search=None
            datas=[]
            search=request.args.get("search")
            if not search == None:
                for d in data:
                    r=re.search(search,d["course_name"])
                    if r:
                        datas.append(d)
                data=datas
            if len(data) > 0:        
                return Response.success(data=data, message="success")
            else:
                return Response.success(data=[], message="no experiment")
        except Exception as e:
            logger.warning("get method meets error: {}".format(e))
            return Response.failed(data="", message=e)

#***********************更新课程教师表****************************

update_ct_parser=reqparse.RequestParser()
update_ct_parser.add_argument("course_id",          required=True,location=['json',],type=str,)
#update_ct_parser.add_argument("teacher_name",       required=True,location=['json',],type=str,action='append',)
update_ct_parser.add_argument("course_teacher_id",         required=True,location=['json',],type=str,action='append',)
class UpdateCourseTeacher(Resource):
    """
    API: /api/v1/course/updateCourseTeacher
    """
    @login_required
    def get(self):
        try:
            course_id=request.args.get("course_id")
            if course_id == None:
                raise ValueError("course_id null")
            res,status=CourseTeacher.find(
                query={"course_id":course_id},
                need={
                    "_id":0,
                    "teacher_id":1
                }
            )
            if not status == CourseStatusCode.FIND_SUCCESS:
                return Response.failed(data="", message="not found")
            data=[]
            for r in res:
                data.append(r['teacher_id'])
            return Response.success(data=data, message="success")
        except Exception as e:
            return Response.failed(data="", message=e)
        
    @login_required
    def post(self):
        try:
            request=update_ct_parser.parse_args()
            new_teacher=request["course_teacher_id"]
            res,status=CourseTeacher.find(
                query={"course_id":request["course_id"]},
                need={
                    "_id":0,
                    "teacher_id":1
                }
            )
            if not status == CourseStatusCode.FIND_SUCCESS:
                return Response.failed(data="", message="not found")
            update_teacher=res[0]['teacher_id']
            for t in new_teacher:
                if not t in update_teacher:
                    update_teacher.append(t)
            query={"course_id":request["course_id"]}
            update={
                "teacher_id":update_teacher
                }
            _,status=CourseTeacher.update_many(
                query=query,update=update
            )
            if not status == CourseStatusCode.UPDATE_SUCCESS:
                return Response.failed(data="",message="update ct error")
            return Response.success(data="",message="update ct success")
        except Exception as e:
            return Response.failed(data="", message=e)


#***********************************老师与实例化(存在问题)*****************************************
class TeacherAndExperimentResource(Resource):
    """API /api/v1/teacher/experiment/resource"""
    # {
    #     "_id":1,
    #     "course_id":1,
    #     "teacher_id":1,
    #     "resources":[
    #        {"exp_id":"resource_id"}
    #      ],
    # }
    @login_required
    def get(self):
        try:
            course_id=request.args.get("course_id")
            if course_id == None:
                raise ValueError("Null course_id")
            exp_id=None
            exp_id=request.args.get("chapter_id")
            teacher_id=session.get("id")
            res,status=ExperimentTeacher.find(
                query={
                    "teacher_id":teacher_id,
                    "course_id":course_id,
                },
                need={
                    "_id":0,
                    "resources":1,
                }
            )
            if not status == CourseStatusCode.FIND_SUCCESS:
                return Response.success(data=[],message="no instance")
            data=[]
            if exp_id == None:
                return Response.success(data=res,message="success")
            else:
                for exp in res:
                    if exp_id in exp.keys():
                        data.append(exp)
            return Response.success(data=data,message="success")
        except Exception as e:
            logger.warning("post method meets error: {}".format(e))
            return Response.failed(data="", message=e)
    
    @login_required
    def post(self):
        try:
            data=request.get_json()
            if not "course_id" in data.keys():
                raise ValueError("miss course_id")
            if not "chapter_id" in data.keys():
                raise ValueError("miss chapter_id")
            if not "instance_id" in data.keys():
                raise ValueError("miss instance_id")
            teacher_id=session.get("id")
            _id,status=ExperimentTeacher.find(
                query={
                    "course_id":data["course_id"],
                    "teacher_id":teacher_id,
                },
                need={
                    "_id":1,
                }
            )
            if status == CourseStatusCode.FIND_SUCCESS:
                _,status=ExperimentTeacher.update_one_by_id(
                    _id=ObjectId(str(_id[0]["_id"])),
                    update={
                        data["chapter_id"]:data["resources"],
                    }
                )
                return Response.success(message="update et success")
            else:
                data.update({
                    "teacher_id":str(teacher_id),
                    "available":1,
                })
                _id,status=ExperimentTeacher.insert_one(
                    data=data
                )
                if not status == CourseStatusCode.INSERT_SUCCESS:
                    return Response.failed(message="create teacher resource failed")
                return Response.success(data=str(_id),message="success")
        except Exception as e:
            logger.warning("post method meets error: {}".format(e))
            return Response.failed(data="", message=e)

    @login_required
    def delete(self):
        try:
            course_id=request.json["course_id"]
            if course_id == None:
                raise ValueError("Null course_id")
            exp_id=request.json["experiment_id"]
            if exp_id == None:
                raise ValueError("Null experiment_id")
            teacher_id=session.get("id")
            res,status=ExperimentTeacher.find(
                query={
                    "teacher_id":teacher_id,
                    "course_id":course_id,
                },
                need={
                    "_id":0,
                    "resources":1,
                }
            )
            if not status == CourseStatusCode.FIND_SUCCESS:
                return Response.failed(message="cant find in T-et")
            newexp=[]
            exps=res[0]["resources"]
            if len(exps)<=0:
                return Response.failed(message="no expr")
            for exp in exps:
                if not str(exp_id) in exp.keys():
                    newexp.append(exp)
            _,status=CourseStudent.update_many(
                query={
                    "teacher_id":teacher_id,
                    "course_id":course_id,
                },
                update={
                    "resources":newexp
                }
            )
            if not status == CourseStatusCode.UPDATE_SUCCESS:
                return Response.failed(message="cant delete in T-et")
            return Response.success(message="delete success")
        except Exception as e:
            logger.warning("post method meets error: {}".format(e))
            return Response.failed(data="", message=e)