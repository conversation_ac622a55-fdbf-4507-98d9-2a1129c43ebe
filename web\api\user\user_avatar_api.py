
import hashlib
import imghdr
import os
from flask import request, send_file, session
from flask_restful import Resource
from common.utils.logger import logger
from config import Config
from core.auth.auth import login_required
from core.data.response import Response

from web.api.user.user_api import get_handler, get_user_manager


def is_valid_image(stream):
    header = stream.read(512) 
    stream.seek(0)
    return imghdr.what(None, header) is not None
    
class AvatarUploader(Resource):
    """ API: /user/avatar/upload """

    @login_required
    def post(self):
        try:
            file = request.files.get('file')
            if not file:
                raise Exception("invalid image file")
            
            if file.filename == "" or not is_valid_image(file.stream):
                raise Exception("invalid image file")

            new_name = hashlib.md5(file.read()).hexdigest()
            ext = file.filename.rsplit('.', 1)[1]        
                
            fdir = os.path.join(Config.UPLOAD_FOLDER,"images")
            fname =  new_name+"."+ext
            
            if not os.path.exists(fdir):
                os.makedirs(fdir)
            
            fpath = os.path.join(fdir, new_name+"."+ext)
            # print(fname)
            
            # print(fpath)
            if not os.path.exists(fpath):
                file.seek(0)
                file.save(fpath)

            furl = 'http://' + Config.MONGO_HOST + ':' + str(Config.SERVER_PORT) + '/api/v1/user/avatar/' + fname
            
            get_user_manager(session.get("role")).put(id=session.get("id"), args={"avatar": furl})

            return Response.success(data=furl, message="upload avatar success.")
        except Exception as e:
            msg = "upload avater failed: {}.".format(e)
            logger.warning(msg)
            return Response.failed(message=msg)
        
class AvatarLooker(Resource):
    """ API: /user/avatar/<id> """

    def get(self, id):
        try:
            fdir = os.path.join(".",Config.UPLOAD_FOLDER,"images")
            # fdir = "upload\\images"
            fpath = os.path.join(fdir, id)
            if not os.path.exists(fpath):
                raise Exception("avatar not exists")
            fpath = os.path.abspath(fpath) #"../" + fpath
            return send_file(fpath, mimetype='image/jpeg')
        except Exception as e:
            msg = "get avater failed: {}.".format(e)
            logger.warning(msg)
            return Response.failed(message=msg)
            

        
        
