#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time   :
# <AUTHOR> msm
# @File   : course_orm.py
# @desc   : ""


import time

from bson import ObjectId
from core.databases.orm.database_base import DatabaseBase
from core.databases.db_mongo import *

class CourseStatusCode():   
    #文档状态
    DOCUMENT_NOT_EXIST=-2
    DOCUMENT_UNAVAILABLE=0
    DOCUMENT_AVAILABLE=1
    #操作状态
    FIND_SUCCESS=10
    FIND_ERROR_NULL=11
    UPDATE_SUCCESS=12
    UPDATE_ERROR=13
    INSERT_ERROR=14
    INSERT_SUCCESS=15
    INSERT_ERROR_ALREADY_EXIST=16
    DELETE_ERROR=17
    DELETE_SUCCESS=18
    QUERY_EMPTY=19
    #课程状态
    COURSE_CLOSED=20
    COURSE_OPEN=21
    COURSE_WAIT_OPEN=22


class _CourseInfo(DatabaseBase):
    
    
    
    def __init__(self):
        super(_CourseInfo,self).__init__()
        self.table = T_COURSE



    def find(self, query:dict,need:dict):
        """根据条件查询
        Args:
            query (dict): 查询条件. Defaults to None.
            need(dict):需要的字段内容
        Returns:
            fail: DOCUMENT_NOT_EXIST
            success:query result+success code
        """
        result = []
        query.update({"available":CourseStatusCode.DOCUMENT_AVAILABLE})
        data = mongo[self.table].find(query,need)
        for item in data:
            result.append(item)
        if len(result) <= 0:
            return query, CourseStatusCode.FIND_ERROR_NULL
        return result, CourseStatusCode.FIND_SUCCESS



    def find_id(self,query:dict):
        """根据条件查询
        Args:
            query (_type_, optional): 查询条件. Defaults to None？感觉不太安全
            {“属性”：“值”，...}
        Returns:
            fail: nothing has been found
            success: renturn 所有符合条件_id构成的[]
        """
        if query == None:
            return query, CourseStatusCode.QUERY_EMPTY
        result=[]
        query=mongo[self.table].find(query)
        for item in query:
            _,available = self.isAvailable(ObjectId(str(item["_id"])))
            if available == CourseStatusCode.DOCUMENT_AVAILABLE:
                result.append(str(item["_id"]))
        if len(result)<=0:
            return query, CourseStatusCode.DOCUMENT_NOT_EXIST
        else:
            return result, CourseStatusCode.FIND_SUCCESS




    #需要吗？后面再看一眼
    def find_by_id(self, _id,need:dict):
        """find by _id 
        Args:
            _id (_type_): Object id
        Returns:
            document:only one 
        """
        _id=ObjectId(str(_id))
        _,available=self.isAvailable(_id)
        if not available == CourseStatusCode.DOCUMENT_AVAILABLE:
            return _,CourseStatusCode.FIND_ERROR_NULL
        return mongo[self.table].find_one({"_id":_id},need), CourseStatusCode.FIND_SUCCESS
    
    
    
    def get_list(self, limit=1000):
        return super().get_list(limit)
    
    
    
    def get_count(self, query=None):
        return super().get_count(query)
    
    
    
    def isAvailable(self,_id):
        """查询课程是否存在
        Args:
            _id (str): 课程_id   
        """
        _id=ObjectId(str(_id))
        res=mongo[self.table].find_one(
            {"_id":_id}
        )
        if res["available"]==CourseStatusCode.DOCUMENT_AVAILABLE:
            return res,CourseStatusCode.DOCUMENT_AVAILABLE
        return _id,CourseStatusCode.DOCUMENT_NOT_EXIST
    
    
    
    #by_id操作中相关验证可删
    def delete_by_id(self, _id):
        """删除数据库文档（修改状态）
        Args:
            _id (ObjectId):
        Returns:
            fail: DELETE_ERROR
            success:DELETE_SUCCESS
        """
        _id=ObjectId(str(_id))
        result=mongo[self.table].update_one({"_id":_id},{"$set":{"available":CourseStatusCode.DOCUMENT_UNAVAILABLE}})
        if result.modified_count <= 0:
            return _id,CourseStatusCode.DELETE_ERROR
        else:
            return _id,CourseStatusCode.DELETE_SUCCESS



    def update_one_by_id(self, _id, update:dict):
        """根据id进行更新，id唯一项
        Args:
            _id (str/Object):id
            update ({"":"",}): 更改信息
        Returns:
            id,status:结果id，状态码
        """
        _id=ObjectId(str(_id))
        result = mongo[self.table].update_one({"_id":_id},{"$set":update})
        if result.matched_count <= 0 :
            return _id,CourseStatusCode.UPDATE_ERROR
        return result.upserted_id,CourseStatusCode.UPDATE_SUCCESS
        
    def find_for_deleted(self,query,need):
        result = []
        query.update({"available":CourseStatusCode.DOCUMENT_UNAVAILABLE})
        data = mongo[self.table].find(query,need)
        for item in data:
            result.append(item)
        if len(result) <= 0:
            return query, CourseStatusCode.FIND_ERROR_NULL
        return result, CourseStatusCode.FIND_SUCCESS
    
    def delete_for_admin(self,id):
        _id=ObjectId(str(id))
        result=mongo[self.table].delete_one({"_id":_id})
        if result.deleted_count<0:
            return result,CourseStatusCode.DELETE_ERROR
        return result,CourseStatusCode.DELETE_SUCCESS
    
    def delete_many(self,query):
        result=mongo[self.table].delete_many(
            filter=query
        )
        if result.deleted_count<=0:
            return result.deleted_count,CourseStatusCode.DELETE_ERROR
        return result.deleted_count,CourseStatusCode.DELETE_SUCCESS
    
    def reverse(self,id):
        _id=ObjectId(str(id))
        result=mongo[self.table].update_one({"_id":_id},{"$set":{"available":CourseStatusCode.DOCUMENT_AVAILABLE}})
        if result.modified_count<=0:
            return result,CourseStatusCode.UPDATE_ERROR
        return result,CourseStatusCode.UPDATE_SUCCESS
        
    def update_many(self,query:dict,update:dict):
        """根据给定条件进行更新
        Args:
            query(dict):查询条件信息
            update (dict): 更改信息
        Returns:
            id,status:修改后结果,状态码
        """
        if query == None:
            return query, CourseStatusCode.QUERY_EMPTY
        query.update({"available":CourseStatusCode.DOCUMENT_AVAILABLE})
        result = mongo[self.table].update_many(query,{"$set":update})
        if result.matched_count <= 0:
            return query, CourseStatusCode.UPDATE_ERROR
        return result.raw_result, CourseStatusCode.UPDATE_SUCCESS
    
    
    
    def insert_one(self,data:dict):
        """插入新文档，无需查重
        Args:
            data (dict): 插入数据
        Returns:
            成功时：
                id,StatusCode
            失败时：
                data,StatusCode
        """
        # course_name=str(data["course_name"])
        # _,isrepeat=self.find({"course_name":course_name})
        # if isrepeat == CourseStatusCode.FIND_SUCCESS:
        #     return data, CourseStatusCode.INSERT_ERROR_ALREADY_EXIST
        result = mongo[self.table].insert_one(data)
        if result.inserted_id == None:
            return data, CourseStatusCode.INSERT_ERROR
        return result.inserted_id, CourseStatusCode.INSERT_SUCCESS
        
    def aggregate_find(
        self,
        aggregate_info:list
        ):
        cursor = mongo[self.table].aggregate(aggregate_info)
        result=[]
        if cursor:
            for data in cursor:
                result.append(data)
        if len(result) <= 0:
            status = CourseStatusCode.FIND_ERROR_NULL
        else:
            status = CourseStatusCode.FIND_SUCCESS
        return result,status
        
    def addTimeStamp(self, data):
        date = int(time.time()) - 24 * 60 * 60 * 2
        data['date'] = date
        inserted_id = mongo[self.table].insert_one(data).inserted_id
        return inserted_id
    

CourseInfo = _CourseInfo()


class _Course_Student(_CourseInfo):
    def __init__(self):
        super(_Course_Student,self).__init__()
        self.table=T_COURSE_STUDENT

CourseStudent=_Course_Student()

class _Course_Teacher(_CourseInfo):
    def __init__(self):
        super(_Course_Teacher,self).__init__()
        self.table=T_COURSE_TEACHER
        
CourseTeacher=_Course_Teacher()

class _Experiment(_CourseInfo):
    def __init__(self):
        super(_Experiment,self).__init__()
        self.table=T_EXPERIMENT
Experiment=_Experiment()

class _Experiment_Course(_CourseInfo):
    def __init__(self):
        super(_Experiment_Course,self).__init__()
        self.table=T_EXPERMIENT_COURSE
ExperimentCourse=_Experiment_Course()

class _Experiment_Teacher(_CourseInfo):
    def __init__(self):
        super(_Experiment_Teacher,self).__init__()
        self.table=T_EXPERMIENT_TEACHER
ExperimentTeacher=_Experiment_Teacher()

class _Course_Time(_CourseInfo):
    def __init__(self):
        super(_Course_Time,self).__init__()
        self.table=T_COURSE_TIME
CourseTime=_Course_Time()

class _Semesters(_CourseInfo):
    def __init__(self):
        super(_Semesters,self).__init__()
        self.table=T_SEMESTERS
Semesters=_Semesters()

class _Notebook(_CourseInfo):
    def __init__(self):
        super(_Notebook,self).__init__()
        self.table=T_NOTEBOOK
Notebook=_Notebook()

class _Teaidxsta(_CourseInfo):
    def __init__(self):
        super(_Teaidxsta,self).__init__()
        self.table=T_TEAIDXSTA
TeacherIdxStatistics=_Teaidxsta()