'''
-*- coding:utf-8 -*-
@Project ：jxsjpt 
@File    ：homeworkApi.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2023/3/18 18:33 
@desc    ：用于学生做作业界面展示，展示作业题目信息
'''
import traceback

from flask import request, session
from flask_restful import Resource, reqparse

from common.utils.logger import logger
from core.data.response import Response
from core.hwService.handInService import HandInService




class StudentDoHworkApi(Resource):
    def __init__(self):
        self.businessService = HandInService()

    def get(self):
        # parser = reqparse.RequestParser()
        # parser.add_argument('birthday', type=inputs.date, help='生日字段验证错误！')
        # parser.add_argument('telphone', type=inputs.regex(r'1[3578]\d{9}'))
        # parser.add_argument('home_page', type=inputs.url, help='个人中心链接验证错误！')
        # parser.add_argument('username', type=str, help='用户名验证错误！', required=True)
        # parser.add_argument('password', type=str, help='密码验证错误！')
        # parser.add_argument('_id', type=str, help='作业id没有上传！', required = True)
        # parser.add_argument('courseId', type=int,  help='课程id！', required = True)
        # parser.add_argument('gender', type=str, choices=['male', 'female', 'secret'])
        args = request.args
        try:
            # args = parser.parse_args()
            _id = args.get('_id', type=str)
            isDone = args.get('isDone',type=int)# 0 未做 1 已做
            jsonData = {
                '_id':_id
            }
            result = self.businessService.toDoHomework(jsonData,isDone)
            for x in result:
                if x["question"].get("ref_answer"):
                    del x["question"]["ref_answer"]
            return Response.success(data=result, message="")
        except Exception as e:
            logger.warning("学生端获取作业习题信息异常 failed: {}".format(e))
            logger.exception(e)
            return Response.failed(data="", message=e)
    def post(self):
        parser = reqparse.RequestParser()
        # parser.add_argument('birthday', type=inputs.date, help='生日字段验证错误！')
        # parser.add_argument('telphone', type=inputs.regex(r'1[3578]\d{9}'))
        # parser.add_argument('home_page', type=inputs.url, help='个人中心链接验证错误！')
        # parser.add_argument('username', type=str, help='用户名验证错误！', required=True)
        # parser.add_argument('password', type=str, help='密码验证错误！')
        parser.add_argument('_id', type=str, help='作业id没有上传！', required = True)
        parser.add_argument('isHandIn', type=int,  help='是否提交！', required = True,choices=[0,1])
        parser.add_argument('handInDetails', type=dict, action = 'append', help='答案明细！', required=True)
        # parser.add_argument('gender', type=str, choices=['male', 'female', 'secret'])
        try:
            user_from = session.get("user_from")#判断当前是否是ilab用户提交的作业
            access_token = session.get("access_token")
            args = parser.parse_args()
            hw_id = args.get('_id')
            isHandIn = args.get('isHandIn')
            handInDetails = args.get('handInDetails')
            jsonData = {
                '_id':hw_id,
                'isHandIn': isHandIn,
                'handInDetails': handInDetails,
                'user_from': user_from,
                'access_token': access_token
            }
            result = self.businessService.saveHandIn(jsonData)
            if result==True:
                return Response.success(message=result)
            else:
                return Response.failed(message=result)
        except Exception as e:
            logger.warning("学生端提交作业习题异常 failed: {}".format(traceback.format_exc()))
            logger.exception(e)
            return Response.failed(data="", message=e)