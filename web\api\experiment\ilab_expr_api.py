from flask import request, session
from flask_restful import Resource,reqparse
from common.utils.logger import logger
from common.utils.dict_parse import DictParseType
from core.data.response import Response
from core.databases.orm.exam.exam_manage_orm import DBExamPlatformAdmin
from datetime import datetime, timedelta
from bson import ObjectId
from flask_restful.reqparse import Argument
from pymongo import MongoClient
from config import Config
from pymongo.errors import OperationFailure
from core.databases.orm.user.organzation_orm import DBCoursePlatformStuOrg
from core.databases.orm.course.course_orm import CourseInfo
from core.databases.orm.experiment.ilab_experiment_orm import DBExprforAdmin
from core.auth.ilab.ilab_expr_service import *
from common.utils.ilab_util import *
import json
from datetime import datetime

# 日期格式
date_format = '%Y-%m-%dT%H:%M:%SZ'
class ExprStepCorrect(Resource):
    def __init__(self) -> None:
        self.businessService=ilabExprService()
    def post(self):
        '''
        获取学生实验结果并批改
        API：/api/v1/user/ilab/correct

        @param  startTime   str   实验开始时间
        @param  endTime     str   实验结束时间
        @param  courseId    str   实验id
        @param  chapterId   str   实验记录id
        @param  flag        str   学生提交的结果
        '''
        parser_exam_create = reqparse.RequestParser()
        parser_exam_create.add_argument('startTime', type=str, required = True)
        parser_exam_create.add_argument('endTime', type=str, required = True)
        parser_exam_create.add_argument('courseId', type=str, required = True)
        parser_exam_create.add_argument('chapterId', type=str, required = True)
#        parser_exam_create.add_argument('repeatCount', type=int, required = True)
        parser_exam_create.add_argument('flag', type=str, required = True)
        args = parser_exam_create.parse_args()

        access_id = session.get("id")
        access_role = session.get("role")
        course=CourseInfo.find_by_id(ObjectId(args.get('courseId')),need={'chapter':1,'course_name':1})
        for item in course[0]['chapter']:
            if item['id']==args.get('chapterId'):
                chapter_name=item['name']
                break
        try:
            result={}
            result['user_id']=access_id
            result['title']=chapter_name
            result['startTime']=datetime.strptime(args['startTime'],date_format).timestamp()*1000+8*3600000
            result['endTime']=datetime.strptime(args['endTime'],date_format).timestamp()*1000+8*3600000
            result['timeUsed']=(result['endTime']-result['startTime'])/1000
            result['expectTime']=3600
            result['score']=0
            result['courseId']=args['courseId']
            result['chapterId']=args['chapterId']
            result['flag']=args['flag']

            score = self.businessService.correct(result)['score']
            access_token=session.get('access_token')
            appid=str(IlabUtils().appid)
            res=ilabExprService().sendreport(username=session.get("profile_id"),title=course[0]['course_name'],access_id=access_id,access_token=access_token,appid=appid)
            logger.info('报告提交结果：{}'.format(res))
            logger.info("真实ip{}".format(str(request.access_route[0])))
            #与3D场景对接
            if score and chapter_name=='实验三':
                Nickname=session.get('user_name')
                threeD_res=ilabExprService().threeDAttack(theme='waterprocess',hostIP=request.access_route[0],tag='valve1',status=1,Nickname=Nickname)
                logger.info("3D场景对接{}".format(threeD_res))
            if score and chapter_name=="实验四":
                Nickname=session.get('user_name')
                threeD_res=ilabExprService().threeDAttack(theme='waterprocess',hostIP=request.access_route[0],tag='pump2',status=1,Nickname=Nickname)
                logger.info("3D场景对接{}".format(threeD_res))
            return Response.success(data=str(result), message='')
        except Exception as e:
            logger.warning('insert expr faild: {}'.format(e))
            logger.exception(e)
            return Response.failed(data='', message=e)
        
    