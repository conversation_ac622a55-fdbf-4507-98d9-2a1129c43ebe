"""
-*- coding:utf-8 -*-
@Project ：jxsjpt
@File    ：question_service.py
@IDE     ：PyCharm
<AUTHOR>
@Date    ：2023/3/18 17:08
@desc    ：处理布置作业相关的业务
"""
from bson import ObjectId

from core.databases.orm.homework.question_orm import QuestionOrm



class QuestionService:
    def __init__(self):
        self.dbOrm = QuestionOrm()

    def save_question(self, jsonData):
        # question = Question(jsonData)
        data = {
            # "serial_No": jsonData['serial_No'],
            "question_type": jsonData['question_type'],
            "course_id": jsonData['course_id'],
            "knowledge_label": jsonData['knowledge_label'],
            "degree": jsonData['degree'],
            "construct_time": jsonData['construct_time'],
            "builder": jsonData['builder'],
            "chapter_id": jsonData['chapter_id'],
            "question_content": jsonData['question_content'],
            "question_options": jsonData['question_options'],
            "answer_content": jsonData['answer_content'],
            "is_delete": jsonData['is_delete'],
            'ref_answer': jsonData['ref_answer']
        }
        item = self.dbOrm.add_one(data)
        return item

    def query_question(self, jsonData,page,pageSzie):
        # question = Question(jsonData)
        # if serialNo
        # data = {
        #     "_id": jsonData['_id'],
        #     "question_type": jsonData['question_type'],
        #     "course_id": jsonData['course_id'],
        # }
       return self.dbOrm.find_by_page(jsonData,page,pageSzie)
    def query_total(self, jsonData):
        return self.dbOrm.count(jsonData)
    def query_detail(self,jsonData):
        return self.dbOrm.find_one(jsonData)
    def update_question(self, jsonData):
        # question = Question(jsonData)
        condition = {"_id": ObjectId(jsonData['_id'])}
        dao_object = {"question_content": jsonData['question_content'],
                      "question_options": jsonData['question_options'],
                      "answer_content": jsonData['answer_content'],
                      'ref_answer': jsonData['ref_answer'],
                      'knowledge_label': jsonData['knowledge_label'],
                      'degree': jsonData['degree']
                      }
        return self.dbOrm.update_hw_question(condition, dao_object)


    def delete_question(self, jsonData):
        # question = Question(jsonData)
        jsonData = {"_id": ObjectId(jsonData["_id"])}
        return self.dbOrm.update_by_condition(jsonData, {'is_delete': True})
        # self.dbOrm.delete_hw_question(question.toDbType())
