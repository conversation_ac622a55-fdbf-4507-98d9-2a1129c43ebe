#!/bin/bash
apikey=**************************
userpwd=sgv#98756
filename=gwj.tar
filepath=jxsjpt-develop

pid=$(ps aux | grep python | grep -v grep | awk '{print $2}')

echo "[+]进程id为：$pid"

echo $userpwd |  sudo -S kill -9 $pid
echo "[+]has kiiled process"

echo "[+]delete raw tar pkg"
rm $filename

echo "[+]download new tar pkg"
url="http://222.20.126.145/gwj/jxsjpt/-/archive/develop/jxsjpt-develop.tar"

curl --header "Private-Token:${apikey}" -o $filename -L $url

sleep 2
wait

echo "[+]untar new tar pkg"
tar -xvf $filename 

cd $filepath

echo "[+]activate conda env and start service"
eval "$(conda shell.bash hook)"
conda activate courseplatform_env && nohup python3 course_platform_manager.py &
