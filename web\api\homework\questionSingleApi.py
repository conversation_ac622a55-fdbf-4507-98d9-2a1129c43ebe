"""
-*- coding:utf-8 -*-
@Project ：jxsjpt
@File    ：homeworkApi.py
@IDE     ：PyCharm
<AUTHOR>
@Date    ：2023/3/18 18:33
@desc    ：用于题单条查询
"""
import time

from bson import ObjectId
from flask import request
from flask_restful import Resource

from common.utils.logger import logger
from core.data.response import Response
from core.hwService.question_service import QuestionService


class QuestionSingleApi(Resource):
    def __init__(self):
        self.businessService = QuestionService()

    def get(self):
        try:
            jsonData = []

            _id = request.args.get('_id', type=str)
            query_condition = []
            query_condition.append({'is_delete': False})
            query_condition.append({'_id': ObjectId(_id)})
            """
            条件查询
            """
            question = self.businessService.query_detail(
                {
                    "$and": query_condition
                }
            )

            data = {
                "_id": str(question['_id']),
                "question_type": question['question_type'],
                "degree": question['degree'],
                "knowledge_label": question['knowledge_label'],
                "construct_time": question['construct_time'],
                "builder": question['builder'],
                "course_id": question['course_id'],
                "chapter_id": question['chapter_id'],
                "question_content": question['question_content'],
                "question_options": question['question_options'],
                "answer_content": question['answer_content'],
                'ref_answer': question['ref_answer']
            }

            return Response.success(data=data, message="查询成功")

        except Exception as e:
            logger.warning("get homework question failed: {}".format(e))
            logger.exception(e)
            return Response.failed(message=e)


