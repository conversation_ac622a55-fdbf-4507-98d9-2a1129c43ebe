'''
-*- coding:utf-8 -*-
@Project ：jxsjpt 
@File    ：teachCorrectApi.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2023/3/18 18:33 
@desc    ：用于老师查询学生提交作业详情,批改作业
'''
from flask import request
from flask_restful import Resource, reqparse

from common.utils.logger import logger
from core.data.response import Response
from core.hwService.hworkCorrectService import HworkCorrectService




class TeacherCorrectApi(Resource):
    def __init__(self):
        self.businessService = HworkCorrectService()

    def post(self):
        parser = reqparse.RequestParser()
        parser.add_argument('byStu', type=int, choices=[1,2]) # 批改方式，1 按学生 2按题

        parser.add_argument('teacherId', type=str)# 批改老师
        parser.add_argument('note', type=str)# 作业评语
        parser.add_argument('obtainedScore', type=int)  # 作业总分
        parser.add_argument('correctDetails', type=dict, action='append', required=True)
        parser.add_argument('_id', type=str)  # 提交表id
        try:
            args = parser.parse_args()
            byStu = args.get('byStu')
            teacherId = args.get('teacherId')
            note = args.get('note')
            obtainedScore = args.get('obtainedScore')
            _id = args.get('_id')
            correctDetails = args.get('correctDetails')
            if byStu == 1:
                jsonData = {
                    'teacherId':teacherId,
                    'note':note,
                    'obtainedScore':obtainedScore,
                    'correctDetails':correctDetails,
                    '_id':_id
                }
                result = self.businessService.correctHworkByStu(jsonData)
            else:
                jsonData = {
                    'teacherId': teacherId,
                    # 'note': note,
                    # 'obtainedScore': obtainedScore,
                    'correctDetails': correctDetails
                }
                result = self.businessService.correctHworkByQues(jsonData)

            return Response.success(data=result, message="")

        except Exception as e:
            logger.warning("批改学生作业 failed: {}".format(e))
            logger.exception(e)
            return Response.failed(data="", message=e)

    def get(self):

        try:
            byStu = request.args.get('byStu', type=int)  # 批改方式，1 按学生 2按题

            _id = request.args.get('_id', type=str)# 按学生时，是提交表id，按题时，是作业明细表id

            classId = request.args.get('classId', type=str)# 班级id，用于按题批改时使用

            if byStu == 1:
                result = self.businessService.toCorrectByStuDetail(_id)
            else:
                result = self.businessService.toCorrectByQuesDetail(_id,classId)

            return Response.success(data=result, message="")
        except Exception as e:
            logger.warning("获取批改数据异常: {}".format(e))
            logger.exception(e)
            return Response.failed(data="", message=e)