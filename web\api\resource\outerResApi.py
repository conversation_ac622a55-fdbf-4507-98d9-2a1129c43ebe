'''
-*- coding:utf-8 -*-
@Project ：jxsjpt 
@File    ：instanceApi.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2023/7/21 21:49 
@desc    ：外部资源操作接口
'''
import time

from flask import request, session
from flask_restful import Resource, reqparse

from common.utils.logger import logger
from core.data.response import Response
from core.middle.outerResService import OuterResService


class OuterResApi(Resource):
    def __init__(self):
        self.businessService = OuterResService()

    def get(self):
        try:
            page = request.args.get('page', type=int)
            size = request.args.get('page_size', type=int)
            course_id = request.args.get('course_id', type=str)

            instances = self.businessService.queryOuterRes(
                {"course_id":course_id}, page, size )
            return Response.success(data=instances, message="")

        except Exception as e:
            logger.warning("get OuterRes failed: {}".format(e))
            logger.exception(e)
            return Response.failed(message=e)

    def post(self):
        """
        新增实例：
        """
        parser = reqparse.RequestParser()
        parser.add_argument('resource_name', type=str)
        parser.add_argument('resource_depict', type=str)
        parser.add_argument('resource_url', type=str)
        parser.add_argument('course_id', type=str)
        # parser.add_argument('update_time', type=str)
        # parser.add_argument('update_user', type=str)
        try:

            args = parser.parse_args()
            resource_name = args['resource_name']
            resource_depict = args['resource_depict']
            resource_url = args['resource_url']
            course_id = args['course_id']
            # update_time = args['update_time']
            # update_user = args['update_user']
            jsonData = str(self.businessService.saveOuterRes({
                # 'create_time': int(time.strftime("%Y%m%d%H%M%S", time.localtime())),
                'resource_name': resource_name,
                'resource_depict': resource_depict,
                'resource_url': resource_url,
                'course_id': course_id,
                'update_time': int(time.strftime("%Y%m%d%H%M%S", time.localtime())),
                'update_user': session.get("id")
            }))
            if jsonData == 'False':
                return Response.failed(message="外部资源名称已存在")
            return Response.success(message=jsonData)
        except Exception as e:
            logger.warning("save OuterRes failed: {}".format(e))
            logger.exception(e)
            return Response.failed(message=e)

    def delete(self):
        try:
            parser = reqparse.RequestParser()
            parser.add_argument('_id', type=str)
            args = parser.parse_args()
            _id = args.get("_id")
            reuslt = self.businessService.deleteOuterRes(_id)
            return Response.success( message=reuslt )
        except Exception as e:
            logger.warning("delete instance failed: {}".format(e))
            logger.exception(e)
            return Response.failed(message=e)

    def put(self):
        parser = reqparse.RequestParser()
        parser.add_argument('resource_name', type=str)
        parser.add_argument('resource_depict', type=str)
        parser.add_argument('resource_url', type=str)
        parser.add_argument('_id', type=str)
        try:
            args = parser.parse_args()
            resource_name = args['resource_name']
            resource_depict = args['resource_depict']
            resource_url = args['resource_url']
            _id = args.get("_id")
            jsonData = self.businessService.modifyOuterRes(
                {
                    'resource_name': resource_name,
                    'resource_depict': resource_depict,
                    'resource_url': resource_url,
                    'update_time': int(time.strftime("%Y%m%d%H%M%S", time.localtime())),
                    'update_user': session.get("id")
                }, _id
            )

            return Response.success( message=jsonData )

        except Exception as e:
            logger.warning("update OuterRes status failed: {}".format(e))
            logger.exception(e)
            return Response.failed(message=e)