import random
from flask import request, session
from flask_restful import Resource,reqparse
from common.utils.logger import logger
from common.utils.dict_parse import DictParseType
from core.data.response import Response
from core.databases.orm.exam.exam_manage_orm import DBExamPlatformAdmin
from datetime import datetime, timedelta
from bson import ObjectId
from flask_restful.reqparse import Argument
from pymongo import MongoClient
from config import Config
from pymongo.errors import OperationFailure
from core.databases.orm.user.organzation_orm import DBCoursePlatformStuOrg
from core.databases.orm.course.course_orm import CourseInfo
from core.databases.orm.experiment.ilab_experiment_orm import DBExprforAdmin
from core.hwService.hworkCorrectService import HworkCorrectService
from common.utils.ilab_util import *
import json
import copy
import requests

scoringModel="每个实验正确得12分，错误得0分"
expr_seq={
    "实验一":1,
    "实验二":2,
    "实验三":3,
    "实验四":4,
    "实验五":5,
    "实验报告":6
}
class ilabExprService:
    def __init__(self) -> None:
        pass
    def correct(self,record):
#TODO：对提交的内容进行批改
        correct_res=HworkCorrectService().autoCorrect(title=record['title'],course_id=record['courseId'],user_id=record['user_id'],flag=record['flag'])
        record['repeatCount']=1
        record['maxScore']=correct_res['maxScore']
        record['score']=correct_res['score']
        record['evaluation']=correct_res['evaluation']
        record['scoringModel']=scoringModel
        res=DBExprforAdmin.insert_expr_record(record)
        return record
    
    def getSteps(self,access_id):
        request={
            'user_id':access_id
        }
        res=DBExprforAdmin.find(request).sort('chapterId')
        step=1
        steps=[]
        for item in res:
            item['seq']=expr_seq[item['title']]
            step+=1
            item.pop('flag')
            item.pop('_id')
            item.pop('user_id')
            item.pop('courseId')
            item.pop('chapterId')
            steps.append(item)
        return steps
    
    def sendreport(self,username,title,access_id,access_token,appid):
        # parser_exam_create = reqparse.RequestParser()
        # parser_exam_create.add_argument('username', type=str, required = True)
        # parser_exam_create.add_argument('title', type=str, required = True)
        # parser_exam_create.add_argument('originId', type=str, required = True)
        # # parser_exam_create.add_argument('group_id', type=str, required = True)
        # # parser_exam_create.add_argument('group_name', type=str, required = True)
        # # parser_exam_create.add_argument('role_in_group', type=str, required = True)
        # # parser_exam_create.add_argument('group_members', type=str, required = True)
        # args = parser_exam_create.parse_args()
        steps=self.getSteps(access_id)

        score=0
        status=2
        for step in steps:
            score+=step['score']
        result={
#                'username':session.get("profile_id"),
            'username':username,
            'title':title,
            'startTime':float('inf'),
            'endTime':0,
            'timeUsed':0,
            'status':status,
            'score':score,
            'appid':appid,
            'originId':access_id,
            'steps':steps
            # 'group_id':args.get('group_id'),
            # 'group_name':args.get('group_name'),
            # 'role_in_group':args.get('role_in_group'),
            # 'group_members':args.get('group_members'),
            # 'ext_data':''
        }
        start = int(datetime.now().timestamp())*1000-600000
        mid = int(datetime.now().timestamp())*1000-300000
        end = int(datetime.now().timestamp())*1000
        for step in result["steps"]:
            step['startTime']= random.randint(start,mid)//1000*1000
            step['endTime']= random.randint(mid,end)//1000*1000
            step['timeUsed']=(step['endTime']-step['startTime'])//1000  
        if score==100:
            result['status']=1
            steps_a = copy.deepcopy(result["steps"])
            steps_b = copy.deepcopy(result["steps"])
            for step in steps_a:
                t1 = random.randint(0,2*60)
                t2 = random.randint(2*60,4*60)
                step['startTime']= step['startTime']-t2*1000
                step['endTime']= step['endTime']-t1*1000
                step['timeUsed']=(step['endTime']-step['startTime'])//1000  
                step["score"]/=2
                step["maxScore"]/=2
                step["title"]+='A'
            for step in steps_b:
                t1 = random.randint(0,2*60)
                t2 = random.randint(2*60,4*60)
                step['startTime']= step['startTime']+t1*1000
                step['endTime']= step['endTime']+t2*1000
                step['timeUsed']=(step['endTime']-step['startTime'])//1000  
                step["score"]/=2
                step["maxScore"]/=2
                step["title"]+='B'
                step["seq"]+=6
            result["steps"] = sorted(steps_a+steps_b,key=lambda x:x["title"])
        for step in result["steps"]:
            if result['startTime']>step['startTime']:
                result['startTime']=step['startTime']
            if result['endTime']<step['endTime']:
                result['endTime']=step['endTime']
        result['timeUsed']=(result['endTime']-result['startTime'])//1000
        res=IlabUtils().send_experimental_data(data=result,access_token=access_token)
        logger.info('报告：{}'.format(result))
        return res
    
    def threeDAttack(self,theme,hostIP,tag,status,Nickname):
        headers = {"Content-Type": "application/json; charset=UTF-8",
                   "User-Agent": "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/31.0.1650.63 Safari/537.36"
                   }
        create_data={
            "ip":hostIP,
            "createNickname":Nickname
        }
        ins_data={
            'theme':theme,
            'hostIp':hostIP,
            'tag':tag,
            'status':status
        }
        data_list=[]
        data_list.append(create_data)
        data_json=json.dumps(data_list)
        logger.info('录入实验人员信息{}'.format(data_json))
        res=requests.post(Config.threeD_PLATFORM+'/api-dataservice/api/hk/insertNickName',data=data_json,headers=headers,verify=False)
        logger.info('录入实验人员信息{}'.format(res.text))
        data_list.pop(0)
        data_list.append(ins_data)
        data_json=json.dumps(data_list)
        res=requests.post(Config.threeD_PLATFORM+'/api-dataservice/api/hk/insertCourseAttackEvent',data=data_json,headers=headers,verify=False)
        logger.info('发起攻击{}'.format(data_json))
        logger.info('发起攻击：{}'.format(res.text))
        return res