import time
import datetime

def compare_time(stm:str,etm:str)->bool:
    _,ntm = str2utc(time.strftime("%Y-%m-%d"))
    _,stmm = str2utc(stm)
    _,etmm = str2utc(etm)
    if ntm < stmm :
        return 1
    elif ntm <= etmm :
        return 2
    else :
        return 3

def str2utc(date_string:str,format_str:str = None) -> (str,datetime.datetime):
    formatstr = format_str or '%Y-%m-%dT%H:%M:%SZ'
    
    now_stamp = time.time()
    local_time = datetime.datetime.fromtimestamp(now_stamp)
    utc_time = datetime.datetime.utcfromtimestamp(now_stamp)
    offset = local_time - utc_time
    
    if 'T' in date_string:
        str_time = datetime.datetime.strptime(date_string, '%Y-%m-%dT%H:%M:%SZ')
    elif ':' in date_string:
        str_time = datetime.datetime.strptime(date_string, "%Y-%m-%d-%H:%M")
    else:
        str_time = datetime.datetime.strptime(date_string, '%Y-%m-%d')
    # 将本地时间转换为UTC时间
    utc_time = str_time - offset
    return utc_time.strftime(formatstr),utc_time

def utc2loc(utc_time:datetime.datetime,format_str:str = None) -> (str,datetime.datetime):
    formatstr = format_str or '%Y-%m-%d'
    
    now_stamp = time.time()
    local_time = datetime.datetime.fromtimestamp(now_stamp)
    utc_time = datetime.datetime.utcfromtimestamp(now_stamp)
    offset = local_time - utc_time
    # 将UTC时间转换为本地时间
    loc_time = utc_time + offset
    return local_time.strftime(formatstr),local_time
