"""
-*- coding:utf-8 -*-
@Project ：jxsjpt 
@File    ：handInDetail_orm.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2023/3/18 18:30 
@desc    ：
"""
from core.databases.db_mongo import mongo
from core.databases.orm.homework.hwDatabase import HwDatabase


class ClassHIHwOrm(HwDatabase):
    def __init__(self):
        HwDatabase.__init__(self)
        self.table = "hw_clas_handin_table"

    def teaStati(self, teaId, courseIds):
        cursor = mongo[self.table].aggregate([
            {
                "$lookup":
                    {
                        "from": "hw_handIn_hwork_table",
                        "let": {"homeworkNo": "$homeworkNo",
                                "courseId": "$courseId",
                                "classId": "$classId"},
                        "pipeline": [
                            {
                                "$match": {
                                    "$expr": {
                                        "$and": [
                                            {"$eq": ["$$homeworkNo", "$hworkNo"]},
                                            {"$eq": ["$$courseId", "$courseId"]},
                                            {"$eq": ["$$classId", "$classId"]},
                                            {"$gte": ["$state", 2]},
                                        ]
                                    }
                                }
                            }
                        ],
                        "as": "hand_info"
                    }
            },
            {
                "$match": {
                    "$expr": {
                        "$and": [
                            {"$in": ["$courseId", courseIds]}
                        ]
                    }
                }
            },
            {
                "$project": {
                    "_id": {"$toString": "$_id"},
                    "courseId": 1,
                    "className": 1,
                    "obtainedScore": "$hand_info.obtainedScore",
                    "classId": 1,
                    "homeworkName": 1,
                    "studentCount":1,
                    "handedCount":1,
                    "endTime":1
                }
            }
        ])
        result = []
        if cursor:
            for data in cursor:
                result.append(data)
        return result