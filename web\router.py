#!/usr/bin/env python
# -*- coding: utf-8 -*- 
# @Time   : 5/10/21 6:43 AM 
# <AUTHOR> jackey 
# @File   : router.py
# @desc   : ""


from flask_restful import Api

from web.api.statistics.stuOrtea_course_info import GetStuCourseInfo, GetTeaCourseInfo
from web.api.statistics.stu_my_grade import  stuHwGrade,stuExamGrade
from web.api.statistics.study_info import  StuStudyInfo, TeachInfo
from web.api.statistics.tea_teach_grade import  TeaHwGrade, TeaExamGrade
from web.api.user.ilab.ilab_login import ILABLogin
# from flask import render_template
# from web.flask_app import flask_app
# from web.api.config.settings import AccountManageV1
# from web.api.auth.access_auth_api import UserManageV1, TokenManageV1
# from web.api.auth.user_api import WhoAreYouV1
from web.api.user.organization_api import StuOrganization, StuOrganizations
# from web.flask_app import flask_app
# from web.api.config.settings import AccountManageV1, BasicConfigMangeV1
# from web.api.auth.access_auth_api import UserManageV1, TokenManageV1
# from web.api.auth.user_api import WhoAreYouV1

from web.api.user.user_api import Login, Logout, Teachers, Users, User
from web.api.test.test import TestTest
from web.api.resource.resource_handler import ResourceHandler, ResourceIDHandler
from web.api.resource.file_handler import FileIDHandler, FileHandler
from web.api.course.course import *
from web.api.course.sector import *
from web.api.course.course_teacher import *
from web.api.course.course_student import *
from web.api.course.Experiment import *
from web.api.course.course_admin import *
from web.api.course.course_time import *

from web.api.homework.homeworkApi import HomeworkApi
from web.api.homework.questionApi import QuestionApi
from web.api.homework.questionSingleApi import QuestionSingleApi
from web.api.homework.hworkIssueApi import HworkIssueApi
from web.api.homework.stuHworkApi import StudentHworkApi
from web.api.homework.studoHworkApi import StudentDoHworkApi
from web.api.homework.hworkLibraryApi import HworkLibraryApi
from web.api.homework.teachCallBackApi import TeacherCallBackApi
from web.api.homework.teachCorrectApi import TeacherCorrectApi
from web.api.homework.teachQueryCorrectApi import TeacherQueryCorrectApi
from web.api.homework.statisticsHw import StatisticsHw
from web.api.exam.paper_api import *
from web.api.exam.exam_manage_api import  *
from web.api.user.user_avatar_api import AvatarLooker, AvatarUploader 


# from web.api.resource.file_handler import upload_test
# flask_app.register_blueprint(blue_view)
from web.api.admin.instanceApi import InstanceApi, InstanceActivateCheck
from web.api.resource.outerResApi import OuterResApi

from web.api.statistics.teaIndexStatistics import TeaIndexStatistics
from web.api.statistics.stuRank import stuRank
from web.api.statistics.stuIndexStatistic import stuIndexStatistic

from web.api.experiment.ilab_expr_api import ExprStepCorrect


api = Api(flask_app)

# ************************Resource & File****************************
api.add_resource(ResourceHandler, "/api/v1/resource")
api.add_resource(ResourceIDHandler, "/api/v1/resource/<resource_id>")
api.add_resource(FileHandler, "/api/v1/file")
api.add_resource(FileIDHandler, "/api/v1/file/<path:file_name>")
# ************************账户接口****************************
# api.add_resource(WhoAreYouV1, "/api/v1/account/who")
# api.add_resource(UserManageV1, "/api/v1/account/admin")
# api.add_resource(TokenManageV1, "/api/v1/account/token")   #获取token
# api.add_resource(AccountManageV1, "/api/v1/account/settings/user", "/api/v1/account/settings/user/<uid>")

api.add_resource(Login, "/api/v1/user/login")
api.add_resource(Logout, "/api/v1/user/logout")

api.add_resource(AvatarUploader, "/api/v1/user/avatar/upload")
api.add_resource(AvatarLooker, "/api/v1/user/avatar/<id>")


api.add_resource(Users, 
                 "/api/v1/<role>/info",
                 "/api/v1/<role>/create")
api.add_resource(User, "/api/v1/<role>/<id>")

api.add_resource(StuOrganizations, "/api/v1/teacher/organizations")
api.add_resource(StuOrganization, "/api/v1/teacher/organization/<id>")

# ************************TEST****************************
api.add_resource(TestTest, "/api/v1/test/test_args")
# ************************课程接口****************************
api.add_resource(CourseSearchPage, "/api/v1/course/search")
# /api/v1/course/search?course_name=<course_name>&teacher=<teacher>&organization=<organization>
api.add_resource(CourseDisplayPage, 
                 "/api/v1/course/NCC-1",
                 "/api/v1/student/course",
                 "/api/v1/student/experiment",
                 "/api/v1/teahcer/course",
                 "/api/v1/teahcer/experiment",
                 "/api/v1/admin/course",
                 "/api/v1/admin/experiment",
                 )
# /api/v1/course/course/NCC-1?id=<_id>&course_name=<course_name>
api.add_resource(CourseCreatePage, 
                 "/api/v1/teacher/create_course",
                 "/api/v1/admin/create_course",
                 )
# api.add_resource(CourseDisplayPage,"/api/v1/course/course_detail")
# /api/v1/course/course/course_detail?id=<_id>&course_name=<course_name>
api.add_resource(CourseLearnPage, 
                 "/api/v1/course/view",
                 "/api/v1/student/course/learn",
                 "/api/v1/student/experiment/learn",
                 "/api/v1/teacher/course/view",
                 "/api/v1/teacher/experiment/view",
                 "/api/v1/admin/course/view",
                 "/api/v1/admin/experiment/view"
                 )
# GET: /api/v1/course/view?id=<course_id>&user=<user_id>
# PUT: /api/v1/course/view?id=<course_id>&user=<user_id>&time=<page_stay_time(min)>&chapter=<chapter_name>
api.add_resource(CourseDelete, 
                 "/api/v1/teacher/delete_course",
                 "/api/v1/admin/delete_course"
                 )
# DELET:/api/v1/course/delet?id=<course_id>
api.add_resource(CourseUpdatePage, 
                 "/api/v1/teacher/update_courseinfo",
                 "/api/v1/admin/update_courseinfo",
                 )
# POST /api/v1/course/teacher_center/update_courseinfo?course_id=121212121212

# *************************课程表接口**************************
#POST /api/v1/teacher/create_scheme
api.add_resource(baseScheme,
                 "/api/v1/teacher/create_scheme",
                 "/api/v1/admin/create_scheme"
)
# GET /api/v1/teacher/update_scheme?course_id=xxxxxxxxx
# POST /api/v1/teacher/update_scheme
api.add_resource(updateScheme,
                 "/api/v1/teacher/update_scheme",
                 "/api/v1/admin/update_scheme"
)
# GET /api/v1/teacher/progress?course_id=xxxxxxxxx
api.add_resource(getProgress,
                 '/api/v1/student/progress',
                 '/api/v1/teacher/progress',
                 '/api/v1/admin/progress')

# GET /api/v1/role/get_scheme?week=13
api.add_resource(getWeekCourse,
                 "/api/v1/student/get_scheme",
                 "/api/v1/teacher/get_scheme",
                 "/api/v1/admin/get_scheme")

# GET /api/v1/role/get_week_courseinfo
api.add_resource(getWeekCourseInfo,
                 "/api/v1/student/get_week_courseinfo",
                 "/api/v1/teacher/get_week_courseinfo",
                 "/api/v1/admin/get_week_courseinfo")

api.add_resource(GetTeacherScheme,
                 "/api/v1/student/allscheme",
                 "/api/v1/teacher/allscheme",
                 "/api/v1/admin/allscheme"
)

# *************************学期管理***************************
# POST /api/v1/role/semester/create
api.add_resource(CreateSemesters,
                 '/api/v1/teacher/semester/create',
                 '/api/v1/admin/semester/create')

#GET /api/v1/role/semester/all #获取所有学期
api.add_resource(getSemesters,
                 '/api/v1/student/semester/all',
                 '/api/v1/teacher/semester/all',
                 '/api/v1/admin/semester/all')
# GET  /api/v1/role/semester/update?id=xxxxxxxx
# POST /api/v1/role/semester/update
# DELETE /api/v1/role/semester/update?id=xxxxxxxx
api.add_resource(UpdateSemesters,
                 '/api/v1/teacher/semester/update',
                 '/api/v1/admin/semester/update')
# GET /api/v1/role/semester/now?id=xxxxxxxxxx
api.add_resource(setNowSemester,
                 '/api/v1/teacher/semester/now',
                 '/api/v1/admin/semester/now')
# GET /api/v1/role/semester/calendar(?semester=2023-1)
api.add_resource(getCalendar,
                 '/api/v1/student/semester/calendar',
                 '/api/v1/teacher/semester/calendar',
                 '/api/v1/admin/semester/calendar')
# GET /api/v1/role/semester/time
api.add_resource(SummerWinterTime,
                 '/api/v1/student/semester/time',
                 '/api/v1/teacher/semester/time',
                 '/api/v1/admin/semester/time'
                 )
                 

# *************************实验接口***************************
api.add_resource(ExperimentCreate,
                 "/api/v1/teacher/create_experiment",
                 "/api/v1/admin/create_experiment",
                 )
api.add_resource(UpdateExperiment,
                 "/api/v1/teacher/update_experiment",
                 "/api/v1/admin/update_experiment",
                 )
#/api/v1/course/teacher_center/update_experiment?experiment_id=xxxxxx
api.add_resource(DeleteExperiment,
                 "/api/v1/teacher/delete_experiment",
                 "/api/v1/admin/delete_experiment",
                 )
#/api/v1/course/teacher_center/delete_experiment?experiment_id=xxxxxx
api.add_resource(GetExperimentInfo,
                 "/api/v1/teacher/chapter/experiment",
                 "/api/v1/student/chapter/experiment",
                 "/api/v1/admin/chapter/experiment",
                 )
#/api/v1/course/teacher_center/experiment?experiment_id=xxxxxx


# *************************课程与学生相关**********************
api.add_resource(StudentChooseCourse, "/api/v1/student/chosen_course")
#api.add_resource(CourseDisplayPage,"/api/v1/course/student_center/course")
#api.add_resource(CourseLearnPage,"/api/v1/course/student_center/course/learn")
api.add_resource(StudentChooseExperiment,"/api/v1/student/chosen_experiment")
#api.add_resource(CourseDisplayPage,"/api/v1/course/student_center/experiment")
#api.add_resource(CourseLearnPage,"/api/v1/course/student_center/experiment/learn")
api.add_resource(SaveExperimentResource,"/api/v1/student/experiment/resource")
api.add_resource(StudentHWork,"/api/v1/student/hwork")
from web.api.course.notebook import *
api.add_resource(Editnote,
                 "/api/v1/student/course/notes"
                 )


# *************************课程与教师相关**********************
api.add_resource(CourseAddStudent, 
                 "/api/v1/teacher/course/student_management",
                 "/api/v1/admin/course/student_management",
                 )
# /api/v1/teacher_center/course/student_management?course_id=<course_id>
api.add_resource(ShowTeacherCourse, 
                 "/api/v1/course/showteachercourse",
                 "/api/v1/teacher/taught_course",
                 )
api.add_resource(ShowTeacherExperiment,
                 "/api/v1/course/showteacherexperiment",
                 "/api/v1/teacher/taught_experiment",
                 )
# GET:/api/v1/test/test_mongo/showteachercourse?id=xxxxxxx
#api.add_resource(CourseDisplayPage,"/api/v1/course/teahcer/course")
#api.add_resource(CourseDisplayPage,"/api/v1/course/teahcer/experiment")
api.add_resource(UpdateCourseTeacher, 
                 "/api/v1/course/updateCourseTeacher",
                 "/api/v1/teacher/course/updateCourseTeacher",
                 "/api/v1/admin/course/updateCourseTeacher",
                 )
api.add_resource(TeacherAndExperimentResource,"/api/v1/teacher/experiment/resource")
# ***********************章节相关**********************
api.add_resource(ChapterUpdate, 
                 "/api/v1/course/sector/chapterupdate",
                 "/api/v1/teacher/sector/chapterupdate",
                 "/api/v1/admin/sector/chapterupdate",
                 )
# post形式以json格式传输_id,更新者updater以及更新内容chapter
api.add_resource(SectorListPage, 
                 "/api/v1/course/sector/SectorListPage",
                 "/api/v1/student/sector/SectorListPage",
                 "/api/v1/teacher/sector/SectorListPage",
                 "/api/v1/admin/sector/SectorListPage",
                 )
# get形式获取课程_id以及名称  ?id=<_id>&course_name=<course_name>

# *************************课程与管理端相关**********************
api.add_resource(ShowAllCourse,"/api/v1/admin/allcourse")
api.add_resource(ShowAllExperiment,"/api/v1/admin/allexperiment")
api.add_resource(ShowAllDeletedCourse,"/api/v1/admin/alldeletedcourse")
api.add_resource(ShowAllDeletedExperiment,"/api/v1/admin/alldeletedexperiment")
api.add_resource(ReversDeletedCourse,"/api/v1/admin/reverse")
#post:{"course_id":"xxxxxxxxx"}
api.add_resource(DeleteStuIns,"/api/v1/admin/deleteStuIns")

# api.add_resource(AggregateTest,
#                  '/api/v1/test')

# ***********************homework****************************
api.add_resource(HomeworkApi, "/api/v1/teacher/hw/homework")
api.add_resource(QuestionApi, "/api/v1/teacher/hw/question")
api.add_resource(QuestionSingleApi, "/api/v1/teacher/hw/questionSingle")
api.add_resource(HworkIssueApi, "/api/v1/teacher/hw/issueHwork")
api.add_resource(StudentHworkApi, "/api/v1/student/hw/stuHwork")
api.add_resource(StudentDoHworkApi, "/api/v1/student/hw/stuDoHwork")
api.add_resource(HworkLibraryApi, "/api/v1/teacher/hw/library")
api.add_resource(TeacherQueryCorrectApi, "/api/v1/teacher/hw/queryCorrect")
api.add_resource(TeacherCorrectApi, "/api/v1/teacher/hw/correct")
api.add_resource(TeacherCallBackApi, "/api/v1/teacher/hw/callback")
api.add_resource(StatisticsHw, "/api/v1/student/hw/statistics")


# ************************考试接口****************************

# 考试管理
api.add_resource(ExamManageV1, 
                 "/api/v1/exam/<course_id>",
                 "/api/v1/admin/exam/<course_id>",
                 "/api/v1/teacher/exam/<course_id>",
                 "/api/v1/student/exam/<course_id>")
api.add_resource(ExamDetailInfoManageV1, 
                 "/api/v1/exam/detail/info/<exam_id>",
                 "/api/v1/admin/exam/detail/info/<exam_id>",
                 "/api/v1/teacher/exam/detail/info/<exam_id>")
api.add_resource(ExamDetailRecordManageV1, 
                 "/api/v1/exam/detail/record/<exam_id>/<user_id>",
                 "/api/v1/admin/exam/detail/record/<exam_id>/<user_id>",
                 "/api/v1/teacher/exam/detail/record/<exam_id>/<user_id>")
# 试卷管理
api.add_resource(ExamPaperV1,
                 "/api/v1/exam/exam/paper",
                 "/api/v1/admin/exam/exam/paper",
                 "/api/v1/teacher/exam/exam/paper")
api.add_resource(DeletePaperV1, "/api/v1/exam/paper/del_paper",
                 "/api/v1/admin/exam/paper/del_paper", 
                 "/api/v1/teacher/exam/paper/del_paper")
api.add_resource(DeletePaper_numV1, "/api/v1/exam/paper/del_paper_num",
                 "/api/v1/admin/exam/paper/del_paper_num", 
                 "/api/v1/teacher/exam/paper/del_paper_num")

#ExamCenterManageV1
# 考试中心(学生端)
api.add_resource(ExamCenterManageV1, "/api/v1/student/exam/content/<exam_id>")
# 考试统计
api.add_resource(ExamStatV1,"/api/v1/teacher/exam/stat",
                 "/api/v1/student/exam/stat",
                 "/api/v1/exam/stat")
#这里注销掉，url不对
# api.add_resource(stuExamGrade,"/api/v1/student/exam/statistics")
# api.add_resource(TeaExamGrade,"/api/v1/teacher/exam/statistics")
#管理端实例查询
api.add_resource(InstanceApi, "/api/v1/admin/instances/manageInstances", "/api/v1/student/instances/manageInstances")
api.add_resource(InstanceActivateCheck, "/api/v1/student/instances/activateCheck")
api.add_resource(OuterResApi, "/api/v1/teacher/outerres/manageOuterRes")

#统计接口
api.add_resource(TeaIndexStatistics, "/api/v1/teacher/indexTop/statistics")
api.add_resource(stuRank,
                 "/api/v1/teacher/sturank",
                 "/api/v1/student/sturank")
api.add_resource(stuIndexStatistic,
                 "/api/v1/student/indexTop/statistics"
                 )
'''
服务学生端我的成绩，教师端数据分析
'''
api.add_resource(StuStudyInfo, "/api/v1/student/studyinfo/statistics")
api.add_resource(TeachInfo, "/api/v1/teacher/teachinfo/statistics")
api.add_resource(stuHwGrade,  "/api/v1/student/mygrade/hwork/statistics")
api.add_resource(stuExamGrade,  "/api/v1/student/mygrade/exam/statistics")
api.add_resource(TeaHwGrade,  "/api/v1/teacher/teachgrade/hwork/statistics")
api.add_resource(TeaExamGrade,  "/api/v1/teacher/teachgrade/exam/statistics")
api.add_resource(GetStuCourseInfo,  "/api/v1/student/course/simpleinfo")
api.add_resource(GetTeaCourseInfo,  "/api/v1/teacher/course/simpleinfo")


#ILAB相关
api.add_resource(ILABLogin, "/api/v1/user/ilab/login")
api.add_resource(ExprStepCorrect,"/api/v1/user/ilab/correct")