#!/usr/bin/env python
# -*- coding: utf-8 -*- 
# @Time   : 5/18/21 5:43 AM 
# <AUTHOR> jackey 
# @File   : user_api.py
# @desc   : ""

from flask import request
from flask_restful import Resource
from common.utils.logger import logger
from core.databases.orm.auth.user_orm import DBCoursePlatformAdmin
from core.data.response import Response, StatusCode


class WhoAreYouV1(Resource):
    @staticmethod
    def get():
        """
        GET /api/v1/who
        :return: 返回当前访问用户 username nick 等信息
        """
        data = {"username": "guest", "nick": "Guest", "email": "<EMAIL>"}
        try:
            token = request.headers.get('token')
            if not token:
                token = request.args.get('token')
            if not token:
                token = request.form.get('token')
            if not token:
                return Response.failed(StatusCode.AUTH_FAILED, data=data)
            _item = DBCoursePlatformAdmin.get_user_info_by_token(token)
            data['username'] = _item['username']
            data['nick'] = _item['nick']
            data['email'] = _item['email']
            return Response.success(data=data)
        except Exception as e:
            if str(e) == "the access token is invalid":
                return Response.failed(StatusCode.AUTH_FAILED, data=data)
            logger.warning("get user info failed: {}".format(e))
            return Response.failed(message=e, data=data)