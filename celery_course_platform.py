from datetime import timedelta
from web.flask_app import flask_app, course_platform_celery

flask_app.app_context().push()
beat_schedule = {
    'system_info_loop_1': {
        'task': 'course_platform.core.tasks.tools.system.t_schedule_update_system_info',
        'schedule': timedelta(seconds=10 * 1),
    },
}

course_platform_celery.conf.beat_schedule.update(beat_schedule)
course_platform_celery.conf.timezone = 'UTC'
celery = course_platform_celery
