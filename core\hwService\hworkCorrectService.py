'''
-*- coding:utf-8 -*-
@Project ：jxsjpt 
@File    ：hworkCorrectService.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2023/3/19 20:21 
@desc    ：教师批改作业模块，查询班级提交详情，按学生批改，按题批改，批改，打回
'''
import os
from datetime import datetime

from bson import ObjectId

from common.utils.ilab_util import ilab_util
from common.utils.logger import logger
from config import Config
from core.databases.orm.homework.classHIHw_orm import ClassHIHwOrm
from core.databases.orm.homework.handInDetail_orm import HandInDetailOrm
from core.databases.orm.homework.handInHwork_orm import HandInHworkOrm
from core.databases.orm.homework.homeworkDetail_orm import HomeworkDetailOrm
from core.databases.orm.homework.question_orm import QuestionOrm


def _match(flag, param):
    logger.info("学生提交的答案：{},参考答案字符串：{}".format(flag, param))
    if not param or flag == param: # param 为空视为任意答案
        return 12, "优"
    else:
        return 0, "及格"
    score = 12
    items = param.split("|")
    for item in items:
        if item in flag:
            score = score + 2
    logger.info("学生得分：{}".format(score))

    if score >= 18:
        evaluation = "优"
    elif score <18 and score >=14:
        evaluation = "好"
    else:
        evaluation = "及格"
    return score,evaluation


def _uploadToIlab(file_path, access_token, title, originId, file_name):
    ilab_util.send_experimental_report(file_path, access_token, title, originId, file_name)
    #此处不再处理返回结果，因为不重要
class HworkCorrectService:
    def __init__(self):
        self.dbhInOrm = HandInHworkOrm()
        self.dbhIDOrm = HandInDetailOrm()
        self.dbQuestionOrm = QuestionOrm()
        self.dbclahiOrm = ClassHIHwOrm()
        self.dbhwdOrm = HomeworkDetailOrm()
    def queryClassHandIn(self,jsonData):
        '''
        查询班级提交详情，返回每次作业整体提交详情，及作业设置信息，包括截止时间等
        '''
        pass
    def toCorrectByStu(self, jsonData):
        '''
        以学生为单位批改作业
        '''
        handInlist =  self.dbhInOrm.find({'$and':[
            {'classId':jsonData['classId']},
            {'courseId': jsonData['courseId']},
            {'hworkNo': jsonData['hworkNo']}
        ]})
        result = []
        for handIn in handInlist:
            handIn['_id'] = str(handIn['_id'])
            result.append(handIn)
        return result
    def toCorrectByQues(self, jsonData):
        '''
        以题目为单位批改作业,查询列表
        '''
        hworkNo = jsonData['hworkNo']
        hwds = self.dbhwdOrm.find({'homeworkNo':hworkNo})
        result = []
        for hwd in hwds:
            hwd['_id'] = str(hwd['_id'])
            question = self.dbQuestionOrm.find_by_id(hwd['questionNo'])
            question.pop('_id')
            hwd['question'] = question
            result.append(hwd)
        return result
    def toCorrectByStuDetail(self,handInId):
        logger.info("按人批改-查询待批改作业详情开始{}".format(handInId))
        handIn = self.dbhInOrm.find_by_id(handInId)
        if handIn['state'] in [1,2,4]:
            logger.info("未提交状态,不能批改")
            return '未提交状态,不能批改'
        handIndetails = self.dbhIDOrm.find({'handInHworkNo':handInId})
        result = []
        logger.info("查询待批改的作业习题详情")
        for handIndetail in handIndetails:
            handIndetail['_id'] = str(handIndetail['_id'])
            question = self.dbQuestionOrm.find_by_id(handIndetail['questionNo'])
            question.pop('_id')
            handIndetail['question'] = question
            result.append(handIndetail)
        # 提交表信息也要返回前台
        handIn['_id'] = handInId
        handIn['handIndetails'] = result
        return handIn
    def toCorrectByQuesDetail(self,hworkDetailId,classId):
        '''
        hworkDetailId:作业明细表id
        classId:班级id
        '''
        logger.info("按题批改-查询待批改页面详情开始,习题{}，班级{}".format(hworkDetailId,classId))
        handIndetails = self.dbhIDOrm.find({'hworkDetailNo': hworkDetailId})
        detailList = []
        for handIndetail in handIndetails:
            handInID = handIndetail['handInHworkNo']
            handInHw = self.dbhInOrm.find_by_id(handInID)
            if classId != handInHw['classId']:
                logger.info("不是本班作业")
                continue
            if handInHw['state'] != 3: #已提交
                logger.info("作业不是已提交状态，不可批改")
                continue
            handIndetail['_id'] = str(handIndetail['_id'])
            handIndetail['studentId'] = handInHw['studentId']
            handIndetail['studentName'] = handInHw['studentName']
            handIndetail['classId'] = handInHw['classId']
            detailList.append(handIndetail)
        question = self.dbQuestionOrm.find_by_id(handIndetail['questionNo'])
        question.pop('_id')
        result = {"question":question,"handIndetails":detailList}
        logger.info("按题批改-查询待批改页面详情结束")
        return result
    def correctHworkByStu(self, jsonData):
        '''
        按学生提交批改，
        '''
        logger.info("按人批改-保存批改信息开始：[{}]".format(str(jsonData)))
        teacherId = jsonData['teacherId']
        note = jsonData['note']
        obtainedScore = jsonData['obtainedScore']
        correctDetails = jsonData['correctDetails']
        handInid = jsonData['_id']
        # 更新提交明细表
        for correctDetail in correctDetails:
            hidId = correctDetail.pop('_id')
            self.dbhIDOrm.update_by_id(hidId,correctDetail)
        #更新提交表
        #先查，因为前端老师可以修改批阅结果
        handInCurr = self.dbhInOrm.find_by_id(handInid)
        if 5 == handInCurr['state']:
            logger.info("作业{}已批改，不再修改班级提交表统计数据".format(handInid))
            self.dbhInOrm.update_by_id(handInid, {
                'note': note,
                'obtainedScore': obtainedScore,
                'correctTeaId': teacherId,  # 批改人
                'correctTime': datetime.utcnow().strftime('%Y-%m-%dT%H:%M:%SZ') # 批改时间
            })
            return True
        logger.info("作业{}刚批改，需要修改班级提交表统计数据".format(handInid))
        self.dbhInOrm.update_by_id(handInid,{
            'note':note,
            'obtainedScore':obtainedScore,
            'correctTeaId':teacherId,#批改人
            'correctTime':datetime.utcnow().strftime('%Y-%m-%dT%H:%M:%SZ'),#批改时间
            'state':5 #已批改
        })
        #更新班级提交表
        handInHwork = self.dbhInOrm.find_by_id(handInid)
        condition = {'$and': [{'homeworkNo': handInHwork['hworkNo']},
                              {'classId': handInHwork['classId']}]}

        result = self.dbclahiOrm.find(condition)
        for classHandIn in result:
            correctCount = classHandIn['correctCount']
            logger.info("   班级{}修改已提交数据，修改后为{}".format(classHandIn['className'],str(correctCount + 1)))
            self.dbclahiOrm.update_by_condition(condition, {'correctCount': correctCount + 1})
        #判断是否需要上传ilab，条件为，报告作业，且作业提交表有特殊标记
        user_from = handInCurr.get('user_from')
        if user_from=="ilab" and handInHwork['type']==2:
            #开始上传实验报告
            #1,获取实验报告地址
            handInDetail = self.dbhIDOrm.find_by_id(hidId)
            file_name = handInDetail['aContent']
            strs = file_name.split(sep='/')
            # file_path = os.path.join(Config.Resource_root_path, 'upload/'+strs[len(strs)-1])
            file_path = 'upload/'+strs[len(strs)-1]
            title = "水处理工控安全仿真实验报告"
            originId = handInHwork['studentId']
            #上传报告
            _uploadToIlab(file_path,handInHwork['access_token'],  title, originId, strs[len(strs)-1])

        return True
    def correctHworkByQues(self, jsonData):
        '''
        按题批改，
        '''
        logger.info("按题批改-保存批改信息开始：[{}]".format(str(jsonData)))
        teacherId = jsonData['teacherId']
        correctDetails = jsonData['correctDetails']
        for correctDetail in correctDetails:
            handInId = correctDetail.pop('handInHworkNo')
            handInHwork = self.dbhInOrm.find_by_id(handInId)
            _id = correctDetail.pop('_id')
            self.dbhIDOrm.update_by_id(_id, correctDetail)
            #更新批改数
            handInCount = handInHwork['handInCount']+1
            obtainedScore = handInHwork['obtainedScore'] + correctDetail['obtainedScore']

            if handInCount == handInHwork['questionCount']:
                #所有作业题批改完毕
                self.dbhInOrm.update_by_id(handInId, {
                    'correctTeaId':teacherId,#批改人
                    'correctTime':datetime.utcnow().strftime('%Y-%m-%dT%H:%M:%SZ'),#批改时间
                    'state':5, #已批改
                    'handInCount': handInCount,
                    'obtainedScore': obtainedScore
                })
                # 更新班级提交表
                condition = {'$and': [{'homeworkNo': handInHwork['hworkNo']},
                                      {'classId': handInHwork['classId']}]}
                self.dbclahiOrm.increment_by_condition(condition,{'correctCount':1})
                # result = self.dbclahiOrm.find(condition)
                # for classHandIn in result:
                #     correctCount = classHandIn['correctCount']
                #     self.dbclahiOrm.update_by_condition(condition, {'correctCount': correctCount + 1})
            else:
                self.dbhInOrm.update_by_id(handInId, {
                    'handInCount': handInCount,
                    'obtainedScore': obtainedScore
                })
        return True
    def callBackHwork(self,jsonData):
        '''
        打回作业，
        '''
        teacherId = jsonData['teacherId']
        note = jsonData['note']
        _id = jsonData['_id']
        logger.info("老师{}开始打回学生{}作业".format(teacherId,_id))
        #修改作业提交表
        oldhandInHwork = self.dbhInOrm.find_by_id(_id)
        if oldhandInHwork['state'] in [1,2]:
            logger.info("未提交状态无需打回")
            return '未提交状态无需打回'
        if oldhandInHwork['state'] in [4]:
            logger.info("已经打回，无需再次打回")
            return '已经打回，无需再次打回'
        self.dbhInOrm.update_by_id(_id, {
            'correctTeaId': teacherId,  # 批改人
            'correctTime': datetime.now().utcnow().strftime('%Y-%m-%dT%H:%M:%SZ'),  # 批改时间
            'handInCount':0,
            'state': 4,  # 打回
            'note':note
        })
        # 更新班级提交表
        condition = {'$and': [{'homeworkNo': oldhandInHwork['hworkNo']},
                              {'classId': oldhandInHwork['classId']}]}
        logger.info("已经打回作业")
        logger.info("班级提交汇总表已提交数-1")
        self.dbclahiOrm.increment_by_condition(condition, {'handedCount': -1})
        if oldhandInHwork['state'] == 5:#原本是已批改状态,已批改数也要减1
            logger.info("班级提交汇总表已批改数-1")
            self.dbclahiOrm.increment_by_condition(condition, {'correctCount': -1})
        return True
    def autoCorrect(self,course_id, title, flag, user_id):
        '''
        title:章节名
        flag:提交的实验结果，可用于自动批改
        自动批改的逻辑，就是答案字符串再结果中是否出现，如果答案字符串有两个，在flag中只有一个，的一半分，分数保底60分
        '''
        #查询学生提交表明细
        logger.error([
                                 {'courseId': course_id},
                                 {'hwName': title},
                                 {'studentId': user_id}])
        handIn = self.dbhInOrm.find_one({'$and': [
                                 {'courseId': course_id},
                                 {'hwName': title},
                                 {'studentId': user_id}]})
        #查询作业明细获取习题答案，进行自动批改
        hwDetail  = self.dbhIDOrm.find_one({'handInHworkNo': str(handIn['_id'])})
        question = self.dbQuestionOrm.find_by_id(hwDetail['questionNo'])
        logger.info("开始自动批改{}提交的【{}】章节实验结果".format(user_id,title))
        score,evaluation = _match(flag,question['ref_answer'])
        logger.info("批改结果为：{},{}".format(score,evaluation))
        #批改完成后修改作业状态为已批改
        #如果是重复提交，则指修改得分和提交的答案，不再修改各种状态
        self.dbhIDOrm.update_by_id(hwDetail['_id'], {"obtainedScore": score, "aContent": flag})
        if handIn['state'] != 5:
            self.dbhInOrm.update_by_id(handIn['_id'],{
                'note':evaluation,
                'obtainedScore':score,
                'correctTeaId':"auto",#批改人
                'correctTime':datetime.utcnow().strftime('%Y-%m-%dT%H:%M:%SZ'),#批改时间
                'state':5 #已批改
            })
            self.dbclahiOrm.increment_by_condition({"$and":[{'homeworkNo': handIn['hworkNo']},
                                                         {'classId': handIn['classId']}]}, {'correctCount': 1,'handedCount': 1})
        else:
            self.dbhInOrm.update_by_id(handIn['_id'], {
                'note': evaluation,
                'obtainedScore': score,
                # 'correctTeaId': "auto",  # 批改人
                'correctTime': datetime.utcnow().strftime('%Y-%m-%dT%H:%M:%SZ')  # 批改时间
            })
        return {"score":score, "evaluation":evaluation, "maxScore":handIn['score']}