from flask import request
from flask_restful import Resource,reqparse
from common.utils.logger import logger
from common.utils.dict_parse import DictParseType
from core.data.response import Response
from core.databases.orm.exam.exam_manage_orm import DBExamPlatformAdmin
from datetime import datetime, timedelta
from bson import ObjectId
from flask_restful.reqparse import Argument
from pymongo import MongoClient
from config import Config
from pymongo.errors import OperationFailure
from core.databases.orm.user.organzation_orm import DBCoursePlatformStuOrg
from core.databases.orm.course.course_orm import CourseInfo
from core.auth.auth import *
from web.api.course.innerapi import _get_semester_courses_form_sid
from core.databases.orm.course.course_orm import _Course_Teacher
import json

'''
@description: 考试信息控制器
@author: xiong
@date: 2023/5/20
'''

# 日期格式
date_format = '%Y-%m-%dT%H:%M:%SZ'

# 创建用户
createUser = "createUser"

# 更新用户
updateUser = "updateUser"

class ExamManageV1(Resource):
    '''
    考试管理相关操作
    API: /api/v1/exam/<course_id>?keyword=<keyword>
    '''
    @login_required
    def get(self, course_id):
        '''
        根据course_id获取考试列表信息

        @param  course_id   课程id
        @param  keyword     关键字
        @return 考试列表信息
        '''
        query = {
            'course_id' : ObjectId(course_id)
        }
        items = {
            'exam_name' : 1, 
            'exam_id':1,
            'exam_date' : 1, 
            'begin_time' : 1, 
            'end_time': 1,
            'class_id':1,
            'is_delete':1
        }

        # 判断请求是否存在关键字，多个关键字以空格分隔
        keyword = request.args.get('keyword')
        if(keyword is not None): 
            str_list = keyword.split(' ')
            keyword_list = []
            for s in str_list:
                if len(s) > 0:
                    keyword_list.append(s)
            keyword_query = '.*('
            for i in range(0,len(keyword_list)):
                if i > 0:
                    keyword_query += '|'
                keyword_query += keyword_list[i]
            keyword_query +=  ').*'
            query['exam_name']= {'$regex':keyword_query}
        
        # 权限判断
        access_id = session.get("id")
        access_role = session.get("role")

        if access_role == 'student':
            query_record_student_items = {
                'course_id' : course_id,
                'user_id' : access_id,
            }
            res_record_student_items = {
                'exam_id' : 1,
            }
            try:
                record_student_list = DBExamPlatformAdmin.query_exam_record_student_list(query_record_student_items, res_record_student_items)
                ids = [ObjectId(doc['exam_id']) for doc in record_student_list]

                query['_id'] = {'$in' : ids}
                query_res = DBExamPlatformAdmin.query_exam_list(query, items)
                exam_list = [doc for doc in query_res]

                for record in exam_list:

                    record['exam_id'] = str(record['_id'])
                    student_exam_state={
                        'exam_state':1,
                    }
                    res=DBExamPlatformAdmin.query_exam_record_student({'user_id':access_id,'exam_id':record['exam_id']},student_exam_state)
                    record['exam_state']=res['exam_state']
                    #correct状态
                    query_correct = {
                        'exam_id':record['exam_id']
                    }
                    res_correct = {
                        "correct_state":1,
                        'score':1
                    }
                    query_exam_record_res = DBExamPlatformAdmin.query_exam_record_student(query_correct,res_correct)
                    correct_state=query_exam_record_res['correct_state']
                    record['score']=query_exam_record_res['score']
                    # 加入考试状态，由服务器端提供
                    # 1:未开始 2:进行中 3:已结束
                    if(record['begin_time'] is not None and record['end_time'] is not None):
                        current_time=datetime.now()
                        if(current_time<record['begin_time']):
                            record['state']=0
                        elif((current_time<record['end_time']) and (record['exam_state']==False) and (correct_state==False)):
                            record['state']=1
                        elif((current_time<record['end_time']) and (record['exam_state']==True) and (correct_state==False)):
                            record['state']=2
                        elif((current_time<record['end_time']) and (record['exam_state']==True) and (correct_state==True)):
                            record['state']=3
                        elif((current_time>record['end_time']) and (record['exam_state']==False) and (correct_state==False)):
                            record['state']=4
                        elif((current_time>record['end_time']) and (record['exam_state']==True) and (correct_state==False)):
                            record['state']=5
                        elif((current_time>record['end_time']) and (record['exam_state']==True) and (correct_state==True)):
                            record['state']=6
                    record['begin_time'] = record['begin_time'].strftime(date_format)
                    record['end_time'] = record['end_time'].strftime(date_format)
                    record['correct_state']=correct_state
                    record.pop('_id')
                return Response.success(data=exam_list, message='query exam list successful')
            except Exception as e:
                logger.warning('test failed: {}'.format(e))
                logger.exception(e)
                return Response.failed(data='', message=e)
        else:

            try:
                # 执行查询操作，转换数据输出格式execute query, transform
                query_teacher={
                    'course_id' : ObjectId(course_id),
                    'is_delete' : False
                }
                query_res = DBExamPlatformAdmin.query_exam_list(query_teacher, items)
                exam_list = [doc for doc in query_res]
                for record in exam_list:
                    record['exam_id'] = str(record['_id'])
                    record['class_name'] = []
                    for id in record['class_id']:
                        classinfo=DBCoursePlatformStuOrg.find_by_id(id)
                        if classinfo is not None:
                            record['class_name'].append(classinfo['name'])
                    # 加入考试状态，由服务器端提供
                    # 1:未开始 2:进行中 3:已结束
                    if(record['begin_time'] is not None and record['end_time'] is not None):
                        current_time=datetime.now()
                        if(current_time<record['begin_time']):
                            record['state']=1
                        elif(current_time<record['end_time']):
                            record['state']=2
                        else:
                            record['state']=3
                    record['begin_time'] = record['begin_time'].strftime(date_format)
                    record['end_time'] = record['end_time'].strftime(date_format)
                    record.pop('_id')
                return Response.success(data=exam_list, message='query exam list successful')
            except Exception as e:
                logger.warning('test failed: {}'.format(e))
                logger.exception(e)
                return Response.failed(data='', message=e)
        
    @login_required
    def post(self, course_id):
        '''
        创建考试信息
        
        @param  course_id   课程id
        @param  exam_name   考试任务名称
        @param  class_id    考试任务对应班级
        @param  paper_id    考试所采用的试卷
        @param  begin_time  开始考试时间
        @param  end_time    截止考试时间
        @return 创建执行结果
        '''
        parser_exam_create = reqparse.RequestParser()
        parser_exam_create.add_argument('exam_name', type=str, required = True)
        parser_exam_create.add_argument('class_id', type=str, required = True, action='append')
        parser_exam_create.add_argument('paper_id', type=str, required = True)
        parser_exam_create.add_argument('begin_time', type=str, required = True)
        parser_exam_create.add_argument('end_time', type=str, required = True)
        args = parser_exam_create.parse_args()


        access_id = session.get("id")
        access_role = session.get("role")
        if access_role == 'student':
            return Response.failed(data='get exam list fail', message='student is not allowed')

        try:

            # 从试卷库获取题目信息
            exam_paper = DBExamPlatformAdmin.query_paper({'_id' : ObjectId(args['paper_id'])}, {'paper_content' : 1, 'paper_name' : 1})
            if exam_paper is None:
                return Response.failed(data='', message='paper id is invalid.')

             # 创建考试信息
            insert_exam_items = {
                'course_id' : ObjectId(course_id),
                'exam_name' : args['exam_name'],
                'class_id' : args['class_id'],
                'begin_time' : datetime.strptime(args['begin_time'], date_format),
                'end_time' : datetime.strptime(args['end_time'], date_format),
                'create_user' : ObjectId(access_id),
                'create_time' : datetime.utcnow(),
                'update_user' : ObjectId(access_id),
                'update_time' : datetime.utcnow(),
                'is_delete' : False
            }
            res_insert_exam = DBExamPlatformAdmin.insert_exam(insert_exam_items)


            # 备份考试题目信息
            insert_paper_items = {
                'course_id' : ObjectId(course_id),
                'create_user' : ObjectId(access_id),
                'create_time' : datetime.utcnow(),
                'update_user' : ObjectId(access_id),
                'update_time' : datetime.utcnow(),
                'paper_content' : exam_paper['paper_content'],
                'exam_id' : ObjectId(str(res_insert_exam.inserted_id)),
            }
            DBExamPlatformAdmin.insert_exam_record_paper(insert_paper_items)
            print("paper_content:")
            # print(type(exam_paper['paper_content'][0])

            
            # 为所有学生创建空答案
            studentList=[]
            for classes in args['class_id']:
                studentDict=DBCoursePlatformStuOrg.get_one({'_id': ObjectId(classes)})
                studentDict['students']['class_id']=str(classes)
                studentList.extend(studentDict['students'])
            emptyUserAnswer = []
            for question in exam_paper['paper_content']:
                dict_question = question
                question_answer = {
                    'seqNo' : dict_question['seqNo'],
                    'user_score' : 0,
                    'user_answer' : '',
                }
                emptyUserAnswer.append(question_answer)

            for i in range(0, len(studentList)):
                insert_student_items = {
                    'user_id' : studentList[i]['id'],
                    'class_id':str(studentList[i]['class_id']),
                    'course_id' : course_id,
                    'exam_id' : str(res_insert_exam.inserted_id),
                    'answer_content' : emptyUserAnswer,
                    'exam_state' : False,
                    'correct_state' : False,
                    'score' : 0,
                }
                DBExamPlatformAdmin.insert_exam_record_student(insert_student_items)

            return Response.success(data=str(res_insert_exam.inserted_id), message='成功创建考试')
        except Exception as e:
            logger.warning('create exam faild: {}'.format(e))
            logger.exception(e)
            return Response.failed(data='', message=e)
        

    @login_required
    def patch(self, course_id):
        '''
        更新指定课程下的考试任务
        
        @param  course_id   课程id
        @param  exam_id     待更新的考试id
        @param  exam_name   新的考试名称
        @param  begin_time  起始时间
        @param  end_time    截止时间
        @return 更新执行结果
        '''
        parser_exam_update_list = reqparse.RequestParser()
        parser_exam_update_list.add_argument('exam_id', type=str, required = True)
        parser_exam_update_list.add_argument('exam_name', type=str, required = True)
        parser_exam_update_list.add_argument('begin_time', type=str, required = True)
        parser_exam_update_list.add_argument('end_time', type=str, required = True)
        args = parser_exam_update_list.parse_args()

        # 权限判断
        access_id = session.get("id")
        access_role = session.get("role")
        if access_role == 'student':
            return Response.failed(data='get exam list fail', message='student is not allowed')
        exam_id = ObjectId(args['exam_id'])
        query_items = {
            '_id' : exam_id,
            'course_id': ObjectId(course_id)
        }
        # 
        update_items = {
            'exam_name' : args['exam_name'],
            'begin_time' : datetime.strptime(args['begin_time'], date_format),
            'end_time' : datetime.strptime(args['end_time'], date_format),
            'update_user' : access_id,
            'update_time' : datetime.utcnow()
        }

        try:
            res = DBExamPlatformAdmin.update_exam(query_items, update_items)
            return Response.success(data=res.modified_count, message='成功更新考试')

        except Exception as e:
            logger.warning('update exam failed: {}'.format(e))
            logger.exception(e)
            return Response.failed(data='fail', message=e)

    
    @login_required
    def delete(self, course_id):
        '''
        批量删除指定课程下的考试任务
        
        @param  course_id   课程id
        @param  exam_list   待删除的考试id列表
        @return 执行结果
        '''
        parser_exam_delete_list = reqparse.RequestParser()
        parser_exam_delete_list.add_argument('exam_list', type=str, required = True, action = 'append')
        args = parser_exam_delete_list.parse_args()
        exam_list = args['exam_list']
        access_id = session.get("id")
        access_role = session.get("role")
        if access_role == 'student':
            return Response.failed(data='get exam list fail', message='student is not allowed')
        ids = []
        for exam_id in exam_list:
            ids.append(ObjectId(exam_id))
        
        query_items = {
            '_id': {'$in' : ids}, 
            'course_id' : ObjectId(course_id),
            'begin_time' : {"$gte":datetime.utcnow()}
        }
        # TODO get update user
        update_items = {
            'update_user' : ObjectId(access_id),
            'update_time' : datetime.utcnow(),
            'is_delete' : True
        }
        exmessage='成功删除考试'
        try:
            res =  DBExamPlatformAdmin.update_exam(query_items, update_items)
            if len(ids)>res.modified_count:
                exmessage+="开考后无法删除考试！"
            return Response.success(data=res.modified_count, message=exmessage)
        except Exception as e:
            logger.warning('test failed: {}'.format(e))
            logger.exception(e)
            return Response.failed(data='fail', message=e)
    

    

class ExamDetailInfoManageV1(Resource):
    '''
    考试详情管理
    API: /api/v1/exam/detail/info/<exam_id>
    '''

    @login_required
    def get(self, exam_id):
        '''
        考试详情列表展示
        
        @param  exam_id 考试id
        @return 列表信息(学生id, 分数, 考试状态, 批改状态)
        '''
        access_id = session.get("id")
        access_role = session.get("role")
        if access_role == 'student':
            return Response.failed(data='get exam list fail', message='student is not allowed')
        
        query_exam_items = {
            '_id' : ObjectId(exam_id),
        }
        res_exam_items = {
            'class_id' : 1,
        }

        # 获取参加考试用户名称
        query_res_exam = DBExamPlatformAdmin.query_exam_list(query_exam_items, res_exam_items)
        exam =  [doc for doc in query_res_exam]
        if not exam:
            return Response.failed(data='', message="exam not found")
        class_id = exam[0]['class_id']
        studentList = DBCoursePlatformStuOrg.get_one({'_id': ObjectId(class_id[0])})
        studentList = studentList['students']

        query_record_student_items = {'exam_id' : str(exam_id)}
        res_record_student_items = {'user_id':1, 'score': 1, 'exam_state':1, 'correct_state' : 1}

        # 在exam_record_student表查考生考试列表
        try:
            res_exam_record = DBExamPlatformAdmin.query_exam_record_student_list(query_record_student_items, res_record_student_items)
            res = [doc for doc in res_exam_record]
            for record in res:
                record['record_id'] = str(record['_id'])
                for student in studentList:
                    if record['user_id'] ==  student['id'] :
                        record['user_name'] = student['user_name']
                        break
                record.pop('_id')
            return Response.success(data=res, message='query exam record successful')
        
        except Exception as e:
            logger.warning('query failed: {}'.format(e))
            logger.exception(e)
            return Response.failed(data='', message=e)



class ExamDetailRecordManageV1(Resource):
    '''
    考试记录管理
    API: /api/v1/exam/detail/record/<exam_id>/<user_id>
    '''
    @login_required
    def get(self, exam_id, user_id):
        '''
        获取指定考试下, 某考生的答题情况
        
        @param  exam_id 考试id
        @param  user_id 学生id

        @return 答题情况
        '''
        access_id = session.get("id")
        access_role = session.get("role")
        if access_role == 'student':
            return Response.failed(data='get exam list fail', message='student is not allowed')
        res = {}
        query_record_student = {
            'exam_id': exam_id, 
            'user_id' : user_id,
        }
        items_record_student = {'answer_content' : 1}
        query_record_paper = {'exam_id': ObjectId(exam_id)}
        items_record_paper = {'paper_content' : 1}
        # 查询答题情况
        try:
            res_exam_student = DBExamPlatformAdmin.query_exam_record_student(query_record_student, items_record_student)
            res_exam_paper = DBExamPlatformAdmin.query_exam_record_paper(query_record_paper, items_record_paper)
            
            if(res_exam_student is not None) and (res_exam_paper is not None) :

                list_answer = res_exam_student['answer_content']
                list_question = res_exam_paper['paper_content']
                
                if len(list_answer) != len(list_question):
                    return Response.success(data='fail', message='the number of question and answer is not equal.')
                res['content'] = []
                for i in range(0, len(list_question)):
                    question = list_question[i]
                    answer = list_answer[i]
                    question['user_answer'] = answer['user_answer']
                    question['user_score'] = answer['user_score']
                    res['content'].append(question)

                return Response.success(data=res, message='query exam detail successful')
            else :
                return Response.success(data='fail', message='exam_id or user_id is invalid')
        except Exception as e:
            logger.warning('query failed: {}'.format(e))
            logger.exception(e)
            return Response.failed(data='', message=e)

    @login_required
    def patch(self, exam_id, user_id):
        '''
        更新某考生试卷的批改情况
        
        @param  exam_id 考试id
        @param  user_id 学生id
        @param  content 答题情况
        @return 答题情况
        '''
        access_id = session.get("id")
        access_role = session.get("role")
        if access_role == 'student':
            return Response.failed(data='get exam list fail', message='student is not allowed')
        parser_exam_detail_update = reqparse.RequestParser()
        parser_exam_detail_update.add_argument('content', type=DictParseType([
            Argument('seqNo', type=int),
            Argument('user_score', type = int),
        ]), action = 'append')

        args = parser_exam_detail_update.parse_args()
        new_content = args['content']

        query_record_student = {'exam_id': exam_id, 'user_id' : user_id}
        items_record_student = {'answer_content' : 1}
        query_record_paper = {'exam_id': ObjectId(exam_id)}
        items_record_paper = {'paper_content' : 1}


        try:
            res_exam_student = DBExamPlatformAdmin.query_exam_record_student(query_record_student, items_record_student)
            res_exam_paper = DBExamPlatformAdmin.query_exam_record_paper(query_record_paper, items_record_paper)
            student_answer_content = res_exam_student['answer_content']
            paper_content = res_exam_paper['paper_content']

            # 更新批改分数
            for content in new_content:
                for answer in student_answer_content:
                    if(content['seqNo'] == answer['seqNo']):
                        answer['user_score'] = content['user_score']
            
            # 计算总分，过滤批改分数大于题目总分
            sumScore = 0
            for answer in student_answer_content:
                for content in paper_content:
                    if(content['seqNo'] == answer['seqNo']):
                        answer['user_score'] = min(answer['user_score'], int(content['question_score']))
                        sumScore += answer['user_score']

            update_record_student_items = {
                'score' : sumScore,
                'answer_content' : student_answer_content,
                'correct_state' : True
            }

            update_res = DBExamPlatformAdmin.update_exam_record_student(query_record_student, update_record_student_items)

            return Response.success(data=update_res.modified_count, message='update exam detail successful')
        except Exception as e:
            logger.warning('query failed: {}'.format(e))
            logger.exception(e)
            return Response.failed(data='', message=e)

        

class ExamCenterManageV1(Resource):
    '''
    考试中心
    API: /api/v1/exam/content/<exam_id>
    '''
    @login_required
    def get(self, exam_id):
        '''
        学生获取考试题目
        
        @param  exam_id 考试id
        @return 试卷题目信息
        '''
        # 权限判断
        access_id = session.get("id")
        access_role = session.get("role")

        try:

            # 获取考试记录，未存在则表示该用户未被选入参加该次考试，存在则取出之前答题情况
            query_record_student = {
                'exam_id' : exam_id,
                'user_id' : access_id,
            }
            res_record_student = {
                'course_id' : 1,
                'answer_content' : 1,
            }
            query_student_res = DBExamPlatformAdmin.query_exam_record_student(query_record_student, res_record_student)
            if query_student_res == None:
                return Response.failed(data='user is not allow to access exam', message="get exam content failed")
            

            # 获取考试信息，判断是否开考
            query_exam = {
                '_id' : ObjectId(exam_id),
            }
            res_exam = {
                'exam_name' : 1, 
                'begin_time' : 1, 
                'end_time': 1,
                'is_delete':1
            }
            query_correct = {
                'exam_id':exam_id
            }
            res_correct = {
                "correct_state":1
            }
            query_exam_res = DBExamPlatformAdmin.query_exam(query_exam, res_exam)
            correct_state = DBExamPlatformAdmin.query_exam_record_student(query_correct,res_correct)['correct_state']
            if query_exam_res is None :
                return Response.failed(data='', message="get exam content failed:考试数据异常.")
            if datetime.now() < query_exam_res['begin_time']:
                return Response.failed(data='', message="get exam content failed:考试未开始.")
            if (datetime.now() > query_exam_res['end_time']) & (not correct_state):
                return Response.failed(data='', message="get exam content failed:考试已结束.")
            
            # 获取考试试卷内容
            query_record_paper = {'exam_id': ObjectId(exam_id)}
            res_record_paper = {'paper_content' : 1}
            query_paper_res = DBExamPlatformAdmin.query_exam_record_paper(query_record_paper, res_record_paper)
            if query_paper_res is None: 
                return Response.failed(data='试卷数据异常.', message="get exam content failed")

            list_answer = query_student_res['answer_content']
            list_question = query_paper_res['paper_content']
                
            if len(list_answer) != len(list_question):
                return Response.success(data='the number of question and answer is not equal.', message='get exam content failed.')
            
            # 试卷内容和答题情况整合
            res = {}
            res['content'] = []
            res['is_delete']=query_exam_res['is_delete']
            for i in range(0, len(list_question)):
                question = list_question[i]
                answer = list_answer[i]
                if question['type']==2:
                    question['user_answer'] = json.dumps(answer['user_answer'])
                question['user_score'] = answer['user_score']
                res['content'].append(question)
            #获取
            return Response.success(data=res, message='query exam detail successful')

            
        except Exception as e:
            logger.warning('query failed: {}'.format(e))
            logger.exception(e)
            return Response.failed(data='', message=e)
    
    # @login_required   
    def post(self, exam_id):
        '''
        考生提交试卷
        
        @param  exam_id 考试id
        @param  user_id 学生id
        @param  content 答题情况
        @return  答题情况
        '''
        parser_exam_center_commit = reqparse.RequestParser()
        parser_exam_center_commit.add_argument('content', type=dict,action="append")
        args = parser_exam_center_commit.parse_args()
        content = args['content']

        access_id = session.get("id")
        access_role = session.get("role")
        
        try:
            # 获取考试记录
            query_record_student = {
                'exam_id' : exam_id,
                'user_id' : access_id,
            }
            res_record_student = {
                'course_id' : 1,
                'answer_content' : 1,
            }
            query_student_res = DBExamPlatformAdmin.query_exam_record_student(query_record_student, res_record_student)
            if query_student_res == None:
                return Response.failed(data='user is not allow to access exam', message="get exam content failed")
            
            # 获取考试信息，判断是否开考
            query_exam = {
                'is_delete' : False,
                '_id' : ObjectId(exam_id),
            }
            res_exam = {
                'exam_name' : 1, 
                'exam_date' : 1, 
                'begin_time' : 1, 
                'end_time': 1
            }
            query_exam_res = DBExamPlatformAdmin.query_exam(query_exam, res_exam)
            if query_exam_res is None :
                return Response.failed(data='考试数据异常.', message="get exam content failed")
            if datetime.now() < query_exam_res['begin_time']:
                return Response.failed(data='考试未开始.', message="get exam content failed")

            if (datetime.now()- timedelta(minutes=1)) >= query_exam_res['end_time']:
                return Response.failed(data='考试已结束.', message="get exam content failed")
            

            # 从content中提取用户答案，存入考试记录
            answer_content = query_student_res['answer_content']

            # print(answer_content)
            if len(content) != len(answer_content):
                return Response.success(data='the number of question and answer is not equal.', message='get exam content failed.')
            for question in content:
                for answer in answer_content:
                    if answer['seqNo'] == question['seqNo']:
                        answer['user_answer'] = question['user_answer']
                        break
                

            update_record_student = {
                'answer_content' : answer_content,
                'exam_state':True
            }
            res_update = DBExamPlatformAdmin.update_exam_record_student(query_record_student, update_record_student)

            return Response.success(data=res_update.modified_count, message='成功提交考试考试')
            
        except Exception as e:
            logger.warning('query failed: {}'.format(e))
            logger.exception(e)
            return Response.failed(data='', message=e)

class ExamStatV1(Resource):
    '''
    考试统计：
    API:API: /api/v1/exam/stat/<student_id>
    '''
    def get(self):
        try:
            sid=session.get("id")
            course_id=request.args.get('courseId',type=str)
            course_info=_get_semester_courses_form_sid(sid)
            course_list=[]
            if course_id is not None:
                course_list.append(ObjectId(course_id))
            else:
                for i in range(len(course_info)):
                    course_list.append(ObjectId(course_info[i]['course_id']))
            result={
                "total":0,
                "done":0,
                "todo":0,
                "todoList":[]
            }
            res=DBExamPlatformAdmin.query_exam_list({'course_id':{"$in":course_list}},{})
            current_time=datetime.now()
            for item in res:
                result['total']+=1
                if item["end_time"]>current_time and item['is_delete']==False:
                    result['todo']+=1
                    item['course_id']=str(item['course_id'])
                    for i in range(len(course_info)):
                        if item['course_id']==str(course_info[i]['course_id']):
                            item['course_name']=course_info[i]['course_name']
                            item['course_type']=course_info[i]['course_type']
                    item['exam_id']=str(item['_id'])
                    item.pop('create_user')
                    item.pop('create_time')
                    item.pop('update_user')
                    item.pop('update_time')
                    item['begin_time']=item['begin_time'].strftime(date_format)
                    item['end_time']=item['end_time'].strftime(date_format)
                    item.pop('_id')
                    result['todoList'].append(item)
                else:
                    result['done']+=1

            return Response.success(data=result, message='')
        except Exception as e:
            logger.warning('query failed: {}'.format(e))
            logger.exception(e)
            return Response.failed(data='', message=e)

        