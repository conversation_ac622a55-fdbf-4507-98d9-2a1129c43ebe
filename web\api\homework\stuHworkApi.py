'''
-*- coding:utf-8 -*-
@Project ：jxsjpt 
@File    ：homeworkApi.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2023/3/18 18:33 
@desc    ：学生查看已布置的作业
'''
from flask import request
from flask_restful import Resource, reqparse

from common.utils.logger import logger
from core.data.response import Response
from core.hwService.handInService import HandInService




class StudentHworkApi(Resource):
    def __init__(self):
        self.businessService = HandInService()

    def get(self):
        parser = reqparse.RequestParser()

        # parser.add_argument('birthday', type=inputs.date, help='生日字段验证错误！')
        # parser.add_argument('telphone', type=inputs.regex(r'1[3578]\d{9}'))
        # parser.add_argument('home_page', type=inputs.url, help='个人中心链接验证错误！')
        # parser.add_argument('username', type=str, help='用户名验证错误！', required=True)
        # parser.add_argument('password', type=str, help='密码验证错误！')
        # parser.ar('studentId', type=str, help='学生id没有上传！', required = True)
        # parser.add_argument('courseId', type=int,  help='课程id！', required = True)
        # parser.add_argument('gender', type=str, choices=['male', 'female', 'secret'])
        args = request.args
        try:
            studentId = args.get('studentId', type=str)
            couserId = args.get('courseId', type=str)
            jsonData = {
                'studentId':studentId,
                'courseId':couserId
            }
            result = self.businessService.queryHandIn(jsonData)
            result = sorted(result,key=lambda x: x.get("type"))
            return Response.success(data=result, message="")
        except Exception as e:
            logger.warning(" failed: {}".format(e))
            logger.exception(e)
            return Response.failed(data="", message=e)