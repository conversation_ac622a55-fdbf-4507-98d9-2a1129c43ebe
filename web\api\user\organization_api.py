from bson import ObjectId
from flask import request,jsonify
from flask_restful import Resource,reqparse
from common.utils.logger import logger
# from core.databases.orm.auth.user_orm import DBCoursePlatformAdmin
from core.data.response import Response, StatusCode
from core.databases.orm.user.organzation_orm import DBCoursePlatformStuOrg as stu_org
# from core.databases.orm.user.organzation_orm import DBCoursePlatformTeaOrg as tea_org
from core.auth.auth import *
from core.databases.orm.user.users_orm import DBCoursePlatformStudents as stu
from web.api.course.course import deleteStudentCourse, updateNewStudentCourse



class StuOrganizations(Resource):
    # @login_required # 登录验证
    @api_role_allowed("teacher", "admin")
    def get(self):
        """
        获取班级列表
        过滤需求：所有班级，课程对应的班级 ......
        GET /teacher/organizations
        :return: 返回班级列表信息
        """
        data = []
        try:
            filter = None
            if session.get("role") == "teacher":
                filter = {"creator": ObjectId(session.get("id"))}
            data = stu_org.get(filter)                
            return Response.success(data=data)
        except Exception as e:
            msg = "get class list failed: {}".format(e)
            logger.warning(msg)
            logger.exception(e)
            return Response.failed(message=msg, data=data)

    # @login_required # 登录验证
    @api_role_allowed("teacher")
    def post(self):
        """
        post /teacher/organizations
        :return: 创建班级
        """
        try:
            parser = reqparse.RequestParser()
            parser.add_argument('name', type=str)
            args = parser.parse_args()

            data = stu_org.create(name=args.get("name"), creator=ObjectId(session.get("id")))

            return Response.success(message="create successful",data=data)
        except Exception as e:
            msg = "create organization failed: {}".format(e)
            logger.warning(msg)
            logger.exception(e)
            return Response.failed(message=msg)


class StuOrganization(Resource):

    def check_permission(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            if session.get("role") == "teacher" :
                id = kwargs.get("id")
                data = stu_org.get_one({'_id': ObjectId(id)}, extended=False)
                if not data:
                    return Response.failed(StatusCode.NOT_FOUND, message="trying to operate an unknown orgnization.")
                            
                if session.get("id") != str(data.get("creator")):
                    return Response.failed(StatusCode.AUTH_FAILED, message="cannot operate different user in same role.")
            return func(*args, **kwargs)
        return wrapper
            
    # @login_required # 登录验证
    @api_role_allowed("teacher", "admin")
    @check_permission
    def get(self, id):
        """
        GET /teacher/organization/<id>
        :return: 单个班级信息
        """
        data = []
        try:
            # 从数据库查询单个班级的信息
            data = stu_org.get_one({'_id': ObjectId(id)})   
            return Response.success(data=data)
        except Exception as e:
            msg = "get class {} failed: {}".format(id,e)
            logger.warning(msg)
            logger.exception(e)
            return Response.failed(message=msg, data=data)

    # @login_required # 登录验证
    @api_role_allowed("teacher", "admin")
    @check_permission
    def put(self, id):
        """
        put /teacher/organization/<id>
        :return: 修改班级信息
        
        {
            "name" : **,
            "add_list" : [
                {
                    "profile_id": **,
                    "user_name": **,
                    "password": **,
                    "college": **,
                    "major": **,
                }
            ]
        }
        
        """
        try:            
            parser = reqparse.RequestParser()
            parser.add_argument('name', type=str)
            parser.add_argument('add_list', type=dict, action='append')
            parser.add_argument('del_list', type=str, action='append')
            args = parser.parse_args()
            # 参数校验
            add_list = []
            if args.add_list:
                for user_info in args.add_list:
                    user_id = stu.get_or_create(user_info)
                    add_list.append(user_id)
                    

            # 更新数据库
            stu_org.update(_id=id, name=args.name, add_list= add_list, del_list=args.del_list)
            if args.add_list:
                updateNewStudentCourse([id])
            if args.del_list:
                deleteStudentCourse([id], args.del_list)
            
            return Response.success()
        except Exception as e:
            msg = "edit organization {} failed: {}".format(id,e)
            logger.warning(msg)
            logger.exception(e)
            return Response.failed(message=msg)

    # @login_required # 登录验证
    @api_role_allowed("teacher", "admin")
    @check_permission
    def delete(self, id):
        """
        delete /teacher/organization/<id>
        :return: 删除班级
        """
        try:
            #班级里的学生删除完毕后，再删除班级
            # from xieyunyang
            org = stu_org.get_one({'_id': ObjectId(id)}, extended=False)
            if org and org.get('students'):
                stus_info = []
                for stu in org['students']:
                    stus_info.append(stu['id'])
                deleteStudentCourse([id],stus_info)
            # 更新数据库
            data = stu_org.delete(id)
            return Response.success(data=data)
        except Exception as e:
            msg = "delete organization {} failed: {}".format(id,e)
            logger.warning(msg)
            logger.exception(e)
            return Response.failed(message=msg)
        
    @login_required
    @api_role_allowed("teacher", "admin")
    @check_permission
    def post(self, id):
        """ 
        post /teacher/organization/<id>
        :批量导入
        """
        try:
            file = request.files.get('file')
            if not file:
                raise Exception("please upload file.")
            
            import pandas as pd
            students_info = pd.read_excel(file.read())
            
            students_info = students_info.rename(columns={
                "学号": "profile_id",
                "姓名": "user_name",
                "密码": "password",
                "学院": "college",
                "专业": "major", 
            })
            
            add_list = []
            for _, info in students_info.iterrows():
                student_info = info.to_dict()
                """ 
                {
                    "profile_id": **,
                    "user_name": **,
                    "password": **,
                    "college": **,
                    "major": **,
                }
                """
                
                add_list.append(stu.get_or_create(student_info))
            
            # from xieyunyang
            updateNewStudentCourse([id])
            stu_org.update(id, add_list=add_list)
            return Response.success()
        except Exception as e:
            msg = "import organization {} failed: {}".format(id,e)
            logger.warning(msg)
            logger.exception(e)
            return Response.failed(message=msg)
        
    

# class TeaOrganizations(Resource):
#     # @login_required # 登录验证
#     # @permission_required()    #权限验证
#     def get(self):
#         """
#         获取班级列表
#         过滤需求：所有班级，课程对应的班级 ......
#         GET /organizations
#         :return: 返回班级列表信息
#         """
#         data = []
#         try:
#             data = stu_org.get()       
#             return Response.success(data=data)
#         except Exception as e:
#             msg = "get class list failed: {}".format(e)
#             logger.warning(msg)
#             return Response.failed(message=msg, data=data)

#     # @login_required # 登录验证
#     def post(self):
#         """
#         post /organizations
#         :return: 创建班级
#         """
#         try:
#             parser = reqparse.RequestParser()
#             parser.add_argument('name', type=str)
#             args = parser.parse_args()
#             # 参数校验

#             # 插入数据库
#             # DBbuyuConfiguration.update_by_id(cid, data)
#             data = stu_org.create(**args)

#             return Response.success(message="create successful",data=data)
#         except Exception as e:
#             msg = "create organization failed: {}".format(e)
#             logger.warning(msg)
#             return Response.failed(message=msg)
