from flask import request,session
from flask_restful import Resource, reqparse
from common.utils.logger import logger
from core.data.response import Response, StatusCode
from core.databases.orm.course.course_orm import *
from flask_restful import Resource, reqparse
from bson import ObjectId
from core.auth.auth import *
from web.api.course.course_teacher import getClassName,getTeacherName
from core.databases.orm.user.organzation_orm import *
from core.databases.orm.user.users_orm import *
from .course_tool import compare_time
import re
import time
import datetime
import requests
from datetime import timedelta
from dateutil import rrule
import collections

def cul_total_du(duration):
    total=0
    for d in duration:
        total+=len(d['weeks'])*len(d['day'])*len(d['time'])
    return total

def cul_total_sc(scheme:dict):
    total=0
    total+=cul_total_du(scheme['duration'])
    if  'total' in scheme.keys():
        scheme['total']=total
    else:
        scheme.update({
            "total":total
        })

def update_progress(scheme):
    day_start=datetime.datetime.strptime(scheme['first_week'],"%Y-%m-%d").date()
    week_start=get_start_of_week(day_start)
    today=datetime.datetime.now()
    toweek=get_start_of_week(today)
    past_week=rrule.rrule(rrule.WEEKLY,dtstart=week_start,until=toweek).count()
    count=0
    for d in scheme['duration']:
        ct1=0
        ct2=0
        for w in d['weeks']:
            if w < past_week:ct1+=1
            if w == past_week:
                t=datetime.datetime.now().weekday()+1
                for dd in d['day']:
                    if dd < t : ct2+=1
        count+=ct1*len(d['day'])+ct2
    if not 'total' in scheme.keys():
        cul_total_sc(scheme)
    scheme['progress']=round(count/scheme['total'],2)

#定时更新课程进度
def auto_update_progress():
    logger.info('【AUTO】auto_update_progress:【'+str(datetime.datetime.now())+'】')
    all_scheme,status=CourseTime.find(
        query={},
        need={}
    )
    for scheme in all_scheme:
        id=str(scheme['_id'])
        update_progress(scheme)
        progress=scheme['progress']
        CourseTime.update_one_by_id(_id=id,update={"progress":progress})
   
#根据学期名字获取起始日期
def get_start_form_name(semester_name:str) -> str:
    res,status=Semesters.find(
        query={"name":semester_name},
        need={"start":1}
    )
    if status == CourseStatusCode.FIND_SUCCESS:
        return res[0]['start']
    else:
        seme=semester_name.split('-')
        if seme[1] == 1:
            date=seme[0]+'-9-1'
        else:
            date=str(int(seme[0])+1)+'-3-1'
        return date

#获取某一学期学期的某一周的日期
def get_semester_week_day(week=-1,semester=-1):
    if semester != -1:
        res,status=Semesters.find(query={"name":semester},need={"start":1})
    else:
        res,status=Semesters.find(query={"isnow":1},need={"start":1})
    
    def whole_week_date(mon):
        res=[]
        for i in range(7):
            day=mon+datetime.timedelta(days=i)
            res.append(str(day))
        return res
    
    if status == CourseStatusCode.FIND_SUCCESS:
        res=res[0]
        start_week=get_start_of_week(datetime.datetime.strptime(res['start'],"%Y-%m-%d").date())
        if week == -1:
            now_week=get_start_of_week(datetime.datetime.now().date())
            res=whole_week_date(now_week)
            return res
        else:
            query_week=start_week+datetime.timedelta(weeks=week-1)
            res=whole_week_date(query_week)
            return res
    else:
        now_week=get_start_of_week(datetime.datetime.now().date())
        if week == -1:
            res=whole_week_date(now_week)
            return res
        else:
            if now_week.month < 3:
                start_week=datetime.datetime.strptime(str(now_week.year-1)+'-9-1',"%Y-%m-%d").date()
            elif 9 > now_week.month >=3 :
                start_week=datetime.datetime.strptime(str(now_week.year)+'-3-1',"%Y-%m-%d").date()
            else:
                start_week=datetime.datetime.strptime(str(now_week.year)+'-9-1',"%Y-%m-%d").date()
            query_week=start_week+datetime.timedelta(weeks=week-1)
            res=whole_week_date(query_week)
            return res   

class baseScheme(Resource):
    """docstring for createTime."""
    @login_required
    def post(self):
        try:
            args=request.json
            course_id=args['course_id']
            seme=args['semester']
            assert course_id != None,'course_scheme:empty course id'
            _,status=CourseTime.find(query={
                "course_id":course_id,
                "semester":seme
            },need={})
            if status == CourseStatusCode.FIND_SUCCESS:
                return Response.failed(message='the scheme of this course is already existed')
            
            res,status=CourseInfo.find_by_id(_id=course_id,need={
                "course_name":1
            })
            if not status == CourseStatusCode.FIND_SUCCESS:
                return Response.failed(message='course scheme:not found course id')
            
            course_name=res['course_name']
            args.update({
                "first_week":get_start_form_name(args['semester']),
                "course_name":course_name,
                "progress":0,
                "total":0,
                "available":1,
            })
            cul_total_sc(args)
            update_progress(args)
            id,status=CourseTime.insert_one(data=args)
            if status == CourseStatusCode.INSERT_SUCCESS:
                week_course.__init__()
                return Response.success(message="create new scheme for "+str(course_id)+' successfully.',
                                        data=str(id))
            else:
                return Response.failed(message='create scheme failed.')
        except Exception as e:
            error=str(e)+';;'+str(e.__traceback__.tb_frame.f_globals["__file__"])+';;'+str(e.__traceback__.tb_lineno)
            logger.exception(error)
            logger.warning("create scheme meets error: {}".format(error))
            return Response.failed(data="", message=error)
        
class updateScheme(Resource):
       
    @login_required
    def get(self):
        try:
            course_id=request.args.get('course_id')
            assert course_id != None,'update scheme:empty course_id'
            res,status=CourseTime.find(
                query={
                    "course_id":course_id
                },
                need={
                    "_id": 1,
                    "course_id": 1,
                    "course_name": 1,
                    "semester": 1,
                    "first_week": 0,
                    "duration": 1
                }
            )
            if not status == CourseStatusCode.FIND_SUCCESS:
                return Response.failed(message='update scheme:not found')
            res[0]['_id']=str(res[0]['_id'])
            return Response.success(message='update scheme:success.',data=res[0])
        except Exception as e:
            error=str(e)+';;'+str(e.__traceback__.tb_frame.f_globals["__file__"])+';;'+str(e.__traceback__.tb_lineno)
            logger.exception(error)
            logger.warning("create scheme meets error: {}".format(error))
            return Response.failed(data="", message=error)
    
    @login_required
    def post(self):
        try:
            args=request.json
            _id=str(args['_id'])
            assert _id != None,'update scheme:empty _id'
            updates={
                "semester": args['semester'],
                "first_week": get_start_form_name(args['semester']),
                "duration": args['duration']
            }
            if 'course_id' in args.keys():
                updates.update({
                    "course_id":args['course_id'],
                    "course_name":args['course_name']
                })
            update_progress(updates)
            _,status=CourseTime.update_one_by_id(_id=_id,
                                                update=updates)
            if status == CourseStatusCode.UPDATE_SUCCESS:
                week_course.__init__()
                return Response.success(message='updateScheme:success')
            else:
                return Response.failed(message='updateScheme:failed,mismatch _id')
        except Exception as e:
            error=str(e)+';;'+str(e.__traceback__.tb_frame.f_globals["__file__"])+';;'+str(e.__traceback__.tb_lineno)
            logger.exception(error)
            logger.warning("create scheme meets error: {}".format(error))
            return Response.failed(data="", message=error)

    @login_required
    def delete(self):
        id=request.args.get('id')
        _,status=CourseTime.delete_by_id(_id=id)
        if status == CourseStatusCode.DELETE_SUCCESS:
            return Response.success(message='updateScheme: delete success',data=[])
        else:
            return Response.failed(message='updateScheme:delete failed')

def get_start_of_week(date):
    # 找到给定日期所在周的开始日期（星期一）
    start_of_week = date - timedelta(days=date.weekday())
    return start_of_week

class weekCourse(object):
    """用于获取本周的课程
    Args:
        object (_type_): _description_
    """
    def __init__(self) -> None:
        super().__init__()
        self.scheme={
            1:[],
            2:[],
            3:[],
            4:[],
            5:[],
            6:[],
            7:[],
            "week_date":get_semester_week_day(),
        }
        #插入示例：
        # {
        #     "course_id":,
        #     "course_name":,
        #     "time":,
        #     "location":,
        # }
        all_scheme,status=CourseTime.find(
            query={},
            need={}
        )
        self.now_week=-1
        logger.info("init week course:"+str(datetime.datetime.now()))
        if not status == CourseStatusCode.FIND_SUCCESS:
            logger.warning("empty scheme table")
            return
        today=datetime.datetime.now()
        toweek=get_start_of_week(today)
        self.now_week=toweek
        for s in all_scheme:
            day_start=datetime.datetime.strptime(s['first_week'],"%Y-%m-%d").date()
            week_start=get_start_of_week(day_start)
            now_seme_week=rrule.rrule(rrule.WEEKLY,dtstart=week_start,until=toweek).count()
            for d in s['duration']:
                if now_seme_week in d['weeks']:
                    course={
                        "course_id":s['course_id'],
                        "course_name":s['course_name'],
                        "time":d['time'],
                        "location":d['location'],
                    }
                    for day in d['day']:
                        day_course=copy.deepcopy(course)
                        course_date=week_start+timedelta(weeks=now_seme_week-1)
                        while course_date.weekday()+1 != day:
                            course_date+=timedelta(days=1)
                        course_date=course_date.strftime('%Y-%m-%d')
                        day_course.update({
                            "course_date":course_date
                        })
                        self.scheme[day].append(day_course)
        #self.scheme["week_date"]=get_semester_week_day()
                
week_course=weekCourse()

#每周定时更新
def auto_update_week_course():
    logger.info('【AUTO】updating this week course:【'+str(datetime.datetime.now())+'】')
    week_course.__init__()

#更改一下返回的形式
def change_scheme_form(scheme):
    new_scheme={}
    for i in range(1,len(scheme)):
        new_scheme.update({
            scheme['week_date'][i-1]:scheme[i]
        })
    return new_scheme

import copy
class getWeekCourse(Resource):
    """只能获取用户相关的课程"""
    @login_required
    def get(self):
        week=request.args.get('week')
        if week == None:
            week =-1
        week=int(week)
        id=session.get('id')
        #当前学期
        res,status=Semesters.find(query={"isnow":1},need={"name":1})
        semesters=res[0]['name']
        in_seme=request.args.get('semester')
        if in_seme != None:
            semesters=str(in_seme)
        #特定老师
        other_teacher_id=request.args.get('teacher')
        if other_teacher_id:
            result=self._get_teacher_scheme(semesters,week,other_teacher_id)
        elif session.get('role') == 'teacher':
            result=self._get_teacher_scheme(semesters,week,id)
        elif session.get('role') == 'student':
            result=self._get_student_scheme(semesters,week,id)
        elif session.get('role') == 'admin':
            result=week_course.scheme
        result=change_scheme_form(result)
        return Response.success(message='success',data=result)
            
    def _get_teacher_scheme(self,semester,week,id):
        if week == week_course.now_week or week == -1:
            scheme_copy=copy.deepcopy(week_course.scheme)
            for day,courses in scheme_copy.items():
                if day == 'week_date':
                    continue
                remove_list=[]
                for course in courses:
                    course_id=course['course_id']
                    _,status=CourseTeacher.find(
                        query={
                            "course_id":course_id,
                            "teacher_id":id
                        },
                        need={"_id":1}
                    )
                    if not status == CourseStatusCode.FIND_SUCCESS:
                        remove_list.append(course)
                for dc in remove_list:
                    courses.remove(dc)
            return scheme_copy
        else:
            scheme={
                1:[],
                2:[],
                3:[],
                4:[],
                5:[],
                6:[],
                7:[],
                "week_date":get_semester_week_day(week=week,semester=semester)
            }
            all_scheme,status=CourseTime.find(
            query={},
            need={}
            )
            for s in all_scheme:
                course_id=s['course_id']
                if s['semester'] != semester:
                    continue
                _,status=CourseTeacher.find(
                        query={
                            "course_id":course_id,
                            "teacher_id":id
                        },
                        need={"_id":1}
                    )
                if not status == CourseStatusCode.FIND_SUCCESS:
                    continue
                for d in s['duration']:
                    if week in d['weeks']:
                        course={
                            "course_id":s['course_id'],
                            "course_name":s['course_name'],
                            "time":d['time'],
                            "location":d['location'],
                        }
                        for day in d['day']:
                            day_course=copy.deepcopy(course)
                            day_start=datetime.datetime.strptime(s['first_week'],"%Y-%m-%d").date()
                            week_start=get_start_of_week(day_start)
                            course_date=week_start+timedelta(weeks=week-1)
                            while course_date.weekday()+1 != day:
                                course_date+=timedelta(days=1)
                            course_date=course_date.strftime('%Y-%m-%d')
                            day_course.update({
                                "course_date":course_date
                            })
                            scheme[day].append(day_course)
        return scheme
    
    def _get_student_scheme(self,semester,week,id):
        if week == week_course.now_week or week == -1:
            scheme_copy=copy.deepcopy(week_course.scheme)
            for day,courses in scheme_copy.items():
                if day == 'week_date':
                    continue
                remove_list=[]
                for course in courses:
                    course_id=course['course_id']
                    _,status=CourseStudent.find(
                        query={
                            "course_id":course_id,
                            "student_id":id
                        },
                        need={"_id":1}
                    )
                    if not status == CourseStatusCode.FIND_SUCCESS:
                        remove_list.append(course)
                for dc in remove_list:
                    courses.remove(dc)
            return scheme_copy
        else:
            scheme={
                1:[],
                2:[],
                3:[],
                4:[],
                5:[],
                6:[],
                7:[],
                "week_date":get_semester_week_day(week=week,semester=semester),
            }
            all_scheme,status=CourseTime.find(
            query={},
            need={}
            )
            for s in all_scheme:
                course_id=s['course_id']
                if s['semester'] != semester:
                    continue
                _,status=CourseStudent.find(
                        query={
                            "course_id":course_id,
                            "student_id":id
                        },
                        need={"_id":1}
                    )
                if not status == CourseStatusCode.FIND_SUCCESS:
                    continue
                for d in s['duration']:
                    if week in d['weeks']:
                        course={
                            "course_id":s['course_id'],
                            "course_name":s['course_name'],
                            "time":d['time'],
                            "location":d['location'],
                        }
                        for day in d['day']:
                            day_course=copy.deepcopy(course)
                            day_start=datetime.datetime.strptime(s['first_week'],"%Y-%m-%d").date()
                            week_start=get_start_of_week(day_start)
                            course_date=week_start+timedelta(weeks=week-1)
                            while course_date.weekday()+1 != day:
                                course_date+=timedelta(days=1)
                            course_date=course_date.strftime('%Y-%m-%d')
                            day_course.update({
                                "course_date":course_date
                            })
                            scheme[day].append(day_course)
        return scheme
    
    def _get_admin_scheme(self,semester,week,id):
        if week == week_course.now_week or week == -1:
            scheme_copy=copy.deepcopy(week_course.scheme)
            return scheme_copy
        else:
            scheme={
                1:[],
                2:[],
                3:[],
                4:[],
                5:[],
                6:[],
                7:[],
                "week_date":get_semester_week_day(week=week,semester=semester)
            }
            all_scheme,status=CourseTime.find(
            query={},
            need={}
            )
            for s in all_scheme:
                course_id=s['course_id']
                if s['semester'] != semester:
                    continue
                for d in s['duration']:
                    if week in d['weeks']:
                        course={
                            "course_id":s['course_id'],
                            "course_name":s['course_name'],
                            "time":d['time'],
                            "location":d['location'],
                        }
                        for day in d['day']:
                            day_course=copy.deepcopy(course)
                            day_start=datetime.datetime.strptime(s['first_week'],"%Y-%m-%d").date()
                            week_start=get_start_of_week(day_start)
                            course_date=week_start+timedelta(weeks=week-1)
                            while course_date.weekday()+1 != day:
                                course_date+=timedelta(days=1)
                            course_date=course_date.strftime('%Y-%m-%d')
                            day_course.update({
                                "course_date":course_date
                            })
                            scheme[day].append(day_course)
        return scheme
    
    
class getProgress(Resource):
    @login_required
    def get(self):
        course_id=request.args.get("course_id")
        seme,status=Semesters.find(query={"isnow":1},need={"name":1})
        if not status == CourseStatusCode.FIND_SUCCESS:
            now=datetime.datetime.now().date()
            if 3 <= now.month < 9:
                seme=str(now.year)+'-2'
            elif 9 <= now.month:
                seme=str(now.year)+'-1'
            else:
                seme=str(now.year-1)+'-1'
        seme=seme[0]['name']
        res,status=CourseTime.find(query={"course_id":course_id,"semester":seme},
                                   need={"_id":0,
                                       "duration":1,
                                       "total":1,
                                       "progress":1})
        if not status == CourseStatusCode.FIND_SUCCESS:          
            return Response.success(message='success',data={"progress":0.5,'total':48,"done":24})
        res=res[0]
        if not 'total' in res.keys():
            cul_total_sc(res)
        back={
            "progress":res["progress"],
            "total":res['total'],
            "done":int(res['total']*res["progress"])
        }
        return Response.success(message='success',data=back)
        
class getWeekCourseInfo(Resource):
    @login_required
    def get(self):
        try:
            id=session.get('id')
            #老师
            courses = self._get_close_courses()
            if session.get('role') == 'teacher':
                result=self._get_teacher_week_courses(id,courses)
            elif session.get('role') == 'student':
                result=self._get_student_week_courses(id,courses)
            elif session.get('role') == 'admin':
                result=self._get_admin_week_courses(id,courses)        
            search=request.args.get("search")
            if search != None:
                sres=[]
                for res in result:
                    if re.search(search,res['course_name']) !=None:
                        sres.append(res)                       
                result=sres
            return Response.success(message='success',data=result)
        except Exception as e:
            error=str(e)+';;'+str(e.__traceback__.tb_frame.f_globals["__file__"])+';;'+str(e.__traceback__.tb_lineno)
            logger.exception(error)
            logger.warning("error: {}".format(error))
            return Response.failed(data="", message=error)
    
    def _get_close_courses(self,) -> list:
        seme_info,status = Semesters.find(query={"isnow":1},need={})
        if not status == CourseStatusCode.FIND_SUCCESS:
            return []
        seme=seme_info[0]['name']
        start=datetime.datetime.strptime(seme_info[0]['start'],"%Y-%m-%d").date()
        total_week=seme_info[0]['week']
        courses,status = CourseTime.find(
            query={
                "semester":seme
            },
            need={}
        )
        if not status == CourseStatusCode.FIND_SUCCESS:
            return []
        now=get_start_of_week(date=datetime.datetime.now().date())
        now_week=rrule.rrule(rrule.WEEKLY,dtstart=start,until=now).count()+1
        
        result=[]
        course_list=[]
        
        while len(result) < 1 and now_week <= total_week:
            for cou in courses:
                if cou['course_id'] in course_list:continue
                for du in cou['duration']:
                    if now_week in du['weeks']:
                        course_info=self.sget_course_info(cou['course_id'])
                        week_date=start+datetime.timedelta(weeks=now_week-1)
                        for day in du['day']:
                            course_date=week_date+datetime.timedelta(days=day-1)
                            if course_date > datetime.datetime.now().date():
                                break
                        course_info.update({
                            "date":course_date.strftime('%Y-%m-%d'),
                            "time":du['time'],
                            "location":du['location']
                        })
                        result.append(course_info)
                        course_list.append(cou['course_id'])      
                        break
            now_week+=1
        
        return result        
    
    
    def _get_teacher_week_courses(self,id:str,courses:list):
        course_info=[]
        for cou in courses:
            _,status=CourseTeacher.find(
                query={
                    "course_id":cou['_id'],
                    "teacher_id":id
                },
                need={}
            )
            if status == CourseStatusCode.FIND_SUCCESS:
                course_info.append(cou)
        return course_info
    
    def _get_student_week_courses(self,id:str,courses:list):
        course_info=[]
        for cou in courses:
            _,status=CourseStudent.find(
                query={
                    "course_id":cou['_id'],
                    "student_id":id
                },
                need={}
            )
            if status == CourseStatusCode.FIND_SUCCESS:
                course_info.append(cou)
        return course_info
    
    def _get_admin_week_courses(self,id:str,courses:list):

        return courses
    
    def sget_course_info(self,course_id):
        ct_res,status=CourseTeacher.find(query={"course_id":course_id},
                                         need={"_id":0,"course_id":1,"teacher_id":1})
        for r in ct_res:
            cinfo,status=CourseInfo.find_by_id(_id=r['course_id'],
                                                need={
                                                    "_id": 1,
                                                    "course_name": 1,
                                                    "course_image": 1,
                                                    "course_type":1,
                                                    "course_status":1,
                                                    "course_organization": 1,
                                                    "open_date":1,
                                                    "finish_date":1,
                                                })
            if not status == CourseStatusCode.FIND_SUCCESS:
                continue
            classid=cinfo["course_organization"]
            classinfo=getClassName(classid)
            cinfo["course_organization"]=classinfo
            #把老师id加入进去
            teacherinfo=getTeacherName(r["teacher_id"])
            cinfo.update({"course_teacher_id":teacherinfo})
            cinfo['_id']=str(cinfo['_id'])
            #课时信息
            seme,_ = Semesters.find(query={"isnow":1},need={})
            seme=seme[0]['name']
            p,status = CourseTime.find(
                query={
                    "course_id":course_id,
                    "semester":seme
                },
                need={}
            )
            if status == CourseStatusCode.FIND_SUCCESS:
                p=p[0]['progress']
            else:
                p=0.42
            cinfo.update({
                "progress":p
            })
            return cinfo
        

##################################学期管理#####################################
def set_unique_semester(id):
    Semesters.update_many(query={},update={"isnow":0})
    Semesters.update_one_by_id(_id=id,update={"isnow":1})

class CreateSemesters(Resource):
    @login_required
    def post(self):
        args=request.json
        try:
            news={
                "available":1,
                "name":args['name'],
                "start":args['start'],
                "end":args['end'],
                "week":25,
                "isnow":0,
            }
            start_week=get_start_of_week(datetime.datetime.strptime(news['start'],"%Y-%m-%d").date())
            end_week=get_start_of_week(datetime.datetime.strptime(news['end'],"%Y-%m-%d").date())
            week_count=rrule.rrule(rrule.WEEKLY,dtstart=start_week,until=end_week).count()
            news['week']=week_count
            now_date=datetime.datetime.now().date()
            id,status=Semesters.insert_one(data=news)
            if start_week <= now_date <= end_week:
                set_unique_semester(id)
            if status == CourseStatusCode.INSERT_SUCCESS:
                return Response.success(message='create new semester successfully',data=str(id))
            else:
                return Response.failed(message='create new semester failed',)
        except Exception as e:
            error=str(e)+';;'+str(e.__traceback__.tb_frame.f_globals["__file__"])+';;'+str(e.__traceback__.tb_lineno)
            logger.exception(error)
            logger.warning("create scheme meets error: {}".format(error))
            return Response.failed(data="", message=error)

class getSemesters(Resource):
    @login_required
    def get(self,):
        need_now=request.args.get("now")
        if need_now == None :
            all,status=Semesters.find(query={},need={
                "_id":1,
                "name":1,
                "start":1,
                "end":1,
                "week":1,
                "isnow":1,
            })
            if not status == CourseStatusCode.FIND_SUCCESS:
                return Response.success(message='empty',data=[])
            for s in all:
                s['_id']=str(s['_id'])
            return Response.success(message='success',data=all)
        else:
            ns,status=Semesters.find(query={"isnow":1},need={
                "_id":1,
                "name":1,
                "start":1,
                "end":1,
                "week":1,
                "isnow":1,
            })
            if not status == CourseStatusCode.FIND_SUCCESS:
                return Response.success(message='放假中',data=[])
            ns=ns[0]
            ns['_id']=str(ns['_id'])
            start_week=get_start_of_week(datetime.datetime.strptime(ns['start'],"%Y-%m-%d").date())
            now_week=get_start_of_week(datetime.datetime.now().date())
            now_week=rrule.rrule(rrule.WEEKLY,dtstart=start_week,until=now_week).count()
            ns.update({"current":now_week})
            return Response.success(message='success',data=ns)
            
        
class UpdateSemesters(Resource):
    @login_required
    def get(self,):
        sid=request.args.get('id')
        seme,status=Semesters.find_by_id(_id=sid,need={
            "_id":1,
            "name":1,
            "start":1,
            "end":1,
            "week":1,
            "isnow":1,
        })
        if not status == CourseStatusCode.FIND_SUCCESS:
            return Response.success(message='UpdateSemesters:not found',data=[])
        seme['_id']=str(seme['_id'])
        return Response.success(message='success',data=seme)
    
    @login_required
    def post(self):
        args=request.json
        id=args['id']
        news={
            "name":args['name'],
            "start":args['start'],
            "end":args['end'],
            "week":25,
            "isnow":0,
        }
        start_week=get_start_of_week(datetime.datetime.strptime(news['start'],"%Y-%m-%d").date())
        end_week=get_start_of_week(datetime.datetime.strptime(news['end'],"%Y-%m-%d").date())
        week_count=rrule.rrule(rrule.WEEKLY,dtstart=start_week,until=end_week).count()
        news['week']=week_count
        now_date=datetime.datetime.now().date()
        _,staus=Semesters.update_one_by_id(_id=id,update=news)
        if not staus == CourseStatusCode.UPDATE_SUCCESS:
            return Response.failed(message='UpdateSemesters:update failed')
        if start_week <= now_date <= end_week:
                set_unique_semester(id)
        return Response.success(message='success')
    
    @login_required
    def delete(self):
        id=request.args.get('id')
        _,status=Semesters.delete_by_id(_id=id)
        if status == CourseStatusCode.DELETE_SUCCESS:
            return Response.success(message='semester delete successfully')
        else:
            return Response.failed(message='semester delete failed')

class setNowSemester(Resource):
    @login_required
    def get(self):
        id=request.args.get('id')
        set_unique_semester(id)
        return Response.success(message='success')
    
class getCalendar(Resource):
    @login_required
    def get(self):
        semester=request.args.get("semester")
        if semester == None:
            res,status=Semesters.find(query={"isnow":1},need={"start":1,"end":1,"week":1})
        else:
            res,status=Semesters.find(query={"name":semester},need={"start":1,"end":1,"week":1})
        if not status == CourseStatusCode.FIND_SUCCESS:
            return Response.failed(message='getCalendar:semester not exist')
        res=res[0]
        start_week=get_start_of_week(datetime.datetime.strptime(res['start'],"%Y-%m-%d").date())
        end_week=datetime.datetime.strptime(res['end'],"%Y-%m-%d").date()
        week_count=res['week']
        dates=[]
        
        def whole_week_date(mon):
            res=[]
            for i in range(7):
                day=mon+datetime.timedelta(days=i)
                res.append(str(day))
            return res
        
        for i in range(week_count):
            query_week=start_week+datetime.timedelta(weeks=i)
            dates.append(whole_week_date(query_week))
        
        return Response.success(message='success',data=dates)
        
class GetTeacherScheme(Resource):
    @login_required
    def get(self):
        try:
            tid=request.args.get('teacher')
            if tid == None:
                tid=str(session.get('id'))
            semester=request.args.get('semester')
            if semester == None:
                seme,status=Semesters.find(query={"isnow":1},need={"name":1})
                if status == CourseStatusCode.FIND_SUCCESS:
                    semester=seme[0]["name"]
            cs,status=CourseTeacher.find(
                query={'teacher_id':tid},
                need={"course_id":1}
            )
            if not status == CourseStatusCode.FIND_SUCCESS:
                return Response.success(message='GetTeacherScheme:empty',data=[])
            schemes=[]
            for c in cs:
                course_id=c['course_id']
                s,status=CourseTime.find(
                    query={"course_id":course_id,"semester":semester},
                    need={
                        "_id": 1,
                        "course_id": 1,
                        "course_name": 1,
                        "semester": 1,
                        "duration": 1
                    }
                )
                if status == CourseStatusCode.FIND_SUCCESS:
                    s=s[0]
                    s['_id']=str(s['_id'])
                    schemes.append(s)
            return Response.success(data=schemes,message='success')
        except Exception as e:
            error=str(e)+';;'+str(e.__traceback__.tb_frame.f_globals["__file__"])+';;'+str(e.__traceback__.tb_lineno)
            logger.exception(error)
            logger.warning("create scheme meets error: {}".format(error))
            return Response.failed(data="", message=error)


class SummerWinterTime(Resource):
    @login_required
    def get(self):
        summer = {
            "第一节课":"8:00-8:45",
            "第二节课":"8:55-9:40",
            "第三节课":"10:10-10:55",
            "第四节课":"11:05-11:50",
            "第五节课":"14:30-15:15",
            "第六节课":"15:20-16:05",
            "第七节课":"16:25-17:10",
            "第八节课":"17:15-18:00",
            "第九节课":"19:00-19:45",
            "第十节课":"19:50-20:35",
            "第十一节课":"20:45-21:30",
            "第十二节课":"21:35-22:20",
        }
        winter = {
            "第一节课":"8:00-8:45",
            "第二节课":"8:55-9:40",
            "第三节课":"10:10-10:55",
            "第四节课":"11:05-11:50",
            "第五节课":"14:00-14:45",
            "第六节课":"14:50-15:35",
            "第七节课":"15:55-16:40",
            "第八节课":"16:45-17:30",
            "第九节课":"18:30-19:15",
            "第十节课":"19:20-20:05",
            "第十一节课":"20:15-21:00",
            "第十二节课":"21:05-21:50",
        }
        
        now_month = datetime.datetime.now().month
        if  10 > now_month >= 5:
            return Response.success(message='summer time',data=summer)
        else:
            return Response.success(message='winter time',data=winter)