#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File   : config.py
# @desc   : "项目配置文件"

import os
from kombu import Exchange, Queue

basedir = os.path.abspath(os.path.dirname(__file__))

class Config(object):
    # flask app config
    DEBUG = True
    threeD_PLATFORM = "https://********"
    # AUTH = True
    SERVER_HOST = "0.0.0.0"
    SERVER_PORT = 50020
    SECRET_KEY = 'B10ySw1nPL8JBo6ztjdljaoeklakel'

    # logger path
    LOGGER_PATH = basedir + '/logs/'

    # Redis configuration
    REDIS_HOST = "************"
    REDIS_PORT = 6379
    REDIS_USERNAME = ""
    REDIS_PASSWORD = ""
    REDIS_DB = 0

    # MongoDB configuration
    MONGO_HOST = '************'
    MONGO_PORT = 27017
    MONGO_DB = 'gwj'
    MONGO_USER = 'gwj'
    MONGO_PASSWD = 'gwjc415'

    # flask config
    DEBUG = True
    # AUTH = False
    SERVER_HOST = "0.0.0.0"
    SERVER_PORT = 50020
    UPLOAD_FOLDER = 'upload/'
    # SECRET_KEY = 'B10ySw1nPL8JBo6z'
    Resource_root_path = '../'

    # ----------------------celery 配置------------------------

    BROKER_URL = 'redis://{}:{}@{}:{}/{}'.format(REDIS_USERNAME, REDIS_PASSWORD, REDIS_HOST, REDIS_PORT,
                                                 REDIS_DB)
    CELERY_RESULT_BACKEND = 'redis://{}:{}@{}:{}/{}'.format(REDIS_USERNAME,REDIS_PASSWORD,REDIS_HOST,REDIS_PORT,REDIS_DB)   #结果存储地址
    CELERY_TASK_SERIALIZER = "json"
    CELERY_RESULT_SERIALIZER = "json"
    CELERY_TASK_RESULT_EXPIRES = 60 * 60 * 24
    CELERY_ACCEPT_CONTENT = ['json','msgpack']
    CELERY_ACKS_LATE = True  # 任务发送完成是否需要确认

    CELERY_TIMEZONE = "Asia/Shanghai"
    CELERYD_CONCURRENCY = 20  # 并发worker数
    CELERY_IMPORTS = (
        'core.tasks.packet_analysis.packet_fetch',
        'core.tasks.tools.system',
    )

    # CELERY_TASK_PATH = "task_manager.task_pool.tasks"
    # imports=(
    #     'core.tasks.packet_analysis.packet_fetch',
    #     'core.tasks.tools.system'
    # )

    # 某个程序中出现的队列，在broker中不存在，则立刻创建它
    CELERY_CREATE_MISSING_QUEUES = True
    CELERYD_FORCE_EXECV = True  # 非常重要,有些情况下可以防止死锁
    CELERYD_MAX_TASKS_PER_CHILD = 100  # 每个worker最多执行万100个任务就会被销毁，可防止内存泄露

    # # 定义一个默认交换机
    # default_exchange = Exchange('dedfault', type='direct')

    # # 定义一个媒体交换机
    # media_exchange = Exchange('media', type='direct')

    CELERY_QUEUES = (
        Queue('packet_analysis_fetch', exchange=Exchange('packet_analysis_fetch',type='direct'),routing_key='packet_analysis_fetch'),   #设置packet_analysis_fetch队列,指定routing_key
        # Queue('vul_scan',  routing_key='vul.scan'),
    )

    #
    CELERY_ROUTES = {
            'core.tasks.tools.system.t_schedule_update_system_info': {  #tasks.celery_tasks.packet_fetch_任务进packet_analysis_fetch队列
                'queue': 'packet_analysis_fetch',
                'routing_key': 'packet_analysis_fetch'
            },
        'core.tasks.packet_analysis.packet_fetch.test': {
        # tasks.celery_tasks.packet_fetch_任务进packet_analysis_fetch队列
            'queue': 'packet_analysis_fetch',
            'routing_key': 'packet_analysis_fetch'
        }
    }

