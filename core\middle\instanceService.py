'''
-*- coding:utf-8 -*-
@Project ：jxsjpt 
@File    ：hworkService.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2023/3/18 17:08 
@desc    ：处理实例相关的业务
'''
from bson import ObjectId

from common.utils.ilab_util import ilab_util
from core.databases.orm.other.instance_orm import InstanceOrm
from common.utils.logger import logger



class InstanceService:
    def __init__(self):
       self.dbInstanceOrm = InstanceOrm()

    def queryInstance(self, queryJson,page,size):
        logger.info("查询实例列表")
        jsonData = self.dbInstanceOrm.find_by_page(queryJson,page,size)
        total = self.dbInstanceOrm.count(queryJson)
        logger.info("实例总数：{}".format(total))
        logger.info("当前页：{}".format(page))

        result = {}
        dataList = []
        for data in jsonData:
            data['_id'] = str(data['_id'])
            dataList.append(data)
        logger.info("当前页数据条数：{}".format(len(dataList)))
        result['data'] = dataList
        result['total'] = total
        result['page'] = page
        result['size'] = size
        return result

    def queryInstanceDetail(self,_id):
        instance = self.dbInstanceOrm.find_by_id(_id)
        instance['_id'] = str( instance['_id'])
        return instance
    def saveInstance(self, jsonData):
        logger.info("保存实例数：{}".format(str(jsonData)))
        return self.dbInstanceOrm.add_one(jsonData)

    def modifyInstance(self, jsonData,ins_id):
        logger.info("修改实例状态：{}||{}".format(ins_id,jsonData))
        success = self.dbInstanceOrm.update_by_condition({"ins_id": ins_id}, jsonData)
        return success

    def deleteInstance(self, jsonData):
        logger.info("删除实例：{}".format(jsonData))
        ins_id = {"ins_id": jsonData}
        self.dbInstanceOrm.delete_by_condition(ins_id)
        return True

    def canStart(self, course_id, chapter_id):
        '''
        是否可以启动，查看当前课程启动的实例是否超过给定阈值
        '''
        count = self.dbInstanceOrm.count({"$and":[{"course_id":course_id}]})
        if False:
        # if count >= ilab_util.current_running_count:
            logger.info("课程{}已启动的实例数为{},不可再启动".format(course_id,count))
            return -1,"需要等待，目前已经启动{}个虚拟试验".format(count)
        else:
            return 1,"可以启动，当前系统已启动{}个虚拟试验".format(count)

