#!/usr/bin/env python
# -*- coding: UTF-8 -*-
'''
@Project ：jxsjpt 
@File    ：pathConcat.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2023-03-23 13:09 
'''

import socket
from urllib.request import urlopen

from config import Config


def outer_url(route):
    try:
        ip = eval(urlopen('http://*************/').read())["ip"]
        url = 'http://' + ip + ':' + str(Config.SERVER_PORT) + route
    except:
        url = 'http://127.0.0.1:' + str(Config.SERVER_PORT) + route
    return url


def inner_url(route):
    url = 'http://127.0.0.1:' + str(Config.SERVER_PORT) + route
    return url
