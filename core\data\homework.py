'''
-*- coding:utf-8 -*-
@Project ：jxsjpt 
@File    ：homework.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2023/3/18 16:02 
@desc    ：课程作业库封装对象
'''


class Homework:
    def __init__(self, data):
        self._id = data['']  # 作业id
        self.course_id = data['']  # 课程id
        self.teacher_id = data['']  # 老师id
        # self.classId = data['']  # 班级id
        # self.studentCount = data['']  # 班级人数
        # self.handedCount = data['']  # 已提交人数
        # self.startTime = data['']  # 开始时间
        # self.endTime = data['']  # 结束时间
        self.question_count = data['']  # 题数
        self.state = data['']  # 作业状态 1已建立/2作废
        self.homework_name = data['']  # 作业名称
        self.score = data['']  # 本次作业总分
        self.type = data['']  # 作业内容，1普通作业 2报告

    def toDbType(self):
        return self.__dict__