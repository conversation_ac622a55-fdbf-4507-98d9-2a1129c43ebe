from bson import ObjectId
from secrets import token_hex
from common.utils.logger import logger
from core.databases.db_mongo import mongo
from core.data.response import Response, StatusCode
from core.databases.db_error import DatabaseError
from core.databases.orm.database_base import DatabaseBase
import datetime
from common.utils.dict_parse import DictParseType

import os

class _Exam_paper(DatabaseBase):
    def __init__(self):
        DatabaseBase.__init__(self)
        self.table = 'Exam_paper'

    """新增试卷"""
    def insert(self,query):
        return mongo[self.table].insert_one(query)
    
    """查看试卷列表"""
    def find_list(self,query):
        result=[]
        return mongo[self.table].find(query)
    """查看试卷"""
    def find(self,query):
        return mongo[self.table].find_one(query)
    """更新试卷"""
    def update(self,condition,data):
        return mongo[self.table].update_one(condition,data)


    #试卷删除
    def delete_by_id(self, _id):
        if _id:
            result=mongo[self.table].update_one({"_id":ObjectId(str(_id))},{"$set":{"is_delete":True}})
            if result.modified_count <= 0:
                return False
            else:
                return True
        else:
            return False

    #试卷批量删除
    def delete_by_nums(self,paper_ids):
        remain_ids=[]
        if paper_ids:
            for _id in paper_ids:
                result = mongo[self.table].update_one({"_id":ObjectId(str(_id))},{"$set":{"is_delete":True}})
                if result.modified_count:
                    continue
                else:
                    remain_ids.append(_id)
            if len(remain_ids)==0:
                return 1,remain_ids
            else:
                return 0,remain_ids
        else:
            logger.warning("delete papers failed: invalid ids")
            return False



    #修改试卷
    def change(self,paper_id,paper_name,paper_content,update_user):
        if paper_id:
            result=mongo[self.table].find_one({"_id": ObjectId(str(paper_id))})
            if result==None:
                return False
            else:
                if result["is_delete"]=="true":
                    return 100
            try:
                mongo[self.table].update_one({"_id":ObjectId(str(paper_id))},{"$set":
                    {
                    "paper_name":paper_name,
                    "paper_content":paper_content,
                    "update_user":update_user,
                    "update_time": datetime.datetime.now()
                }})
                return True
            except Exception as e:
                logger.warning("change paper failed: {}".format(paper_id))
                return False
        else:
                logger.warning("change paper failed: invalid data")
                return False


Exam_paper=_Exam_paper()

# class _Exam_question(DatabaseBase):
#     def __init__(self):
#         DatabaseBase.__init__(self)
#         self.table='Exam_question'

#     #查询试题
#     def find(self,query):
#         return mongo(self.table).find(query)
    
#     #新增试题
#     def insert(self,query):
#         return mongo[self.table].insert_one(query)
    
#     #修改试题
#     def update(self,query):
#         return mongo[self.table].insert_one(query)
    
#     #删除试题
#     def delete(self,query):
#         return mongo[self.table].delete_one(query)
# Exam_question=_Exam_question()



# class _Exam_question(DatabaseBase):
#     def __init__(self):
#         DatabaseBase.__init__(self)
#         self.table='Exam_question'

#     #查询试题
#     def find(self,query):
#         return mongo[self.table].find(query)
    
#     #新增试题
#     def insert(self,query):
#         return mongo[self.table].insert_one(query)
    
#     #修改试题
#     def update(self,query):
#         return mongo[self.table].insert_one(query)
    
#     #删除试题
#     def delete(self,query):
#         return mongo[self.table].delete_one(query)
# Exam_question=_Exam_question()

# class _Exam_knowledge(DatabaseBase):
#     def __init__(self):
#         DatabaseBase().__init__()
#         self.table='Exam_knlowledge'

#     #查询知识点
#     def find(self,query):
#         return mongo[self.table].find(query)
    
#     #新增知识点
#     def post(self,query):
#         return mongo[self.table].insert_one(query)
    
#     #删除知识点
#     def delete(self,query):
#         return mongo[self.table].delete_many(query)
# Exam_knowledge=_Exam_knowledge()