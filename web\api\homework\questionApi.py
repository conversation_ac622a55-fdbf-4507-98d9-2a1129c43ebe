"""
-*- coding:utf-8 -*-
@Project ：jxsjpt
@File    ：homeworkApi.py
@IDE     ：PyCharm
<AUTHOR>
@Date    ：2023/3/18 18:33
@desc    ：用于处理布置作业相关的请求处理,包括查询作业，保存作业，删除作业，修改作业
"""
import time
from datetime import datetime

from flask import request
# from bson import ObjectId
from flask_restful import Resource, reqparse, inputs

from common.utils.logger import logger
from core.data.response import Response
from core.hwService.question_service import QuestionService


class QuestionApi(Resource):
    def __init__(self):
        self.businessService = QuestionService()

    def get(self):
        try:
            jsonData = []
            # parser.add_argument('serial_No', type=int)
            # parser.add_argument('question_type', type=int)
            # parser.add_argument('course_id', type=str)
            # parser.add_argument'knowledge_label', type=str
            # parser.add_argument('degree', type=int)
            # parser.add_argument('chapter_id', type=str)
            # parser.add_argument('keywords', type=str)
            # args = parser.parse_args()
            # serial_No = args['serial_No']
            question_type = request.args.get('question_type', type=int)
            knowledge_label = request.args.get('knowledge_label', type=str)
            degree = request.args.get('degree', type=int)
            course_id = request.args.get('course_id', type=str)
            chapter_id = request.args.get('chapter_id', type=str)
            page = request.args.get('page', type=int)
            pageSize = request.args.get('page_size', type=int)
            if page is None:
                page = 1
            if pageSize is None:
                pageSize = 30

            query_condition = []
            if knowledge_label is not None:
                # query_condition['knowledge_label'] = knowledge_label
                query_condition.append({'knowledge_label': knowledge_label})

            if question_type is not None:
                # query_condition['question_type'] = question_type
                query_condition.append({'question_type': question_type})

            if course_id is not None:
                # query_condition['course_id'] = course_id
                query_condition.append({'course_id': course_id})

            if degree is not None:
                # query_condition['degree'] = degree
                query_condition.append({'degree': degree})

            if chapter_id is not None:
                # query_condition['chapter_id'] = chapter_id
                query_condition.append({'chapter_id': chapter_id})

            """
            将关键字转换为模糊匹配语句
            """
            keywords = request.args.get('keywords')
            if keywords is not None:
                key_word_query = '.*' + keywords
                # query_condition["question_content"] = {"$regex": key_word_query}
                query_condition.append({'question_content': {"$regex": key_word_query}})
            # query_condition["is_delete"] = False
            query_condition.append({'is_delete': False})
            """
            条件查询
            """
            homework_questions = self.businessService.query_question(
                {
                    "$and": query_condition
                },page,pageSize
            )
            total = self.businessService.query_total({
                "$and": query_condition
            })
            for question in homework_questions:
                jsonData.append(
                    {
                        "_id": str(question['_id']),
                        "question_type": question['question_type'],
                        "degree": question['degree'],
                        "knowledge_label": question['knowledge_label'],
                        "construct_time": question['construct_time'],
                        "builder": question['builder'],
                        "course_id": question['course_id'],
                        "chapter_id": question['chapter_id'],
                        "question_content": question['question_content'],
                        "question_options": question['question_options'],
                        "answer_content": question['answer_content'],
                        'ref_answer': question['ref_answer']
                    }
                )

            return Response.success(data={"total": total, "data": jsonData}, message="查询成功")

        except Exception as e:
            logger.warning("get homework question failed: {}".format(e))
            logger.exception(e)
            return Response.failed(message=e)

    def post(self):
        """
        新增题目：
        """
        parser = reqparse.RequestParser()
        parser.add_argument('question_type', type=int)
        parser.add_argument('course_id', type=str)
        parser.add_argument('knowledge_label', type=str)
        parser.add_argument('degree', type=int)
        parser.add_argument('builder', type=str)
        parser.add_argument('chapter_id', type=str)
        parser.add_argument('question_content', type=str)
        parser.add_argument('question_options', type=str)
        parser.add_argument('answer_content', type=str)
        parser.add_argument('ref_answer', type=str)
        try:

            args = parser.parse_args()
            question_type = args['question_type']
            degree = args['degree']
            knowledge_label = args['knowledge_label']
            course_id = args['course_id']
            chapter_id = args['chapter_id']
            builder = args['builder']
            question_content = args['question_content']
            question_options = args['question_options']
            answer_content = args['answer_content']
            ref_answer = args['ref_answer']
            #修改直接返回id
            # jsonData = "sava_question_id:" + str(self.businessService.save_question({
            jsonData = str(self.businessService.save_question({
                'construct_time': datetime.utcnow().strftime('%Y-%m-%dT%H:%M:%SZ'),
                'course_id': course_id,
                'chapter_id': chapter_id,
                'knowledge_label': knowledge_label,
                'question_type': question_type,
                'builder': builder.strip(),
                'degree': degree,
                'question_content': question_content,
                'question_options': question_options,
                'answer_content': answer_content,
                'ref_answer': ref_answer,
                'is_delete': False
            }))

            return Response.success(data=jsonData, message="新建成功")

        except Exception as e:
            logger.warning("get homework question failed: {}".format(e.data))
            logger.exception(e)
            return Response.failed(message=e)

    def delete(self):
        try:
            parser = reqparse.RequestParser()
            parser.add_argument('_id', type=str,action = 'append')
            args = parser.parse_args()
            _ids = args.get("_id")

            for _id in  _ids:
                self.businessService.delete_question( {'_id': _id})
            jsonData = "modified_count:"+str(len(_ids))
            return Response.success(data=jsonData, message="删除成功")

        except Exception as e:
            logger.warning("get homework question failed: {}".format(e))
            logger.exception(e)
            return Response.failed(message=e)

    def put(self):
        try:
            parser = reqparse.RequestParser()
            parser.add_argument('_id', type=str)

            parser.add_argument('question_content', type=str)
            parser.add_argument('question_options', type=str)
            parser.add_argument('answer_content', type=str)
            parser.add_argument('ref_answer', type=str)
            parser.add_argument('knowledge_label', type=str)
            parser.add_argument('degree', type=int)
            args = parser.parse_args()

            question_content = args['question_content']
            question_options = args['question_options']
            answer_content = args['answer_content']
            ref_answer = args['ref_answer']
            knowledge_label = args['knowledge_label']
            degree = args['degree']

            jsonData = self.businessService.update_question(
                {
                    '_id': args.get("_id"),
                    'question_content': question_content,
                    'question_options': question_options,
                    'answer_content': answer_content,
                    'ref_answer': ref_answer,
                    'knowledge_label':knowledge_label,
                    'degree':degree,
                    'is_delete': False
                }
            )

            return Response.success(data=jsonData, message="修改成功")

        except Exception as e:
            logger.warning("get homework question failed: {}".format(e))
            logger.exception(e)
            return Response.failed(message=e)
