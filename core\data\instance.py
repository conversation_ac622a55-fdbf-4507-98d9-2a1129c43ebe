'''
-*- coding:utf-8 -*-
@Project ：jxsjpt 
@File    ：instance.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2023/7/21 14:45 
@desc    ：管理端实例，实体类
'''
class Instance:
    def __init__(self,data):
        #self._id = None
        self.ins_id = data['ins_id']#虚拟化平台实例id
        self.model_name = data['model_name']#实例模板名称
        self.statue = data['statue']#实例状态
        self.create_time = data['create_time']#实例创建时间
        self.course_name = data['course_name']#所属课程
        self.chapter = data['chapter']#所属章节
        self.user_name = data['user_name']#实例用户名
        self.user_id = data['user_id']#实例用户id
        self.user_role = data['user_role']#用户角色
    def toDbType(self):
        return self.__dict__