#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time   : 5/18/21 5:44 AM
# <AUTHOR> jackey
# @File   : user_orm.py
# @desc   : ""


import copy
import os
import profile
import time
import hashlib
from typing import Iterable
from bson import ObjectId
from secrets import token_hex

from numpy import add
from common.utils.logger import logger
from core.databases.db_mongo import (
    T_STUDENTS,
    mongo,
    T_STUDENTS_ORGANIZATION,
    T_TEACHERS_ORGANIZATION,
)
from core.databases.db_error import DatabaseError
from core.databases.orm.database_base import DatabaseBaseCollection
from core.databases.orm.role.roles_orm import Role
from core.databases.orm.user.users_orm import DBCoursePlatformStudents as t_stu
# from web.api.course.course import deleteStudentCourse, updateNewStudentCourse


class _DBCoursePlatformOrg(DatabaseBaseCollection):
    def __init__(self):
        DatabaseBaseCollection.__init__(self)

    def get(self):
        orgs = self.find()
        data = []
        for org in orgs:
            # print(str(org["_id"]))
            # org["_id"] = str(org["_id"])
            org["_id"] = str(org["_id"])
            data.append(org)
        return data

    def get_one(self, filter=None):
        org = self.find_one(filter)
        if org:
            org["_id"] = str(org["_id"])
        return org

    def delete(self, id):
        return self.delete_one(filter={"_id": ObjectId(id)}).deleted_count


class _DBCoursePlatformStuOrg(_DBCoursePlatformOrg):
    """_summary_

    Args:
        DatabaseBase (_type_): _description_

    Example:
        {
            "id":"6299e6d3d1cb1ecbffe56026",
            "name":"华中科技大学",
            "students":[]
        }
    """

    def __init__(self):
        self.collection_name = T_STUDENTS_ORGANIZATION
        _DBCoursePlatformOrg.__init__(self)

        self.pipeline = [
            {
                "$set": {
                    "students": {
                        "$map": {
                            "input": "$students",
                            "in": {
                                "id": {"$toObjectId": "$$this.id"},
                                "join_date": "$$this.join_date",
                            },
                        }
                    }
                }
            },
            {
                "$lookup": {
                    "from": T_STUDENTS,
                    "localField": "students.id",
                    "foreignField": "_id",
                    "as": "students_info",
                }
            },
            {
                "$project": {
                    "_id":{"$toString":"$_id"},
                    "creator":{"$toString":"$creator"},
                    "name": 1,
                    "students": {
                        "$map": {
                            "input": "$students",
                            "in": {
                                "id": {"$toString":"$$this.id"},
                                "join_date": "$$this.join_date",
                                "user_name": {
                                    "$arrayElemAt": [
                                        "$students_info.user_name",
                                        {
                                            "$indexOfArray": [ 
                                                "$students_info._id", 
                                                "$$this.id",
                                            ]
                                                 
                                        },
                                    ]
                                },
                                "college": {
                                    "$arrayElemAt": [
                                        "$students_info.college",
                                        {
                                            "$indexOfArray": [ 
                                                "$students_info._id", 
                                                "$$this.id",

                                            ]
                                        },
                                    ]
                                },
                                "major": {
                                    "$arrayElemAt": [
                                        "$students_info.major",
                                        {
                                            "$indexOfArray": [ 
                                                "$students_info._id", 
                                                "$$this.id",
                                            ]
                                        },
                                    ]
                                },
                            },
                        }
                    },                    
                }
            }
        ]

    def __extend_orgs_with_students_info(self, orgs, to_str=True):
        def extend_org(org):
            if (to_str): 
                org['_id'] = str(org['_id'])
                org['creator'] = str(org.get('creator') or "")
            students_info = org['students']
            for student in students_info:
                student_id = student['id']
                # join_date = student['join_date']
                student_info = t_stu.get_one({"_id": ObjectId(student_id)})
                if not student_info: continue
                student["profile_id"] = student_info.get("profile_id")
                student["user_name"] = student_info.get("user_name")
                student["college"] = student_info.get("college")
                student["major"] = student_info.get("major")
            return org
        
        if type(orgs) is dict:
            return extend_org(orgs)
        
        if isinstance(orgs,Iterable): # 可迭代
            
            result = []
            for org in orgs:
                """
                {
                    "_id": {
                        "$oid": "648fb731bbfbe226baafb74a"
                    },
                    "name": "测试班级",
                    "creator": {
                        "$oid": "64585957c3f51bee352e35de"
                    },
                    "students": [
                        {
                        "id": "648fc3a1bbfbe226baafb751",
                        "join_date": 1687143329
                        }
                    ]
                }
                """
                result.append(extend_org(org))
            return result

    
    def get(self, filter=None, extended=True):
        orgs = self.find(filter)
        if orgs and extended:
            return self.__extend_orgs_with_students_info(orgs)
        elif orgs:
            re = []
            for org in orgs:
                org['_id'] = str(org['_id'])
                org['creator'] = str(org['creator'])
                re.append(org)
            return re
        return None

    def get_one(self, filter=None, extended=True):
        orgs = self.find_one(filter)
        if orgs and extended:
            orgs = self.__extend_orgs_with_students_info(orgs)
        return orgs

    def create(self, **kwargs):
        name = kwargs.get("name")
        creator = kwargs.get("creator")
        try:
            item = self.insert_one({"name": name, "creator": creator, "students": []})
            return str(item.inserted_id)
        except Exception as e:
            logger.warning("class create failed: {}".format(e))
            return False

    def update(self, _id, **info):
        def student_in_org(self, student_id, _id):
            pipe = [
                {"$match": {"_id": ObjectId(_id), "students.id": student_id}},
                {"$project": {"students": 1}},
                {"$unwind": "$students"},
                {"$match": {"students.id": student_id}},
                {"$project": {"_id": 0, "id": "$students.id"}},
            ]

            return self.aggregate(pipe).alive

        if info.get("name"):
            self.update_one(
                filter={"_id": ObjectId(_id)},
                update={
                    "$set": {
                        "name": info.get("name"),
                    }
                },
                upsert=False,
            )

        if info.get("add_list"):
            add_list = info["add_list"]

            students_info = []
            for user_id in add_list:
                # 如果已经存在就不添加了
                if student_in_org(self, user_id, _id):
                    logger.warning(f"user {user_id} has exists.")
                    continue
                students_info.append({"id": user_id, "join_date": int(time.time())})

            self.update_one(
                filter={"_id": ObjectId(_id)},
                update={"$addToSet": {"students": {"$each": students_info}}},
            )
            # from xieyunyang
            # updateNewStudentCourse([_id])

        if info.get("del_list"):
            del_list = info["del_list"]
            self.update_one(
                filter={"_id": ObjectId(_id)},
                update={"$pull": {"students": {'id': {"$in": del_list}}}},
                upsert=False,
            )
            # from xieyunyang
            # deleteStudentCourse([_id], del_list)


# class _DBCoursePlatformTeaOrg(_DBCoursePlatformOrg):
#     """_summary_

#     Args:
#         DatabaseBase (_type_): _description_

#     Example:
#         {
#             "id":"6299e6d3d1cb1ecbffe56026",
#             "teachers":[]
#         }
#     """

#     def __init__(self):
#         self.collection_name = T_TEACHERS_ORGANIZATION
#         _DBCoursePlatformOrg.__init__(self)

#     def create(self, **kwargs):
#         try:
#             item = self.insert_one({"teachers": []})
#             return str(item.inserted_id)
#         except Exception as e:
#             logger.warning("teacher group create failed: {} {}".format(profile_id, e))
#             return False

#     def update(self, id, **info):
#         if info.get("add_list"):
#             add_list = info["add_list"]
#             self.update_one(
#                 filter={"_id": ObjectId(id)},
#                 update={"$addToSet": {"teachers": {"$each": add_list}}},
#                 upsert=False,
#             )

#         if info.get("del_list"):
#             del_list = info["del_list"]
#             self.update_one(
#                 filter={"_id": ObjectId(id)},
#                 update={"$pull": {"teachers": {"$in": del_list}}},
#                 upsert=False,
#             )


DBCoursePlatformStuOrg = _DBCoursePlatformStuOrg()
# DBCoursePlatformTeaOrg = _DBCoursePlatformTeaOrg
