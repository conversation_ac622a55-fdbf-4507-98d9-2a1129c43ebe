#!/usr/bin/env python
# -*- coding: UTF-8 -*-
'''
@Project ：jxsjpt 
@File    ：resourceParser.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2023-03-23 22:36 
'''
from flask_restful import reqparse

def parseResource():
    # 请求为json格式
    parser = reqparse.RequestParser()
    parser.add_argument('id', type=str)
    parser.add_argument('url', type=str)
    parser.add_argument('resource_type', type=str)
    parser.add_argument('add_user', type=str)
    parser.add_argument('add_time', type=str)
    parser.add_argument('modify_user', type=str)
    parser.add_argument('modify_time', type=str)
    parser.add_argument('download_count', type=int)
    parser.add_argument('resource_size', type=int)
    parser.add_argument('resource_version', type=str)
    parser.add_argument('resource_description', type=str)
    parser.add_argument('resource_name', type=str)
    parser.add_argument('icon_id', type=str)
    parser.add_argument('course_id', type=str)
    parser.add_argument('labels', type=str, action='append')
    parser.add_argument('file_name', type=str)

    # 分页
    # 页码
    parser.add_argument('offset', type=int)
    # 页大小
    parser.add_argument('limit', type=int)

    parser.add_argument('blquery_flag', type=bool)
    parser.add_argument('screen_type_flag', type=bool)  # 用类别筛选
    parser.add_argument('screen_label_flag', type=bool)  # 用标签删选
    return parser
