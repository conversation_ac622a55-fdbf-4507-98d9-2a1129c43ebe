'''
-*- coding:utf-8 -*-
@Project ：jxsjpt 
@File    ：teachCorrectApi.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2023/3/18 18:33 
@desc    ：用于老师进入批改界面，包括按学生批改，按题批改，
'''
from flask import request,session
from flask_restful import Resource

from common.utils.logger import logger
from core.data.response import Response
from core.hwService.handInService import HandInService


from  web.api.course.innerapi import _get_semester_courses_form_sid
# from core.databases.orm.course.course_orm import CourseInfo


def selectImportantInfo(handIn):
    info = {}
    # info["hworkNo"] = handIn["hworkNo"]#课程作业表主键
    info["_id"] = str(handIn["_id"])#提交表主键
    info["courseId"] = handIn["courseId"]
    # need,_ = CourseInfo.find_by_id(info["courseId"],need={'course_name':1,'course_type':2})
    # need.pop("_id")
    info["hwName"] = handIn["hwName"]
    # info.update(need)
    info["type"] = handIn["type"] # 作业类型，1普通作业，2报告作业
    return info


class StatisticsHw(Resource):
    def __init__(self):
        self.businessService = HandInService()

    def get(self):

        try:
            studentId = session.get("id")
            # studentId = '6492b47b53c7d7246518ef4e'
            courseId = request.args.get('courseId',type=str)
            if courseId is None:#学生端首页查询
                courseInfos = _get_semester_courses_form_sid(studentId)
            else:#课程内查询
                courseInfos = [{"course_id":courseId}]
            total = 0
            done = 0
            doneList = []
            todo = 0
            todoList = []
            logger.info("学生端查询作业统计:学生是{},查询的课程是 {}".format(studentId,str(courseInfos)))
            for courseInfo in courseInfos:
                jsonData = {
                    'studentId': studentId,
                    'courseId': courseInfo['course_id']
                }
                courseInfo.pop('course_id')
                #查询已发布的作业
                handIns = self.businessService.queryHandIn(jsonData)
                for handIn in handIns:
                    state = handIn["state"]
                    total =  total+1
                    if state in [1,2,4]:
                        todo = todo + 1
                        info = selectImportantInfo(handIn)
                        info.update(courseInfo)
                        todoList.append(info)
                    else:
                        done = done + 1
                        # doneList.append(handIn)

            results = {"total": total, "done": done, "todo": todo, "todoList":todoList}
            return Response.success(data=results, message="学生端查询作业统计结果")
        except Exception as e:
            logger.warning("获取数据异常: {}".format(e))
            logger.exception(e)
            return Response.failed(data="", message=e)
