"""
-*- coding:utf-8 -*-
@Project ：jxsjpt 
@File    ：homeworkApi.py
@IDE     ：PyCharm 
<AUTHOR>
@Date    ：2023/3/18 18:33 
@desc    ：用于作业详情查询
"""

from flask import request
from flask_restful import Resource

from common.utils.logger import logger
from core.data.response import Response
from core.hwService.hworkService import HomeworkService


class HomeworkApi(Resource):
    def __init__(self):
        self.businessService = HomeworkService()

    def get(self):
        try:
            _id = request.args.get('_id', type=str)
            homeworkDetails = self.businessService.queryHworkDetail(
                {
                    '_id': _id,
                }
            )
            return Response.success(data=homeworkDetails, message="")

        except Exception as e:
            logger.exception(e)
            logger.warning("get homework detail failed: {}".format(e))
            return Response.failed(message=e)
