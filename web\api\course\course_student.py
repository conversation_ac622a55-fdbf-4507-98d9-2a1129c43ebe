from flask import request
from flask_restful import Resource, reqparse
from common.utils.logger import logger
from core.data.response import Response, StatusCode
from core.databases.orm.course.course_orm import *
from flask_restful import Resource, reqparse
from core.auth.auth import *
from bson import ObjectId
import re
import time
import datetime

from core.databases.orm.homework.handInHwork_orm import HandInHworkOrm
from core.databases.orm.user.organzation_orm import *
from core.databases.orm.user.users_orm import *
from core.hwService.handInService import HandInService

def cul_total_du(duration):
    total=0
    for d in duration:
        total+=len(d['weeks'])*len(d['day'])*len(d['time'])
    return total

def cul_total_sc(scheme:dict):
    total=0
    total+=cul_total_du(scheme['duration'])
    if  'total' in scheme.keys():
        scheme['total']=total
    else:
        scheme.update({
            "total":total
        })

def _get_process(course_id):
    seme,status=Semesters.find(query={"isnow":1},need={"name":1})
    if not status == CourseStatusCode.FIND_SUCCESS:
        now=datetime.datetime.now().date()
        if 3 <= now.month < 9:
            seme=str(now.year)+'-2'
        elif 9 <= now.month:
            seme=str(now.year)+'-1'
        else:
            seme=str(now.year-1)+'-1'
    seme=seme[0]['name']
    res,status=CourseTime.find(query={"course_id":str(course_id),"semester":seme},
                                need={"_id":0,
                                    "duration":1,
                                    "total":1,
                                    "progress":1})
    if not status == CourseStatusCode.FIND_SUCCESS:          
        return {"progress":0.5,'total':48,"done":24}
    res=res[0]
    if not 'total' in res.keys():
        cul_total_sc(res)
    back={
        "progress":res["progress"],
        "total":res['total'],
        "done":int(res['total']*res["progress"])
    }
    return back


##根据班级id返回名称
def getClassName(classid):
    data=[]
    for c in classid:
        res=DBCoursePlatformStuOrg.get_one({"_id":ObjectId(str(c))})
        if res == None:
            continue
        name=res['name']
        data.append(
            {
                "name":str(name),
                "id":str(c)
            }
        )
    return data
##根据教师id返回名称
def getTeacherName(teacherid):
    data=[]
    for t in teacherid:
        res=DBCoursePlatformTeachers.get_one({"_id":ObjectId(str(t))})
        if res == None:
            continue
        name=res['user_name']
        data.append(
            {
                "name":str(name),
                "id":str(t)
            }
        )
    return data


#**********************************学生查看自己已经选的课程******************************
class StudentChooseCourse(Resource):
    """API /api/v1/course/student_center/chosen_course"""
    # @login_required
    def get(self):
        try:
            student_id=session.get('id')
            if student_id == None:
                raise ValueError('user null')
            result,status=CourseStudent.find(
                query={"student_id":student_id},
                need={"_id": 0,
                      "course_id":1}
            )
            if not status == CourseStatusCode.FIND_SUCCESS:
                return Response.success(data=[],message="empty course")
            data=[]
            for r in result:
                course_info,status=CourseInfo.find(
                    query={"_id":ObjectId(str(r['course_id'])),
                           "course_type":"理论课"},
                    need={
                        "_id":0,
                        "course_name": 1,
                        "course_image": 1,
                        "course_status":1,
                        "course_organization": 1,
                        "open_date":1,
                        "finish_date":1,
                    }
                )
                if status == CourseStatusCode.FIND_SUCCESS:
                    r.update(course_info[0])
                    #更新班级
                    classid=r['course_organization']
                    classinfo=getClassName(classid)
                    r['course_organization']=classinfo
                    teacher,status=CourseTeacher.find(
                        query={"course_id":str(r['course_id'])},
                        need={
                            "_id":0,
                            "teacher_id":1,
                        }
                    )
                    if not status == CourseStatusCode.FIND_SUCCESS:
                        raise RuntimeError('error in find ct table')
                    for t in teacher:
                        teacherid=t['teacher_id']
                        teacherinfo=getTeacherName(teacherid)
                        r.update({"course_teacher_id":teacherinfo})
                    process=_get_process(r["course_id"])
                    r.update(process)
                    data.append(r)
            search=None
            datas=[]
            search=request.args.get("search")
            if not search == None:
                for d in data:
                    r=re.search(search,d["course_name"])
                    if r:
                        datas.append(d)
                data=datas
            
            return Response.success(data=data,message='success')
        except Exception as e:
            logger.warning("get method meets error: {}".format(e))
            return Response.failed(data="", message=e)

#**********************************学生查看自己已经选的实验******************************
class StudentChooseExperiment(Resource):
    """API /api/v1/course/student_center/chosen_experiment"""
    # @login_required
    def get(self):
        try:
            student_id=session.get('id')
            if student_id == None:
                raise ValueError('user null')
            result,status=CourseStudent.find(
                query={"student_id":student_id},
                need={"_id": 0,
                      "course_id":1}
            )
            if not status == CourseStatusCode.FIND_SUCCESS:
                return Response.success(data=[],message="empty course")
            data=[]
            for r in result:
                course_info,status=CourseInfo.find(
                    query={"_id":ObjectId(str(r['course_id'])),
                           "course_type":"实验课"},
                    need={
                        "_id":0,
                        "course_name": 1,
                        "course_image": 1,
                        "course_status":1,
                        "course_organization": 1,
                        "open_date":1,
                        "finish_date":1,
                    }
                )
                if not status == CourseStatusCode.FIND_SUCCESS:
                    continue
                r.update(course_info[0])
                #更新班级
                classid=r['course_organization']
                classinfo=getClassName(classid)
                r['course_organization']=classinfo
                teacher,status=CourseTeacher.find(
                    query={"course_id":str(r['course_id'])},
                    need={
                        "_id":0,
                        "teacher_id":1,
                    }
                )
                if not status == CourseStatusCode.FIND_SUCCESS:
                    raise RuntimeError('error in find ct table')
                for t in teacher:
                    teacherid=t['teacher_id']
                    teacherinfo=getTeacherName(teacherid)
                    r.update({"course_teacher_id":teacherinfo})
                process=_get_process(r["course_id"])
                r.update(process)
                data.append(r)
            search=None
            datas=[]
            search=request.args.get("search")
            if not search == None:
                for d in data:
                    r=re.search(search,d["course_name"])
                    if r:
                        datas.append(d)
                data=datas
            return Response.success(data=data,message='success')
        except Exception as e:
            logger.warning("get method meets error: {}".format(e))
            return Response.failed(data="", message=e)


#***********************************老师批量添加学生到课程*******************************
class CourseAddStudent(Resource):
    """API  /api/v1/course/teacher_center/student_management?course=<course_id> """
    #获取所有学生列表api由用户端负责
    # @login_required
    def get(self):
        try:
            course_id=request.args.get("course")
            data=[]
            raw_students,status=CourseStudent.find(
                query={"course_id":str(course_id)},
                need={"student_id":1}
            )
            if not status == CourseStatusCode.FIND_SUCCESS:
                raise RuntimeError('cant add student '+id+' into course '+course_id+' .unknown reson')
            for item in raw_students:
                data.append(
                    str(item["student_id"])
                )
            return Response.success(data=data,message="success")
        except Exception as e:
            logger.warning("post method meets error: {}".format(e))
            return Response.failed(data="", message=e)
        
        
    """API  /api/v1/teacher_center/course/student_management """
    # @login_required
    def post(self):
        try:
            requestData=request.get_json()
            course_id=requestData["course_id"]
            students=requestData["students_id"]
            
            if course_id == None :
                raise ValueError('user null')
            data,status=CourseInfo.find_by_id(
                _id=course_id,
                need={
                    "course_status":1,
                }
            )
            if not status == CourseStatusCode.FIND_SUCCESS:
                return Response.failed(message="cant find course")
            course_status=data["course_status"]
            if course_status == CourseStatusCode.COURSE_CLOSED:
                return Response.failed(message='course is closed ,cant add students')
            
            data=[]
            
            raw_students,status=CourseStudent.find(
                query={"course_id":ObjectId(str(course_id))},
                need={"student_id":1}
            )
            if  status == CourseStatusCode.FIND_SUCCESS:
                for item in raw_students:
                    data.append(
                        str(item["student_id"])
                    )
                
            for id in students:
                query={"student_id":id,"course_id":course_id}
                _,status=CourseStudent.find(query=query,need={})
                if not status == CourseStatusCode.FIND_SUCCESS:
                    _,status=CourseStudent.insert_one(data={
                        "available":1,
                        "student_id": id,
                        "course_id": course_id,
                        "learn_total_time": 0,
                        "last_view_date": time.strftime("%Y-%m-%d-%H:%M"),
                        "last_view_position": "",
                        "complete":[""],
                        "experiment_resource": [],
                        "course_final_score": 0,
                        "complete_rate": 0,
                        "pass": False
                    })
                    if not status == CourseStatusCode.INSERT_SUCCESS:
                        raise RuntimeError('cant add student '+id+' into course '+course_id+' .unknown reson')
                data.append(id)
            
            return Response.success(data=data,message="add student success")
            
        except Exception as e:
            logger.warning("post method meets error: {}".format(e))
            return Response.failed(data="", message=e)
##内部接口
def addStu(course_id,students):
    try:       
        if course_id == None :
            raise ValueError('user null')
        data,status=CourseInfo.find_by_id(
            _id=course_id,
            need={
                "course_status":1,
            }
        )
        if not status == CourseStatusCode.FIND_SUCCESS:
            raise RuntimeError(f"cant find course")
        # course_status=data["course_status"]
        # if course_status == CourseStatusCode.COURSE_CLOSED:
        #     logger.warning("course {} is closed ,cant add students".format(course_id))
        #     return False
        
        data=[]
        
        raw_students,status=CourseStudent.find(
            query={"course_id":ObjectId(str(course_id))},
            need={"student_id":1}
        )
        if  status == CourseStatusCode.FIND_SUCCESS:
            for item in raw_students:
                data.append(
                    str(item["student_id"])
                )
        added_list = [] 
        failed_list = []
        for id in students:
            query={"student_id":id,"course_id":course_id}
            _,status=CourseStudent.find(query=query,need={})
            if not status == CourseStatusCode.FIND_SUCCESS:
                _,status=CourseStudent.insert_one(data={
                    "available":1,
                    "student_id": id,
                    "course_id": course_id,
                    "learn_total_time": 0,
                    "last_view_date": time.strftime("%Y-%m-%d-%H:%M"),
                    "last_view_position": "",
                    "complete":[""],
                    "experiment_resource": [],
                    "course_final_score": 0,
                    "complete_rate": 0,
                    "pass": False
                })
                if not status == CourseStatusCode.INSERT_SUCCESS:
                    logger.warning(f'cant add student {id} into course {course_id} .unknown reson')
                    failed_list.append(id)
                else:
                    added_list.append(id)
        logger.info(f"func[addStu]:success added : {len(added_list)};added stu list:{added_list};failed stu:{failed_list}")
        return True
        
    except Exception as e:
        logger.warning("post method meets error: {}".format(e))
        return False
#***********************************学生与实验资源*******************************

class SaveExperimentResource(Resource):
    """API /api/v1/course/student_center/chosen_experiment/resource"""
    # @login_required
    def get(self):
        try:
            course_id=request.args.get("course_id")
            if course_id == None:
                raise ValueError("Null course_id")
            chapter_id=None
            # chapter_id=request.args.get("chapter_id")
            student_id=session.get("id")
            if not student_id:
                raise ValueError("请重新登录")
            # student_id = request.args.get("student_id")
            res,status=CourseStudent.find(
                query={
                    "student_id":student_id,
                    "course_id":course_id,
                },
                need={
                    "_id":0,
                    "experiment_resource":1,
                }
            )
            if not status == CourseStatusCode.FIND_SUCCESS:
                return Response.failed(message="cant find in T-cs")
            data=[]
            exps=res[0]["experiment_resource"]
            if len(exps)<=0:
                return Response.success(data=[],message="no expr")
            for exp in exps:
                if chapter_id == None:
                    exp[request.args.get("chapter_id")] = list(exp.values())[0]
                    data.append(exp)
                    continue
                else:
                    if str(chapter_id) in exp.keys():
                        data.append(exp)
            if len(data)>0:
                return Response.success(data=data,message="success")
            return Response.failed(message="cant find")
        except Exception as e:
            logger.warning("post method meets error: {}".format(e))
            return Response.failed(data="", message=e)
    
    # @login_required
    def post(self):
        try:
            course_id=request.json["course_id"]
            if course_id == None:
                raise ValueError("Null course_id")
            chapter_id=request.json["chapter_id"]
            if chapter_id == None:
                raise ValueError("Null chapter_id")
            expr_id=request.json["expriment_resource_id"]
            if expr_id == None:
                raise ValueError("Null expriment_resource_id")
            student_id=session.get("id")
            res,status=CourseStudent.find(
                query={
                    "student_id":student_id,
                    "course_id":course_id,
                },
                need={
                    "_id":0,
                    "experiment_resource":1,
                }
            )
            if not status == CourseStatusCode.FIND_SUCCESS:
                return Response.failed(message="cant find in T-cs")
            newexp=[]
            exps=res[0]["experiment_resource"]
            if len(exps) <=0:
                newexp.append({str(chapter_id):expr_id})
            else:
                changed=False
                for exp in exps:
                    if str(chapter_id) in exp.keys():
                        exp[str(chapter_id)]=expr_id
                        changed=True
                    newexp.append(exp)
                if not changed:
                    newexp.append(
                        {str(chapter_id):expr_id}
                    )
            _,status=CourseStudent.update_many(
                query={
                    "student_id":student_id,
                    "course_id":course_id,
                },
                update={
                    "experiment_resource":newexp
                }
            )
            if not status == CourseStatusCode.UPDATE_SUCCESS:
                return Response.failed(message="cant update in T-cs")
            return Response.success(message="update expr success")
        except Exception as e:
            logger.warning("post method meets error: {}".format(e))
            return Response.failed(data="", message=e)

    # @login_required
    def delete(self):
        try:
            course_id=request.json["course_id"]
            if course_id == None:
                raise ValueError("Null course_id")
            chapter_id=request.json["chapter_id"]
            if chapter_id == None:
                raise ValueError("Null chapter_id")
            student_id=session.get("id")
            if not student_id:
                raise ValueError("请重新登录")
            res,status=CourseStudent.find(
                query={
                    "student_id":student_id,
                    "course_id":course_id,
                },
                need={
                    "_id":0,
                    "experiment_resource":1,
                }
            )
            if not status == CourseStatusCode.FIND_SUCCESS:
                return Response.failed(message="cant find in T-cs")
            newexp=[]
            exps=res[0]["experiment_resource"]
            if len(exps)<=0:
                return Response.failed(message="no expr")
            for exp in exps:
                if not str(chapter_id) in exp.keys():
                    newexp.append(exp)
            _,status=CourseStudent.update_many(
                query={
                    "student_id":student_id,
                    "course_id":course_id,
                },
                update={
                    "experiment_resource":[]
                }
            )
            if not status == CourseStatusCode.UPDATE_SUCCESS:
                return Response.failed(message="cant delete in T-cs")
            return Response.success(message="delete success")
        except Exception as e:
            logger.warning("post method meets error: {}".format(e))
            return Response.failed(data="", message=e)
         
class StudentHWork(Resource):

    # @login_required
    def get(self):
        try:
            course_id=request.args.get("course_id")
            if course_id == None:
                raise ValueError("Null course_id")
            student_id=request.args.get("student_id")
            hw_name = request.args.get("hw_name")
            jsonData = {
                'studentId':student_id,
                'courseId':course_id,
                "hwName":hw_name
            }
            data=list(HandInHworkOrm().find(jsonData))
            for x in data:
                x["_id"] = str(x["_id"])
                res = HandInService().toDoHomework({"_id":x["_id"]})
                x.update(res[0])
            return Response.success(data=data,message="success")
        except Exception as e:
            logger.warning("method meets error: {}".format(e))
            return Response.failed(data="", message=e)
    