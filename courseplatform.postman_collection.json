{"info": {"_postman_id": "5973f8d5-07c0-4c18-a375-a3a93407645f", "name": "courseplatform", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "27087293", "_collection_link": "https://lunar-station-29456.postman.co/workspace/Team-Workspace~17c20072-5668-4d31-80ec-955ec35e8033/collection/27087293-5973f8d5-07c0-4c18-a375-a3a93407645f?action=share&creator=27087293&source=collection_link"}, "item": [{"name": "用户管理", "item": [{"name": "用户登录", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "profile_id", "value": "0001", "type": "text"}, {"key": "password", "value": "1234", "type": "text"}, {"key": "role", "value": "teacher", "type": "text"}]}, "url": {"raw": "http://{{url}}/user/login", "protocol": "http", "host": ["{{url}}"], "path": ["user", "login"]}}, "response": []}, {"name": "用户登出", "request": {"method": "POST", "header": [{"key": "<PERSON><PERSON>", "value": "{{session}}", "type": "text"}], "url": {"raw": "http://{{url}}/user/logout", "protocol": "http", "host": ["{{url}}"], "path": ["user", "logout"]}}, "response": []}, {"name": "获取用户列表", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "<PERSON><PERSON>", "value": "{{session}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://{{url}}/users?role=admin", "protocol": "http", "host": ["{{url}}"], "path": ["users"], "query": [{"key": "role", "value": "admin"}]}}, "response": []}, {"name": "创建用户", "request": {"method": "POST", "header": [{"key": "<PERSON><PERSON>", "value": "{{session}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"profile_id\": \"0001\",\r\n    \"password\": \"1234567\",\r\n    \"user_name\":\"李四\",\r\n    \"nick_name\": \"李二炮\",\r\n    \"role\": \"teacher\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://{{url}}/users", "protocol": "http", "host": ["{{url}}"], "path": ["users"]}}, "response": []}, {"name": "删除用户", "request": {"method": "DELETE", "header": [], "url": {"raw": "http://{{url}}/user", "protocol": "http", "host": ["{{url}}"], "path": ["user"]}}, "response": []}, {"name": "获取单个用户信息", "request": {"method": "GET", "header": [{"key": "<PERSON><PERSON>", "value": "{{session}}", "type": "text"}], "url": {"raw": "http://{{url}}/user/{{user_id}}?role=student", "protocol": "http", "host": ["{{url}}"], "path": ["user", "{{user_id}}"], "query": [{"key": "role", "value": "student"}]}}, "response": []}, {"name": "更新单个用户信息", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"nick_name\": \"张小炮\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://{{url}}/user/{{user_id}}", "protocol": "http", "host": ["{{url}}"], "path": ["user", "{{user_id}}"]}}, "response": []}]}, {"name": "班级管理", "item": [{"name": "获取班级列表", "request": {"method": "GET", "header": [], "url": {"raw": "http://{{url}}/organizations", "protocol": "http", "host": ["{{url}}"], "path": ["organizations"]}}, "response": []}, {"name": "创建班级", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\":\"CS2002\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://{{url}}/organizations", "protocol": "http", "host": ["{{url}}"], "path": ["organizations"]}}, "response": []}, {"name": "获取单个班级信息", "request": {"method": "GET", "header": [], "url": {"raw": "http://{{url}}/organization/{{class_id}}", "protocol": "http", "host": ["{{url}}"], "path": ["organization", "{{class_id}}"]}}, "response": []}, {"name": "删除班级", "request": {"method": "DELETE", "header": [], "url": {"raw": "http://{{url}}/organization/{{class_id}}", "protocol": "http", "host": ["{{url}}"], "path": ["organization", "{{class_id}}"]}}, "response": []}, {"name": "修改班级信息", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\":\"CS2023\",\r\n    \"add_list\":[\"U2021\",\"U2022\"],\r\n    \"del_list\":[\"U2021\"]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://{{url}}/organization/{{class_id}}", "protocol": "http", "host": ["{{url}}"], "path": ["organization", "{{class_id}}"]}}, "response": []}]}, {"name": "课程管理", "item": [{"name": "课程创建", "request": {"method": "POST", "header": [{"key": "<PERSON><PERSON>", "value": "{{session}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"course_name\": \"ceshi1\",\r\n    \"course_id\": \"213213123123\",\r\n    \"course_teacher_name\":[\r\n        \"admin\"\r\n    ],\r\n    \"course_teacher_id\": [\r\n        \"{{user_id}}\"\r\n    ],\r\n    \"course_type\": \"理论课\",\r\n    \"course_tag\": [\r\n        \"ceshi1\"\r\n    ],\r\n    \"course_status\": 21,\r\n    \"course_introduction\": \"ceshi\",\r\n    \"course_purpose\": \"1\",\r\n    \"course_image\": \"url\",\r\n    \"course_organization\": \"ncc\",\r\n    \"open_date\": \"time\",\r\n    \"finish_date\": \"time\",\r\n    \"associate_experment\": [\r\n        \"12231232131\"\r\n    ],\r\n    \"last_update_user\": \"user\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://{{url}}/teacher_center/creat_course", "protocol": "http", "host": ["{{url}}"], "path": ["teacher_center", "creat_course"]}}, "response": []}, {"name": "课程创建获取课程状态", "request": {"method": "GET", "header": [{"key": "<PERSON><PERSON>", "value": "{{session}}", "type": "text"}], "url": {"raw": "http://{{url}}/teacher_center/creat_course", "protocol": "http", "host": ["{{url}}"], "path": ["teacher_center", "creat_course"]}}, "response": []}, {"name": "课程搜索", "request": {"method": "GET", "header": [{"key": "<PERSON><PERSON>", "value": "{{session}}", "type": "text"}], "url": {"raw": "http://{{url}}/course/search?course_name=web", "protocol": "http", "host": ["{{url}}"], "path": ["course", "search"], "query": [{"key": "course_name", "value": "web"}, {"key": "teacher", "value": "<teacher>", "disabled": true}, {"key": "organization", "value": "<organization>", "disabled": true}]}}, "response": []}]}, {"name": "资源管理", "item": [{"name": "资源ID管理", "item": [{"name": "资源查询", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "<PERSON><PERSON>", "value": "{{session}}", "type": "text"}, {"key": "resource_name", "value": "3", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "{\r\n    \"_id\": \"6442429d77a4afd0df3ee7c5\",\r\n    \"\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://{{url}}/resource", "protocol": "http", "host": ["{{url}}"], "path": ["resource"]}}, "response": []}]}, {"name": "文件管理", "item": [{"name": "文件上传", "request": {"method": "POST", "header": [{"key": "<PERSON><PERSON>", "value": "session=e28b7800-6099-4274-ae7b-a7d2a351fbe9.5CidUuBaalvrjjBh9ptkzGx93rQ; Expires=Tue, 25 Apr 2023 10:10:08 GMT; Secure; HttpOnly; Path=/", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "myfile", "type": "file", "src": "/C:/Users/<USER>/Desktop/Label Embedding Online Hashing for Cross-Modal Retrieval.pdf"}]}, "url": {"raw": "http://{{url}}}/api/v1/file", "protocol": "http", "host": ["{{url}}}"], "path": ["api", "v1", "file"]}}, "response": []}]}, {"name": "文件ID管理", "item": []}, {"name": "资源管理", "item": [{"name": "资源查询", "request": {"method": "GET", "header": []}, "response": []}, {"name": "资源上传", "request": {"method": "PUT", "header": [{"key": "<PERSON><PERSON>", "value": "{{session}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"_id\": \"6442429d77a4afd0df3ee7c5\",\r\n    \"id\": \"0335dff574ca52fb88c1c7adc6587e1b\",\r\n    \"resource_type\": \"1\",\r\n    \"add_user\": \"test\",\r\n    \"add_time\": \"1682064029\",\r\n    \"modify_user\": \"test\",\r\n    \"modify_time\": \"1682064029\",\r\n    \"download_count\": \"0\",\r\n    \"resource_size\": \"0\",\r\n    \"resource_version\": \"v4.0\",\r\n    \"resource_description\": \"mysql八股，必不可少\",\r\n    \"resource_name\": \"MySQL必知必会\",\r\n    \"icon_id\": \"test\",\r\n    \"class_id\": \"网络安全\",\r\n    \"labels\": \"书籍\",\r\n    \"url\": \"http://127.0.0.1:50020/api/v1/file/0335dff574ca52fb88c1c7adc6587e1b.pdf\",\r\n    \"file_name\": \"0335dff574ca52fb88c1c7adc6587e1b.pdf\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://{{url}}/resource", "protocol": "http", "host": ["{{url}}"], "path": ["resource"]}}, "response": []}]}]}]}